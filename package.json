{"name": "miccobuy", "version": "0.2.0", "private": true, "type": "module", "workspaces": ["packages/*"], "scripts": {"dev": "npm run dev -w packages/web", "build": "npm run build -w packages/web", "start": "npm run start -w packages/web", "lint": "bunx biome lint --write .", "format": "bunx biome format --write .", "test:chrome-server": "npm run test -w packages/chrome-server", "test:chrome-extension": "npm run test -w packages/chrome-extension", "test": "npm run test:chrome-server && npm run test:chrome-extension", "test:rate-limit": "node --experimental-json-modules test-rate-limit.js", "test:rate-limit:sh": "./test-rate-limit.sh", "translate:zh": "node packages/web/src/messages/translate.mjs zh"}, "dependencies": {"assert": "^2.1.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.12.1", "https-browserify": "^1.0.0", "node-fetch": "^3.3.0", "formdata-node": "^5.0.1", "ts-interface-checker": "^1.0.2", "oauth": "^0.10.2", "next-auth": "^4.24.11", "openai": "^4.95.1", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.4", "util": "^0.12.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "typescript": "^5"}}