#!/bin/bash

# Test script for rate limiting functionality
# This script makes repeated requests to the rate-limited endpoints
# to test if the rate limiting is working correctly.
#
# Usage:
# 1. Start the development server: npm run dev
# 2. Make the script executable: chmod +x test-rate-limit.sh
# 3. Run this script: ./test-rate-limit.sh

# Configuration
BASE_URL="http://localhost:3000"
SEARCH_ENDPOINT="/api/search"
TOTAL_REQUESTS=20
DELAY=0.2  # Delay between requests in seconds

# Get rate limit configuration from adminConfig.mjs
echo "Testing rate limiting for endpoint: $SEARCH_ENDPOINT"
echo "Making $TOTAL_REQUESTS requests"
echo "---------------------------------------------------"

for i in $(seq 1 $TOTAL_REQUESTS); do
  # Make the request and capture the response headers
  response=$(curl -s -w "%{http_code}" -X GET \
    -D - \
    "$BASE_URL$SEARCH_ENDPOINT?q=test+query+$i")
  
  # Extract status code (last 3 characters)
  status_code=${response: -3}
  
  # Extract rate limit headers
  hourly_remaining=$(echo "$response" | grep -i "X-RateLimit-Remaining-Hour" | cut -d' ' -f2 | tr -d '\r')
  daily_remaining=$(echo "$response" | grep -i "X-RateLimit-Remaining-Day" | cut -d' ' -f2 | tr -d '\r')
  
  echo "Request $i/$TOTAL_REQUESTS - Status: $status_code - Remaining: Hourly=$hourly_remaining, Daily=$daily_remaining"
  
  # Check if rate limit is exceeded
  if [ "$status_code" -eq 429 ]; then
    echo "Rate limit exceeded! Test successful."
    break
  fi
  
  # Add a small delay between requests
  sleep $DELAY
done

echo "Test completed!"
