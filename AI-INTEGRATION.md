# AI Integration for Product Analysis

This document explains how to use the AI integration for product analysis in the Miccobuy web application.

## Overview

The AI integration uses OpenAI's GPT-4 API to analyze user search queries and extract relevant product information. The analysis is displayed in the search results page, providing insights about the product requirements, specifications, and other details.

## Setup

1. Create a `.env` file in the root directory of the project (or copy from `.env.example`)
2. Add your OpenAI API key to the `.env` file:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   ```
3. Optionally, customize the user prompt template:
   ```
   USER_PROMPT="Your custom prompt template here"
   ```

## Features

- **AI Model Selection**: Users can select different AI models from the dropdown in the search bar (Auto, Claude 3.7 Sonnet, GPT-4.1)
- **Product Analysis**: The AI analyzes the search query and extracts product information
- **Detailed Insights**: The analysis includes product name, price range, quantity, specifications, and customization requirements
- **Verification Process**: The AI verifies the product requirements and validates the request

## How It Works

1. User enters a search query in the search bar (e.g., "I want to order 100 customizable logo water bottles")
2. User selects an AI model from the dropdown (optional)
3. User clicks the "Source" button to submit the search
4. The AI analyzes the query and extracts relevant information
5. The analysis is displayed in the search results page
6. User can toggle the analysis details to view more information

## Components

- `AIAnalysisPanel.tsx`: Displays the AI analysis results
- `openai.ts`: Handles the OpenAI API integration
- `api/ai-analysis/route.ts`: API endpoint for AI analysis

## Example Queries

Try these example queries to see the AI analysis in action:

- "I want to order 100 customizable logo water bottles"
- "Looking for 5,000 t-shirts with my custom logo"
- "Need 50 wireless headphones with noise cancellation for my team"
- "Searching for bulk order of 1000 promotional pens with company logo"
- "Want to purchase 200 custom hoodies for university event"

## Customizing the AI Prompt

You can customize the AI prompt template in the `.env` file to change how the AI analyzes the search queries. The default prompt template is:

```
Extract product information from the following request and analyze it thoroughly:
- Identify the product name, expected price range (if mentioned), quantity, and any specifications
- Consider customization requirements
- Think step by step about what the user is looking for
- Verify the request against available product categories
- Validate that the request is reasonable and can be fulfilled
```

## Troubleshooting

- If the AI analysis is not working, check that your OpenAI API key is valid and has sufficient credits
- Make sure the `.env` file is in the root directory of the project
- Check the browser console for any error messages
- Verify that the OpenAI API is not being rate limited
