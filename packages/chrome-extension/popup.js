document.addEventListener('DOMContentLoaded', function() {
  const captureBtn = document.getElementById('captureBtn');
  const detectBtn = document.getElementById('detectBtn');
  const status = document.getElementById('status');
  const preview = document.getElementById('preview');
  const resultContainer = document.getElementById('result-container');
  const loading = document.getElementById('loading');
  const resultLink = document.getElementById('result-link');

  // Set backend server URL
  const apiUrl = 'http://localhost:8000/api/similar-products';

  captureBtn.addEventListener('click', function() {
    status.textContent = 'Please select the product area to capture...';

    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: "startCapture"}, function(response) {
        if (chrome.runtime.lastError) {
          status.textContent = 'Unable to connect to the page. Please refresh and try again.';
        } else {
          window.close(); // Close popup to allow user to select area
        }
      });
    });
  });

  detectBtn.addEventListener('click', function() {
    status.textContent = 'Detecting images on the page...';

    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: "detectImages"}, function(response) {
        if (chrome.runtime.lastError) {
          status.textContent = 'Unable to connect to the page. Please refresh and try again.';
        } else {
          window.close(); // Close popup to allow user to interact with the page
        }
      });
    });
  });

  // Listen for messages from the background script
  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === "captureComplete") {
      preview.src = request.imageData;
      preview.style.display = "block";
      status.textContent = "Image captured, analyzing...";
      resultContainer.style.display = "block";
      loading.style.display = "block";

      // Send image to server
      sendImageToServer(request.imageData);
    }
  });

  function sendImageToServer(imageData) {
    // Send base64 image data to server
    fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        image: imageData.split(',')[1] // Remove base64 prefix
      })
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('Server response error');
      }
      return response.json();
    })
    .then(data => {
      loading.style.display = "none";
      if (data.productUrl) {
        const link = document.createElement('a');
        link.href = data.productUrl;
        link.textContent = 'View Similar Products';
        link.target = '_blank';
        link.style.color = '#4285f4';
        resultLink.appendChild(link);

        status.textContent = "Similar products found!";
      } else {
        status.textContent = "No similar products found.";
      }
    })
    .catch(error => {
      loading.style.display = "none";
      status.textContent = "Error: " + error.message;
    });
  }
});