<!DOCTYPE html>
<html>
<head>
  <title>Mi<PERSON>buy</title>
  <style>
    body {
      width: 300px;
      padding: 10px;
      font-family: Arial, sans-serif;
    }
    button {
      width: 100%;
      padding: 8px;
      margin: 5px 0;
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #3367d6;
    }
    #status {
      margin-top: 10px;
      color: #666;
    }
    #preview {
      max-width: 100%;
      margin-top: 10px;
      display: none;
    }
    #result-container {
      margin-top: 15px;
      display: none;
    }
    .loading {
      text-align: center;
      margin: 10px 0;
    }
  </style>
</head>
<body>
  <h2>Miccobuy Product Finder</h2>
  <button id="captureBtn">Capture Product Image</button>
  <button id="detectBtn">Detect Images on Page</button>
  <div id="status"></div>
  <img id="preview" src="" alt="Preview Image">
  <div id="result-container">
    <h3>Similar Products</h3>
    <div id="loading" class="loading">Processing...</div>
    <div id="result-link"></div>
  </div>
  <script src="popup.js"></script>
</body>
</html>