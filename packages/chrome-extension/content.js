(function() {
  let isSelecting = false;
  let startX, startY;
  let selectionBox = null;
  let overlayIcons = [];

  // Listen for messages from popup.js and background.js
  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === "startCapture") {
      startSelectionProcess();
      sendResponse({status: "started"});
    } else if (request.action === "detectImages") {
      detectAndOverlayImages();
      sendResponse({status: "detecting"});
    } else if (request.action === "processCapture") {
      // Process the captured screenshot in content script
      processScreenshot(request.dataUrl, request.rect, sendResponse);
      return true; // Indicate we'll send response asynchronously
    }
  });

  function startSelectionProcess() {
    // Create selection box style and cursor style
    const style = document.createElement('style');
    style.id = 'miccobuy-selection-style';
    style.textContent = `
      .miccobuy-selection {
        position: fixed;
        border: 2px dashed #4285f4;
        background-color: rgba(66, 133, 244, 0.1);
        z-index: 99999;
        pointer-events: none;
      }

      .miccobuy-cursor {
        position: fixed;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid #4285f4;
        background-color: rgba(66, 133, 244, 0.2);
        transform: translate(-50%, -50%);
        pointer-events: none;
        z-index: 100000;
      }
    `;
    document.head.appendChild(style);

    // Create cursor circle
    const cursor = document.createElement('div');
    cursor.className = 'miccobuy-cursor';
    cursor.id = 'miccobuy-cursor';
    document.body.appendChild(cursor);

    // Add mousemove listener for cursor
    document.addEventListener('mousemove', updateCursorPosition);

    // Set cursor to crosshair
    document.body.style.cursor = 'crosshair';

    // Add event listeners
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // Show tooltip
    showToast('Please select the product area to capture');
  }

  function handleMouseDown(e) {
    isSelecting = true;
    startX = e.clientX;
    startY = e.clientY;

    // First, remove any existing overlay icons to avoid interference
    removeOverlayIcons();

    // Create selection box
    selectionBox = document.createElement('div');
    selectionBox.className = 'miccobuy-selection';
    selectionBox.style.left = startX + 'px';
    selectionBox.style.top = startY + 'px';
    document.body.appendChild(selectionBox);

    e.preventDefault();
  }

  function handleMouseMove(e) {
    if (!isSelecting) return;

    // Calculate width and height
    const width = e.clientX - startX;
    const height = e.clientY - startY;

    // Calculate the correct position based on direction of drag
    let newLeft = startX;
    let newTop = startY;
    let newWidth = width;
    let newHeight = height;

    if (width < 0) {
      newLeft = e.clientX;
      newWidth = Math.abs(width);
    }

    if (height < 0) {
      newTop = e.clientY;
      newHeight = Math.abs(height);
    }

    // Update the selection box
    selectionBox.style.left = newLeft + 'px';
    selectionBox.style.top = newTop + 'px';
    selectionBox.style.width = newWidth + 'px';
    selectionBox.style.height = newHeight + 'px';
  }

  // Function to update cursor position
  function updateCursorPosition(e) {
    const cursor = document.getElementById('miccobuy-cursor');
    if (cursor) {
      cursor.style.left = e.clientX + 'px';
      cursor.style.top = e.clientY + 'px';
    }
  }

  function handleMouseUp(e) {
    if (!isSelecting) return;
    isSelecting = false;

    // Restore cursor
    document.body.style.cursor = 'default';

    // Remove event listeners
    document.removeEventListener('mousedown', handleMouseDown);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('mousemove', updateCursorPosition);

    // Get selection area position and size
    const rect = selectionBox.getBoundingClientRect();

    // Remove cursor
    const cursor = document.getElementById('miccobuy-cursor');
    if (cursor && cursor.parentNode) {
      cursor.parentNode.removeChild(cursor);
    }

    // Only proceed if the selection has a meaningful size
    if (rect.width < 10 || rect.height < 10) {
      showToast('Selection too small, please try again with a larger area');
      // Remove selection box
      document.body.removeChild(selectionBox);
      document.head.removeChild(document.getElementById('miccobuy-selection-style'));
      return;
    }

    // Remove selection box
    document.body.removeChild(selectionBox);
    document.head.removeChild(document.getElementById('miccobuy-selection-style'));

    // Capture image
    captureSelectedArea(rect);
  }

  function captureSelectedArea(rect) {
    // Get the window dimensions
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // Get the current zoom level
    const zoomLevel = window.devicePixelRatio || 1;
    console.log('Browser zoom level:', zoomLevel);

    // Calculate the absolute position including scroll offset
    const absoluteRect = {
      left: rect.left + window.scrollX,
      top: rect.top + window.scrollY,
      width: rect.width,
      height: rect.height
    };

    // Log the selection rectangle for debugging
    console.log('Selection rectangle (viewport):', {
      left: rect.left,
      top: rect.top,
      width: rect.width,
      height: rect.height
    });

    console.log('Selection rectangle (absolute):', absoluteRect);
    console.log('Window scroll:', { scrollX: window.scrollX, scrollY: window.scrollY });
    console.log('Window dimensions:', { width: windowWidth, height: windowHeight });

    // Check if selection is within viewport
    const isInViewport =
      rect.left >= 0 &&
      rect.top >= 0 &&
      rect.left + rect.width <= windowWidth &&
      rect.top + rect.height <= windowHeight;

    console.log('Selection is within viewport:', isInViewport);

    // For debugging, show the zoom-adjusted coordinates
    console.log('Zoom-adjusted selection:', {
      left: rect.left * zoomLevel,
      top: rect.top * zoomLevel,
      width: rect.width * zoomLevel,
      height: rect.height * zoomLevel
    });

    // Show a toast with the capture area dimensions
    showToast(`Capturing area: ${Math.round(rect.width)}×${Math.round(rect.height)} pixels`);

    // Use chrome.tabs.captureVisibleTab API to capture the current visible page
    // Then crop the required area in canvas
    chrome.runtime.sendMessage({
      action: "requestCapture",
      rect: absoluteRect
    }, function(response) {
      if (response && response.imageData) {
        // Send image data to background script
        chrome.runtime.sendMessage({
          action: "capturedImage",
          imageData: response.imageData
        });
      } else {
        showToast('Screenshot failed: ' + (response?.error || 'Unknown error'));
      }
    });
  }

  function processScreenshot(dataUrl, rect, sendResponse) {
    try {
      console.log('Processing screenshot with rect:', rect);

      // Get the current zoom level (default to 1 if not available)
      const zoomLevel = window.devicePixelRatio || 1;
      console.log('Browser zoom level:', zoomLevel);

      // Create an image element to load the screenshot
      const img = new Image();
      img.onload = function() {
        try {
          // Log the image dimensions for debugging
          console.log('Screenshot dimensions:', img.width, 'x', img.height);

          // Create canvas to crop the specified area
          const canvas = document.createElement('canvas');
          canvas.width = rect.width;
          canvas.height = rect.height;

          const ctx = canvas.getContext('2d');

          // For debugging, draw a border around the canvas
          ctx.strokeStyle = 'red';
          ctx.lineWidth = 2;
          ctx.strokeRect(0, 0, canvas.width, canvas.height);

          // The captureVisibleTab API captures only what's visible in the viewport
          // So we need to adjust the coordinates relative to the viewport, not the document
          // Also account for browser zoom level
          const viewportRect = {
            left: (rect.left - window.scrollX) * zoomLevel,
            top: (rect.top - window.scrollY) * zoomLevel,
            width: rect.width * zoomLevel,
            height: rect.height * zoomLevel
          };

          console.log('Viewport coordinates for cropping (with zoom):', viewportRect);

          // Draw only the selected portion of the image
          ctx.drawImage(
            img,
            viewportRect.left, viewportRect.top, viewportRect.width, viewportRect.height,
            0, 0, canvas.width, canvas.height
          );

          // Convert the cropped image to dataURL and send it back
          const imageData = canvas.toDataURL('image/png');
          console.log('Processed image size:', canvas.width, 'x', canvas.height);
          sendResponse({imageData: imageData});
        } catch (e) {
          console.error('Canvas error:', e);
          sendResponse({error: 'Canvas error: ' + e.message});
        }
      };

      img.onerror = function() {
        console.error('Image load error');
        sendResponse({error: 'Failed to load screenshot'});
      };

      // Start loading the image
      img.src = dataUrl;
    } catch (e) {
      console.error('Process screenshot error:', e);
      sendResponse({error: 'Process error: ' + e.message});
    }
  }

  function showToast(message) {
    let toast = document.getElementById('miccobuy-toast');
    if (!toast) {
      toast = document.createElement('div');
      toast.id = 'miccobuy-toast';
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        z-index: 10000;
        font-family: Arial, sans-serif;
      `;
      document.body.appendChild(toast);
    }

    toast.textContent = message;

    // Auto-remove after 3 seconds
    setTimeout(function() {
      if (toast.parentNode) {
        document.body.removeChild(toast);
      }
    }, 3000);
  }

  function detectAndOverlayImages() {
    // First, remove any existing overlay icons
    removeOverlayIcons();

    // Add styles for the overlay icon
    addOverlayStyles();

    // Find all images on the page
    const images = document.querySelectorAll('img');
    let count = 0;

    images.forEach(function(img) {
      // Skip small images (likely icons, etc.)
      if (img.width < 50 || img.height < 50) return;

      // Create overlay icon - initially hidden
      const overlay = document.createElement('div');
      overlay.className = 'miccobuy-image-overlay';
      overlay.style.opacity = '0';
      overlay.innerHTML = `
        <div class="miccobuy-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
            <path fill="white" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
          </svg>
        </div>
      `;

      // Position the overlay
      const rect = img.getBoundingClientRect();
      overlay.style.left = (rect.left + window.scrollX) + 'px';
      overlay.style.top = (rect.top + window.scrollY) + 'px';
      overlay.style.width = img.width + 'px';
      overlay.style.height = img.height + 'px';

      // Add mouse events to show/hide overlay
      img.addEventListener('mouseenter', function() {
        overlay.style.opacity = '1';
      });

      img.addEventListener('mouseleave', function() {
        overlay.style.opacity = '0';
      });

      // Add mouse events to the overlay itself
      overlay.addEventListener('mouseenter', function() {
        overlay.style.opacity = '1';
      });

      overlay.addEventListener('mouseleave', function() {
        overlay.style.opacity = '0';
      });

      // Add click event to the overlay
      overlay.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        sendImageToServer(img.src);
      });

      // Add to document
      document.body.appendChild(overlay);
      overlayIcons.push(overlay);
      count++;
    });

    showToast(`Found ${count} images on this page. Hover over any image to see the capture icon.`);
  }

  function addOverlayStyles() {
    // Check if styles already exist
    if (document.getElementById('miccobuy-overlay-styles')) return;

    const style = document.createElement('style');
    style.id = 'miccobuy-overlay-styles';
    style.textContent = `
      .miccobuy-image-overlay {
        position: absolute;
        background-color: transparent;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9000;
        cursor: pointer;
        transition: opacity 0.3s;
        pointer-events: auto;
      }
      .miccobuy-icon {
        background-color: #4285f4;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        opacity: 0;
        transform: scale(0.8);
        transition: opacity 0.2s, transform 0.2s;
      }
      .miccobuy-image-overlay:hover .miccobuy-icon {
        opacity: 1;
        transform: scale(1);
      }
    `;
    document.head.appendChild(style);
  }

  function removeOverlayIcons() {
    overlayIcons.forEach(function(overlay) {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
    });
    overlayIcons = [];
  }

  function sendImageToServer(imageUrl) {
    showToast('Processing image...');

    // For direct image URLs, we can send them directly
    // For data URLs or same-origin images, we can use them directly
    // For cross-origin images, we need to use canvas to get the data

    try {
      // Create an image element to load the image
      const img = new Image();
      img.crossOrigin = 'anonymous'; // Try to request CORS access

      img.onload = function() {
        // Create canvas to get image data
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;

        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);

        try {
          // Try to get image data as data URL
          const imageData = canvas.toDataURL('image/png');

          // Send to background script
          chrome.runtime.sendMessage({
            action: "capturedImage",
            imageData: imageData
          });
        } catch (e) {
          // If we can't get the image data (e.g., tainted canvas), show error
          showToast('Error: Cannot access image data. The image might be protected.');
          console.error('Canvas error:', e);
        }
      };

      img.onerror = function() {
        showToast('Error: Failed to load the image.');
        console.error('Image load error for:', imageUrl);
      };

      // Start loading the image
      img.src = imageUrl;
    } catch (e) {
      showToast('Error processing the image.');
      console.error('Image processing error:', e);
    }
  }
})();