chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === "capturedImage") {
      // Store captured image
      chrome.storage.local.set({capturedImage: request.imageData}, function() {
        // Open result page
        chrome.action.openPopup();

        // Delay sending message to popup
        setTimeout(function() {
          chrome.runtime.sendMessage({
            action: "captureComplete",
            imageData: request.imageData
          });
        }, 500);
      });

      sendResponse({status: "success"});
    }
  });

  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === "requestCapture") {
      chrome.tabs.captureVisibleTab(null, {format: 'png'}, function(dataUrl) {
        if (chrome.runtime.lastError) {
          sendResponse({error: chrome.runtime.lastError.message});
          return;
        }

        // Instead of using Image object, we'll send the dataUrl back to content script
        // for processing, since content scripts have access to DOM APIs like Image
        chrome.tabs.sendMessage(sender.tab.id, {
          action: "processCapture",
          dataUrl: dataUrl,
          rect: request.rect
        }, function(response) {
          if (response && response.imageData) {
            sendResponse({imageData: response.imageData});
          } else {
            sendResponse({error: "Failed to process image in content script"});
          }
        });
      });

      // Return true to indicate we'll send a response asynchronously
      return true;
    }
  });
