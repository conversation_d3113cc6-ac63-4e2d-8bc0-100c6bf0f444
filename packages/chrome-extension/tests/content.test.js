/**
 * Tests for content.js
 */

// Mock DOM elements and methods
document.head = { appendChild: jest.fn() };
document.body = { style: { cursor: '' } };
document.addEventListener = jest.fn();
document.removeEventListener = jest.fn();
document.createElement = jest.fn(tag => {
  if (tag === 'style') {
    return { id: '', textContent: '' };
  }
  if (tag === 'div') {
    return {
      id: '',
      style: { cssText: '' },
      textContent: '',
      parentNode: { removeChild: jest.fn() }
    };
  }
  return {};
});
document.getElementById = jest.fn(() => ({
  parentNode: { removeChild: jest.fn() }
}));

// Mock window properties
window.scrollX = 10;
window.scrollY = 20;

// Mock chrome API
chrome.runtime.onMessage = { addListener: jest.fn() };
chrome.runtime.sendMessage = jest.fn((message, callback) => {
  if (callback) callback({ imageData: 'data:image/png;base64,mockImageData' });
});

// Import the content script
// We need to wrap it in an IIFE to avoid polluting the global scope
const originalScript = require('fs').readFileSync('./content.js', 'utf8');
eval('(function() { ' + originalScript + ' })();');

describe('Content Script', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Message listener for startCapture', () => {
    test('should set up selection process when receiving startCapture message', () => {
      // Get the message listener
      const messageListener = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      const mockSendResponse = jest.fn();

      // Trigger the message listener with startCapture action
      messageListener(
        { action: 'startCapture' },
        {},
        mockSendResponse
      );

      // Check if style was added
      expect(document.createElement).toHaveBeenCalledWith('style');
      expect(document.head.appendChild).toHaveBeenCalled();

      // Check if cursor was changed
      expect(document.body.style.cursor).toBe('crosshair');

      // Check if event listeners were added
      expect(document.addEventListener).toHaveBeenCalledWith('mousedown', expect.any(Function));
      expect(document.addEventListener).toHaveBeenCalledWith('mousemove', expect.any(Function));
      expect(document.addEventListener).toHaveBeenCalledWith('mouseup', expect.any(Function));

      // Check if toast was shown
      expect(document.createElement).toHaveBeenCalledWith('div');
      expect(document.body.appendChild).toHaveBeenCalled();

      // Check if response was sent
      expect(mockSendResponse).toHaveBeenCalledWith({ status: 'started' });
    });
  });

  describe('Selection process', () => {
    test('should handle mouse events for selection', () => {
      // Get the message listener
      const messageListener = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      const mockSendResponse = jest.fn();

      // Trigger the message listener with startCapture action
      messageListener(
        { action: 'startCapture' },
        {},
        mockSendResponse
      );

      // Get the event handlers
      const mouseDownHandler = document.addEventListener.mock.calls.find(
        call => call[0] === 'mousedown'
      )[1];
      const mouseMoveHandler = document.addEventListener.mock.calls.find(
        call => call[0] === 'mousemove'
      )[1];
      const mouseUpHandler = document.addEventListener.mock.calls.find(
        call => call[0] === 'mouseup'
      )[1];

      // Simulate mousedown event
      mouseDownHandler({ clientX: 100, clientY: 100, preventDefault: jest.fn() });

      // Simulate mousemove event
      mouseMoveHandler({ clientX: 200, clientY: 200, preventDefault: jest.fn() });

      // Simulate mouseup event
      mouseUpHandler({ clientX: 200, clientY: 200, preventDefault: jest.fn() });

      // Check if event listeners were removed
      expect(document.removeEventListener).toHaveBeenCalledWith('mousedown', mouseDownHandler);
      expect(document.removeEventListener).toHaveBeenCalledWith('mousemove', mouseMoveHandler);
      expect(document.removeEventListener).toHaveBeenCalledWith('mouseup', mouseUpHandler);

      // Fast-forward timers to trigger the capture
      jest.advanceTimersByTime(500);

      // Check if message was sent to background script
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        {
          action: 'requestCapture',
          rect: {
            left: 100 + window.scrollX,
            top: 100 + window.scrollY,
            width: 100,
            height: 100
          }
        },
        expect.any(Function)
      );

      // Check if captured image was sent to background script
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        {
          action: 'capturedImage',
          imageData: 'data:image/png;base64,mockImageData'
        },
        expect.any(Function)
      );
    });
  });
});
