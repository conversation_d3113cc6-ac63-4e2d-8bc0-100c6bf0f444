/**
 * Tests for popup.js
 */

// Mock DOM elements
document.getElementById = jest.fn(id => {
  const elements = {
    captureBtn: { addEventListener: jest.fn() },
    status: { textContent: '' },
    preview: { src: '', style: { display: 'none' } },
    resultContainer: { style: { display: 'none' } },
    loading: { style: { display: 'none' } },
    resultLink: { appendChild: jest.fn() }
  };
  return elements[id] || null;
});

// Mock fetch
global.fetch = jest.fn();

// Mock chrome API
chrome.tabs.query = jest.fn((_, callback) => callback([{ id: 'tab-id-1' }]));
chrome.tabs.sendMessage = jest.fn((_, __, callback) => callback({ status: 'started' }));
chrome.runtime.onMessage = { addListener: jest.fn() };
window.close = jest.fn();

// Import the popup script
require('../popup.js');

describe('Popup Script', () => {
  let captureBtn, status, preview, resultContainer, loading, resultLink;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Get the mocked elements
    captureBtn = document.getElementById('captureBtn');
    status = document.getElementById('status');
    preview = document.getElementById('preview');
    resultContainer = document.getElementById('resultContainer');
    loading = document.getElementById('loading');
    resultLink = document.getElementById('resultLink');
    
    // Reset chrome.runtime.lastError
    chrome.runtime.lastError = undefined;
  });
  
  describe('Capture button click handler', () => {
    test('should send message to content script and close popup', () => {
      // Trigger the click event
      const clickHandler = captureBtn.addEventListener.mock.calls[0][1];
      clickHandler();
      
      // Check if status was updated
      expect(status.textContent).toBe('请选择要截取的商品区域...');
      
      // Check if message was sent to content script
      expect(chrome.tabs.query).toHaveBeenCalledWith(
        { active: true, currentWindow: true },
        expect.any(Function)
      );
      
      expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(
        'tab-id-1',
        { action: 'startCapture' },
        expect.any(Function)
      );
      
      // Check if popup was closed
      expect(window.close).toHaveBeenCalled();
    });
    
    test('should handle error when sending message to content script', () => {
      // Set up chrome.runtime.lastError
      chrome.runtime.lastError = { message: 'Mock error' };
      
      // Override sendMessage to simulate error
      chrome.tabs.sendMessage = jest.fn((_, __, callback) => callback());
      
      // Trigger the click event
      const clickHandler = captureBtn.addEventListener.mock.calls[0][1];
      clickHandler();
      
      // Check if status was updated with error message
      expect(status.textContent).toBe('无法连接到页面，请刷新后重试。');
      
      // Check that popup was not closed
      expect(window.close).not.toHaveBeenCalled();
    });
  });
  
  describe('Message listener for captureComplete', () => {
    test('should update UI and send image to server', () => {
      // Get the message listener
      const messageListener = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      
      // Setup fetch mock
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          productUrl: 'https://example.com/product'
        })
      });
      
      // Trigger the message listener with captureComplete action
      messageListener({
        action: 'captureComplete',
        imageData: 'data:image/png;base64,mockImageData'
      });
      
      // Check if UI was updated
      expect(preview.src).toBe('data:image/png;base64,mockImageData');
      expect(preview.style.display).toBe('block');
      expect(status.textContent).toBe('图片已捕获，正在分析...');
      expect(resultContainer.style.display).toBe('block');
      expect(loading.style.display).toBe('block');
      
      // Check if fetch was called with correct data
      expect(global.fetch).toHaveBeenCalledWith(
        'https://your-backend-server.com/api/similar-products',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            image: 'mockImageData'
          })
        }
      );
      
      // Return a resolved promise to test the fetch success handler
      return Promise.resolve().then(() => {
        // Create a mock link element
        const mockLink = {
          href: '',
          textContent: '',
          target: '',
          style: { color: '' }
        };
        
        // Mock document.createElement to return our mock link
        document.createElement = jest.fn(() => mockLink);
        
        // Resolve the fetch promise
        return global.fetch.mock.results[0].value
          .then(response => response.json())
          .then(data => {
            // Check if loading was hidden
            expect(loading.style.display).toBe('none');
            
            // Check if link was created and added
            expect(document.createElement).toHaveBeenCalledWith('a');
            expect(mockLink.href).toBe('https://example.com/product');
            expect(mockLink.textContent).toBe('查看相似商品');
            expect(mockLink.target).toBe('_blank');
            expect(mockLink.style.color).toBe('#4285f4');
            expect(resultLink.appendChild).toHaveBeenCalledWith(mockLink);
            
            // Check if status was updated
            expect(status.textContent).toBe('找到相似商品!');
          });
      });
    });
    
    test('should handle server error', () => {
      // Get the message listener
      const messageListener = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      
      // Setup fetch mock to reject
      global.fetch.mockRejectedValueOnce(new Error('Server error'));
      
      // Trigger the message listener with captureComplete action
      messageListener({
        action: 'captureComplete',
        imageData: 'data:image/png;base64,mockImageData'
      });
      
      // Return a resolved promise to test the fetch error handler
      return Promise.resolve().then(() => {
        // Check if loading was hidden
        expect(loading.style.display).toBe('none');
        
        // Check if status was updated with error
        expect(status.textContent).toBe('发生错误: Server error');
      });
    });
  });
});
