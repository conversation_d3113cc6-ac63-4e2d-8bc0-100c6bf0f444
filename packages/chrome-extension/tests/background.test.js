/**
 * Tests for background.js
 */

// Mock the chrome API
const mockSendResponse = jest.fn();
const mockChromeStorageLocalSet = jest.fn((data, callback) => callback());
const mockChromeActionOpenPopup = jest.fn();
const mockChromeRuntimeSendMessage = jest.fn();
const mockChromeTabsCaptureVisibleTab = jest.fn();

// Mock Image and Canvas
global.Image = class {
  constructor() {
    setTimeout(() => {
      if (this.onload) this.onload();
    }, 0);
  }
};

global.document = {
  createElement: jest.fn(() => ({
    getContext: jest.fn(() => ({
      drawImage: jest.fn(),
    })),
    toDataURL: jest.fn(() => 'data:image/png;base64,mockImageData'),
  })),
};

// Setup chrome mocks
chrome.storage.local.set = mockChromeStorageLocalSet;
chrome.action.openPopup = mockChromeActionOpenPopup;
chrome.runtime.sendMessage = mockChromeRuntimeSendMessage;
chrome.tabs.captureVisibleTab = mockChromeTabsCaptureVisibleTab;

// Import the background script
require('../background.js');

describe('Background Script', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('capturedImage message handler', () => {
    test('should store image data and open popup', () => {
      // Trigger the message listener
      chrome.runtime.onMessage.callListeners(
        { action: 'capturedImage', imageData: 'mockImageData' },
        {},
        mockSendResponse
      );

      // Check if storage was called with correct data
      expect(mockChromeStorageLocalSet).toHaveBeenCalledWith(
        { capturedImage: 'mockImageData' },
        expect.any(Function)
      );

      // Check if popup was opened
      expect(mockChromeActionOpenPopup).toHaveBeenCalled();

      // Check if response was sent
      expect(mockSendResponse).toHaveBeenCalledWith({ status: 'success' });

      // Wait for the timeout to complete
      jest.advanceTimersByTime(500);

      // Check if message was sent to popup
      expect(mockChromeRuntimeSendMessage).toHaveBeenCalledWith({
        action: 'captureComplete',
        imageData: 'mockImageData'
      });
    });
  });

  describe('requestCapture message handler', () => {
    test('should capture tab and process image', () => {
      // Setup mock for captureVisibleTab
      mockChromeTabsCaptureVisibleTab.mockImplementation((_, __, callback) => {
        callback('mockDataUrl');
      });

      // Trigger the message listener
      chrome.runtime.onMessage.callListeners(
        { 
          action: 'requestCapture', 
          rect: { left: 10, top: 20, width: 100, height: 200 } 
        },
        {},
        mockSendResponse
      );

      // Check if captureVisibleTab was called
      expect(mockChromeTabsCaptureVisibleTab).toHaveBeenCalled();

      // Check if response was sent with processed image data
      expect(mockSendResponse).toHaveBeenCalledWith({
        imageData: 'data:image/png;base64,mockImageData'
      });
    });

    test('should handle error in captureVisibleTab', () => {
      // Setup mock for captureVisibleTab with error
      chrome.runtime.lastError = { message: 'Mock error' };
      mockChromeTabsCaptureVisibleTab.mockImplementation((_, __, callback) => {
        callback(null);
      });

      // Trigger the message listener
      chrome.runtime.onMessage.callListeners(
        { 
          action: 'requestCapture', 
          rect: { left: 10, top: 20, width: 100, height: 200 } 
        },
        {},
        mockSendResponse
      );

      // Check if response was sent with error
      expect(mockSendResponse).toHaveBeenCalledWith({
        error: 'Mock error'
      });

      // Clean up
      chrome.runtime.lastError = undefined;
    });
  });
});
