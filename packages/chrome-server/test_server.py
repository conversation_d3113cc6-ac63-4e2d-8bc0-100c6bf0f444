import os
import base64
import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from PIL import Image
import io
import json
from server import app, UPLOAD_DIR

# Create a test client
client = TestClient(app)

# Test data
TEST_IMAGE_DATA = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg=="  # 1x1 red pixel

@pytest.fixture(scope="module")
def cleanup_uploads():
    """Clean up test uploads before and after tests"""
    # Setup - ensure the directory exists and is empty
    os.makedirs(UPLOAD_DIR, exist_ok=True)
    for filename in os.listdir(UPLOAD_DIR):
        if filename.startswith("test_"):
            os.remove(os.path.join(UPLOAD_DIR, filename))
    
    yield  # Run the tests
    
    # Teardown - clean up test files
    for filename in os.listdir(UPLOAD_DIR):
        if filename.startswith("test_"):
            os.remove(os.path.join(UPLOAD_DIR, filename))

def test_root_endpoint():
    """Test the root endpoint returns the expected message"""
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "Miccobuy Backend Server is running"}

def test_process_image_endpoint_success(cleanup_uploads):
    """Test the image processing endpoint with valid data"""
    response = client.post(
        "/api/similar-products",
        json={"image": TEST_IMAGE_DATA}
    )
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["status"] == "success"
    assert "imageInfo" in data
    assert "filename" in data["imageInfo"]
    assert "productUrl" in data
    
    # Verify the file was created
    filename = data["imageInfo"]["filename"]
    assert os.path.exists(os.path.join(UPLOAD_DIR, filename))

def test_process_image_endpoint_missing_image():
    """Test the image processing endpoint with missing image data"""
    response = client.post(
        "/api/similar-products",
        json={}
    )
    
    assert response.status_code == 400
    assert response.json()["detail"] == "No image data provided"

def test_process_image_endpoint_invalid_base64():
    """Test the image processing endpoint with invalid base64 data"""
    response = client.post(
        "/api/similar-products",
        json={"image": "not-valid-base64!"}
    )
    
    assert response.status_code == 400
    assert response.json()["detail"] == "Invalid base64 image data"

def test_list_images_endpoint(cleanup_uploads):
    """Test the list images endpoint"""
    # First, create a test image
    img = Image.new('RGB', (100, 100), color='red')
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format='PNG')
    img_byte_arr = img_byte_arr.getvalue()
    
    test_filename = "test_image.png"
    filepath = os.path.join(UPLOAD_DIR, test_filename)
    with open(filepath, 'wb') as f:
        f.write(img_byte_arr)
    
    # Now test the endpoint
    response = client.get("/api/images")
    
    assert response.status_code == 200
    data = response.json()
    
    assert "total" in data
    assert "images" in data
    assert isinstance(data["images"], list)
    
    # Check if our test image is in the list
    found = False
    for img_info in data["images"]:
        if img_info["filename"] == test_filename:
            found = True
            break
    
    assert found, f"Test image {test_filename} not found in the response"

def test_get_image_info_endpoint(cleanup_uploads):
    """Test the get image info endpoint"""
    # First, create a test image
    img = Image.new('RGB', (100, 100), color='red')
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format='PNG')
    img_byte_arr = img_byte_arr.getvalue()
    
    test_filename = "test_image_info.png"
    filepath = os.path.join(UPLOAD_DIR, test_filename)
    with open(filepath, 'wb') as f:
        f.write(img_byte_arr)
    
    # Now test the endpoint
    response = client.get(f"/api/images/{test_filename}")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["filename"] == test_filename
    assert "size" in data
    assert "fileSize" in data
    assert "format" in data
    assert data["format"] == "PNG"

def test_get_image_info_endpoint_not_found():
    """Test the get image info endpoint with a non-existent file"""
    response = client.get("/api/images/non_existent_file.png")
    
    assert response.status_code == 404
    assert response.json()["detail"] == "Image not found"
