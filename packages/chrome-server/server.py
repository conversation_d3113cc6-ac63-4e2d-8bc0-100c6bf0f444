# server.py
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import base64
import os
import json
import time
from datetime import datetime
from PIL import Image
import io
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("server.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("serverChromeExt")

# 创建目录存储图片
UPLOAD_DIR = "uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)

app = FastAPI(title="Miccobuy Backend API", description="接收商品图片并返回相似商品链接")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为你的Chrome扩展ID
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Miccobuy Backend Server is running"}

@app.post("/api/similar-products")
async def process_image(request: Request):
    # Parse request data
    try:
        data = await request.json()
    except json.JSONDecodeError:
        logger.error("Invalid JSON data received")
        raise HTTPException(status_code=400, detail="Invalid JSON data")

    # Check if image data exists
    if "image" not in data:
        logger.error("No image data provided in request")
        raise HTTPException(status_code=400, detail="No image data provided")

    # Decode Base64 image
    image_data = data["image"]
    try:
        # Log the first 50 characters of the image data for debugging
        logger.info(f"Received image data (first 50 chars): {image_data[:50]}...")
        image_bytes = base64.b64decode(image_data)
    except Exception as e:
        logger.error(f"Base64 decoding error: {str(e)}")
        raise HTTPException(status_code=400, detail="Invalid base64 image data")

    # Generate unique filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"product_{timestamp}_{int(time.time() * 1000) % 1000}.png"
    filepath = os.path.join(UPLOAD_DIR, filename)

    # Save image
    try:
        # Use PIL to open the image (this validates the image is valid)
        image = Image.open(io.BytesIO(image_bytes))
        image.save(filepath)

        # Get image information
        width, height = image.size
        format = image.format

        logger.info(f"Image saved: {filename} (format: {format}, size: {width}x{height})")
    except Exception as e:
        logger.error(f"Error saving image: {str(e)}")
        raise HTTPException(status_code=500, detail="Error saving image")

    # Simulate processing and analyzing the image (here we just return example product links)
    # In a real application, this would call product recognition and recommendation algorithms

    # Build response
    response_data = {
        "status": "success",
        "message": "Image processed successfully",
        "imageInfo": {
            "filename": filename,
            "size": f"{width}x{height}",
            "format": format
        },
        "productUrl": "https://www.example.com/similar-products?ref=" + filename
    }

    return JSONResponse(content=response_data)

@app.get("/api/images/{filename}")
async def get_image_info(filename: str):
    filepath = os.path.join(UPLOAD_DIR, filename)
    if not os.path.exists(filepath):
        raise HTTPException(status_code=404, detail="Image not found")

    # 获取文件信息
    file_size = os.path.getsize(filepath)
    created_time = os.path.getctime(filepath)

    try:
        # 使用PIL获取图片详细信息
        with Image.open(filepath) as img:
            width, height = img.size
            format = img.format
            mode = img.mode
    except Exception as e:
        logger.error(f"读取图片信息错误: {str(e)}")
        raise HTTPException(status_code=500, detail="Error reading image information")

    return {
        "filename": filename,
        "path": filepath,
        "size": f"{width}x{height}",
        "fileSize": f"{file_size / 1024:.2f} KB",
        "format": format,
        "mode": mode,
        "created": datetime.fromtimestamp(created_time).strftime("%Y-%m-%d %H:%M:%S")
    }

@app.get("/api/images")
async def list_images():
    images = []
    for filename in os.listdir(UPLOAD_DIR):
        if filename.endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
            filepath = os.path.join(UPLOAD_DIR, filename)
            file_size = os.path.getsize(filepath)
            created_time = os.path.getctime(filepath)

            images.append({
                "filename": filename,
                "url": f"/api/images/{filename}",
                "fileSize": f"{file_size / 1024:.2f} KB",
                "created": datetime.fromtimestamp(created_time).strftime("%Y-%m-%d %H:%M:%S")
            })

    return {"total": len(images), "images": images}

# 启动服务器
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Miccobuy Backend Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host address")
    parser.add_argument("--port", type=int, default=8000, help="Port number")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")

    args = parser.parse_args()

    logger.info(f"启动服务器: host={args.host}, port={args.port}, debug={args.debug}")

    uvicorn.run("server:app", host=args.host, port=args.port, reload=args.debug)