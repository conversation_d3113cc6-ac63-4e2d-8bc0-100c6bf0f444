<!DOCTYPE html>
<html>
<head>
  <title>Favicon Preview</title>
  <link rel="icon" href="/images/miccobuy-logo.png" type="image/png">
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .preview {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin: 20px 0;
    }
    .icon-preview {
      display: flex;
      flex-direction: column;
      align-items: center;
      border: 1px solid #eee;
      padding: 20px;
      border-radius: 8px;
    }
    .icon-preview img {
      margin-bottom: 10px;
    }
    h1 {
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    p {
      color: #666;
    }
  </style>
</head>
<body>
  <h1>Miccobuy Favicon Preview</h1>
  <p>This page shows the different favicon versions created for Miccobuy.</p>
  
  <div class="preview">
    <div class="icon-preview">
      <img src="/images/miccobuy-logo.png" width="32" height="32" alt="Favicon PNG">
      <span>miccobuy-logo.png</span>
    </div>
    <div class="icon-preview">
      <img src="/favicon.svg" width="32" height="32" alt="Favicon SVG">
      <span>favicon.svg</span>
    </div>
    <div class="icon-preview">
      <img src="/favicon-transparent.svg" width="32" height="32" alt="Favicon Transparent SVG">
      <span>favicon-transparent.svg</span>
    </div>
  </div>
  
  <p>
    To use these favicons in your website, add the following code to your HTML head:
  </p>
  
  <pre><code>&lt;link rel="icon" href="/images/miccobuy-logo.png" type="image/png"&gt;
&lt;link rel="icon" href="/favicon.svg" type="image/svg+xml"&gt;
&lt;link rel="apple-touch-icon" href="/images/miccobuy-logo.png"&gt;</code></pre>
  
  <p>
    For Next.js applications, these have been configured in the metadata object in layout.tsx.
  </p>
</body>
</html>
