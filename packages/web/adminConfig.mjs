/**
 * Admin Configuration for Miccobuy Application
 * This file contains centralized configuration settings that can be adjusted by administrators.
 */

const adminConfig = {
  /**
   * Currency Exchange Rate Configuration
   */
  currency: {
    // Exchange rates (base: CNY)
    exchangeRates: {
      CNY_EUR: 0.1875, // 1 CNY = 0.13 EUR
      CNY_USD: 0.17, // 1 CNY = 0.14 USD
    },

    // Display settings
    display: {
      showOnlyCNY: false,
      showOnlyEUR: true,
      showBoth: false,

      // Format settings
      format: {
        EUR: {
          symbol: '€',
          position: 'before', // 'before' or 'after'
          decimalPlaces: 2,
        },
        CNY: {
          symbol: '¥',
          position: 'before',
          decimalPlaces: 2,
        }
      }
    }
  },

  /**
   * Rate Limiting Configuration
   */
  rateLimiting: {
    // Endpoints that should be rate limited
    endpoints: [
      '/api/ai-analysis',
      '/search',
      '/api/search',
    ],

    // Rate limits
    limits: {
      hourly: 200,  // 200 requests per hour (increased from 60)
      daily: 1000,  // 1000 requests per day (increased from 100)
    },

    // Endpoint-specific rate limits (overrides the default limits above)
    endpointLimits: {
      '/api/search': {
        hourly: 500,  // 500 requests per hour for search API
        daily: 2000,  // 2000 requests per day for search API
      },
      '/api/ai-analysis': {
        hourly: 60,   // Keep AI analysis at 60 requests per hour
        daily: 100,   // Keep AI analysis at 100 requests per day
      }
    },

    // Time windows for rate limiting
    timeWindows: {
      hourly: '1 h',
      daily: '24 h',
    },

    // Prefix for Redis keys
    redisPrefix: {
      hourly: 'ratelimit:hourly',
      daily: 'ratelimit:daily',
    },

    // Error messages for rate limiting
    errorMessages: {
      hourly: 'Rate limit exceeded. You can only make {limit} search requests per hour.',
      daily: 'Rate limit exceeded. You can only make {limit} search requests per day.',
      bot: 'Rate limit exceeded. Too many requests from bot.',
    },

    // Toast notification thresholds (percentage of limit remaining)
    toastThresholds: {
      hourly: 20,  // Show toast when less than 20% of hourly limit remains
      daily: 20,   // Show toast when less than 20% of daily limit remains
    },
  },

  /**
   * Search Configuration
   */
  search: {
    // Default search model
    defaultModel: 'gpt-4o mini',

    // Available search models
    models: [
      // Temporarily commented out options as requested
      // { id: 'auto', name: 'No AI (best)', description: 'Not using any AI model' },
      // { id: 'claude-3-7-sonnet', name: 'Claude 3.7 Sonnet', description: 'Advanced model with excellent reasoning' },
      { id: 'gpt-4o mini', name: 'GPT-4o mini (free)', description: 'Free tier model with good performance' },
      // { id: 'gpt-4o', name: 'GPT-4o', description: 'Premium model with best performance' },
    ],

    // Analysis panel text configurations
    analysisPanel: {
      // Text for No AI search
      noAI: {
        title: "Search Analysis",
        userRequestPrefix: "Searching for",
        productInfoTitle: "Search Information",
        thinking: [
          "No AI analysis performed as per user selection.",
          "Search performed using keywords only."
        ],
        verification: ["Search performed using keywords only."],
        validation: ["No AI validation performed."]
      },

      // Text for image search
      imageSearch: {
        title: "Image Analysis",
        userRequestPrefix: "Analyzing image for",
        productInfoTitle: "Image Search Information",
        thinking: [
          "User has uploaded an image for research, I'm analysing and searching the product within our partners to find the best supplier.",
          "In order to find the best adapted product as described in the image, I'm analysing the image details.",
          "Searching for similar products across our partner marketplaces..."
        ],
        verification: [],
        validation: []
      }
    }
  },

  /**
   * Taobao API Configuration
   */
  taobaoApi: {
    baseUrl: 'https://api-gw.onebound.cn/taobao/',
    key: 't3596163167',
    secret: '3167863b',
    defaultLang: 'zh-CN',
    // Enable/disable specific API endpoints
    enabled: {
      itemSearch: false,
      itemGet: true,
      itemSearchImg: false,
    },
    endpoints: {
      itemSearch: 'item_search/',
      itemGet: 'item_get/',
      itemSearchImg: 'item_search_img/',
    },
    cache: {
      // Cache expiration time in seconds
      itemSearch: 3600, // 1 hour
      itemGet: 86400,   // 24 hours
    },
  },

  /**
   * 1688 API Configuration
   */
  api1688: {
    baseUrl: 'https://api-gw.onebound.cn/1688/',
    key: 't3596163167',
    secret: '3167863b',
    defaultLang: 'zh-CN',
    // Enable/disable specific API endpoints
    enabled: {
      itemSearch: true,
      itemGet: true,
      itemSearchImg: true,
    },
    endpoints: {
      itemSearch: 'item_search/',
      itemGet: 'item_get/',
      itemSearchImg: 'item_search_img/',
    },
    cache: {
      // Cache expiration time in seconds
      itemSearch: 3600, // 1 hour
      itemGet: 86400,   // 24 hours
    },
  },

  /**
   * Alibaba API Configuration
   */
  alibabaApi: {
    baseUrl: 'https://api-gw.onebound.cn/alibaba/',
    key: 't3596163167',
    secret: '3167863b',
    defaultLang: 'zh-CN',
    // Enable/disable specific API endpoints
    enabled: {
      itemSearch: false,
      itemGet: true,
      itemSearchImg: false, // Enable image search for Alibaba
    },
    endpoints: {
      itemSearch: 'item_search/',
      itemGet: 'item_get/',
      itemSearchImg: 'item_search_img/',
    },
    cache: {
      // Cache expiration time in seconds
      itemSearch: 3600, // 1 hour
      itemGet: 86400,   // 24 hours
    },
  },

  /**
   * User Plans Configuration
   */
  userPlans: {
    free: {
      name: 'Free',
      features: [
        'Basic product sourcing',
        'Limited searches per day',
        'Standard support',
      ],
    },
    pro: {
      name: 'Pro',
      price: 99,
      features: [
        'Unlimited product sourcing',
        'Priority support',
        'Dedicated agent',
        'Custom product requests',
      ],
    },
  },

  /**
   * AWS S3 Configuration
   */
  s3: {
    region: process.env.AWS_REGION,
    bucket: process.env.AWS_S3_BUCKET,
    imagePrefix: 'search-images',
    publicUrlFormat: 'https://{bucket}.s3.{region}.amazonaws.com/{key}',
  },
};

export default adminConfig;
