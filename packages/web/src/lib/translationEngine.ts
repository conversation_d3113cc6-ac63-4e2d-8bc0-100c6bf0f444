export class TranslationsEngine {
  #worker: Worker | null;
  isReadyPromise: Promise<unknown>;
  #isReadyResolve: ((str?: string) => void) | null = console.log;
  #isReadyReject: ((error: Error | ErrorEvent) => void) | null = console.error;
  #currentTranslationResolve: ((str?: string) => void) | null = null;
  #currentTranslationReject: ((error: Error | ErrorEvent) => void) | null = null;

  /**
   * Constructs a new Translator instance.
   *
   * @param {string} sourceLanguage - The source language code (e.g., 'es').
   * @param {string} targetLanguage - The target language code (e.g., 'fr').
   * @param {string} modelBasePath - Path to the model files (default: './models')
   * @param {string} wasmPath - Path to the WASM file (default: './generated/bergamot-translator.wasm')
   */
  constructor(
    sourceLanguage: string,
    targetLanguage: string,
    modelBasePath = 'https://micco-cashback.s3.eu-west-3.amazonaws.com/assets/models',
    wasmPath = '/generated/bergamot-translator.wasm',
  ) {
    // const workerUrl = URL.createObjectURL(workerBlob);
    this.#worker = new Worker("/generated/worker.js");

    this.#worker.onmessage = (event) => this.#handleMessage(event);
    this.#worker.onerror = (error) => this.#handleError(error);
    
    // Initialize the worker
    this.isReadyPromise = this.#initWorker(sourceLanguage, targetLanguage, modelBasePath, wasmPath);
  }

  /**
   * Private method to initialize the worker by fetching necessary files and sending the initialization message.
   *
   * @returns {Promise<void>}
   */
  async #initWorker(sourceLanguage: string, targetLanguage: string, modelBasePath: string, wasmPath: string) {
    try {
      const wasmBuffer = await this.#fetchBinary(wasmPath);
      const translationModelPayloads = await this.#prepareTranslationModelPayloads(
        sourceLanguage,
        targetLanguage,
        modelBasePath
      );

      // Return a promise that resolves or rejects based on worker messages
      const isReadyPromise = new Promise((resolve, reject) => {
        this.#isReadyResolve = resolve;
        this.#isReadyReject = reject;
      });

      this.#worker?.postMessage({
        type: "initialize",
        enginePayload: {
          bergamotWasmArrayBuffer: wasmBuffer,
          translationModelPayloads,
        },
      }, [wasmBuffer]); // Transfer the buffer to avoid copying

      return isReadyPromise;
    } catch (error) {
      throw new Error(`
        🚨 Failed to load one or more files required for translation 🚨

        ${error}

        ⏩ NEXT STEPS ⏩

        Please ensure all model files are correctly placed in the specified directories.
      `);
    }
  }

  /**
   * Fetches binary data from a URL.
   * 
   * @param {string} url - URL to fetch from
   * @returns {Promise<ArrayBuffer>} - The binary data as an ArrayBuffer
   */
  async #fetchBinary(url: string) {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch ${url}: ${response.status} ${response.statusText}`);
    }
    return await response.arrayBuffer();
  }

  /**
   * Private helper method to prepare the language model files.
   *
   * @param {string} sourceLanguage - The source language code.
   * @param {string} targetLanguage - The target language code.
   * @param {string} modelBasePath - Base path for the model files.
   * @returns {Promise<Array>} - An array of translation model payloads.
   */
  async #prepareTranslationModelPayloads(sourceLanguage: string, targetLanguage: string, modelBasePath: string) {
    const PIVOT = "en";
    let translationModelPayloadPromises;

    if (sourceLanguage === PIVOT || targetLanguage === PIVOT) {
      translationModelPayloadPromises = [
        this.#loadTranslationModelPayload(sourceLanguage, targetLanguage, modelBasePath),
      ];
    } else {
      translationModelPayloadPromises = [
        this.#loadTranslationModelPayload(sourceLanguage, PIVOT, modelBasePath),
        this.#loadTranslationModelPayload(PIVOT, targetLanguage, modelBasePath),
      ];
    }

    return Promise.all(translationModelPayloadPromises);
  }

  /**
   * Private helper method to load language model files.
   *
   * @param {string} sourceLanguage - The source language code.
   * @param {string} targetLanguage - The target language code.
   * @param {string} modelBasePath - Base path for the model files.
   * @returns {Promise<Object>} - An object containing the data required to construct a translation model.
   */
  async #loadTranslationModelPayload(sourceLanguage: string, targetLanguage: string, modelBasePath: string) {
    const langPairDirectory = `${modelBasePath}/${sourceLanguage}${targetLanguage}`;

    const lexPath = `${langPairDirectory}/lex.50.50.${sourceLanguage}${targetLanguage}.s2t.bin`;
    const modelPath = `${langPairDirectory}/model.${sourceLanguage}${targetLanguage}.intgemm.alphas.bin`;
    const vocabPath = `${langPairDirectory}/vocab.${sourceLanguage}${targetLanguage}.spm`;

    const [lexBuffer, modelBuffer, vocabBuffer] = await Promise.all([
      this.#fetchBinary(lexPath),
      this.#fetchBinary(modelPath),
      this.#fetchBinary(vocabPath),
    ]);

    return {
      sourceLanguage,
      targetLanguage,
      languageModelFiles: {
        model: { buffer: modelBuffer },
        lex: { buffer: lexBuffer },
        vocab: { buffer: vocabBuffer },
      },
    };
  }

  /**
   * Private method to handle incoming messages from the worker.
   *
   * @param {MessageEvent} event - The message event from the worker.
   */
  #handleMessage(event: MessageEvent) {
    const { data } = event;

    switch (data.type) {
      case "initialization-success": {
        this.#isReadyResolve?.();
        break;
      }
      case "initialization-error": {
        this.#isReadyReject?.(new Error(data.error));
        break;
      }
      case "translation-response": {
        if (this.#currentTranslationResolve) {
          this.#currentTranslationResolve(data.targetText);
          this.#clearCurrentTranslation();
        }
        break;
      }
      case "translation-error": {
        if (this.#currentTranslationReject) {
          this.#currentTranslationReject(new Error(data.error.message));
          this.#clearCurrentTranslation();
        }
        break;
      }
      default: {
        console.warn(`Unknown message type: ${data.type}`);
      }
    }
  }

  /**
   * Private method to handle errors from the worker.
   *
   * @param {ErrorEvent} error - The error event from the worker.
   */
  #handleError(error: ErrorEvent) {
    if (this.#isReadyReject) {
      this.#isReadyReject(error);
    }
    if (this.#currentTranslationReject) {
      this.#currentTranslationReject(error);
      this.#clearCurrentTranslation();
    }
  }

  /**
   * Translates the given source text.
   *
   * @param {string} sourceText - The text to translate.
   * @param {boolean} [isHTML=false] - Indicates if the source text is HTML.
   * @returns {Promise<string>} - The translated text.
   */
  async translate(sourceText: string, isHTML = false): Promise<string> {
    await this.isReadyPromise;

    return new Promise((resolve, reject) => {
      this.#currentTranslationResolve = resolve;
      this.#currentTranslationReject = reject;

      // Send translation request
      this.#worker?.postMessage({
        type: "translation-request",
        sourceText,
        isHTML,
      });
    });
  }

  /**
   * Clears the current translation promise handlers.
   */
  #clearCurrentTranslation() {
    this.#currentTranslationResolve = null;
    this.#currentTranslationReject = null;
  }

  /**
   * Terminates the worker and cleans up resources.
   */
  terminate() {
    if (this.#worker) {
      this.#clearCurrentTranslation();
      this.#worker.terminate();
      this.#worker.onmessage = null;
      this.#worker.onerror = null;
      this.#worker = null;
    }
  }
}