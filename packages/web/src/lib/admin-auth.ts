import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { jwtVerify } from 'jose';

// JWT secret for admin sessions
const JWT_SECRET = new TextEncoder().encode(
  process.env.NEXTAUTH_SECRET || 'admin-secret-key-change-in-production'
);

// Admin session interface
interface AdminSession {
  username: string;
  role: string;
  loginTime: number;
}

// Check if the current user is an admin
export async function isAdmin(): Promise<boolean> {
  // Only run on server side
  if (typeof window !== 'undefined') {
    return false;
  }

  try {
    const cookieStore = cookies();
    const token = cookieStore.get('admin-token')?.value;

    if (!token) {
      return false;
    }

    // Verify JWT token
    await jwtVerify(token, JWT_SECRET);
    return true;
  } catch (error) {
    return false;
  }
}

// Get admin session or null if not admin
export async function getAdminSession(): Promise<AdminSession | null> {
  // Only run on server side
  if (typeof window !== 'undefined') {
    return null;
  }

  try {
    const cookieStore = cookies();
    const token = cookieStore.get('admin-token')?.value;

    if (!token) {
      return null;
    }

    // Verify JWT token
    const { payload } = await jwtVerify(token, JWT_SECRET);

    return {
      username: payload.username as string,
      role: payload.role as string,
      loginTime: payload.loginTime as number
    };
  } catch (error) {
    return null;
  }
}

// Middleware function to protect admin routes
export async function requireAdmin() {
  const adminSession = await getAdminSession();

  if (!adminSession) {
    return NextResponse.json(
      { error: 'Unauthorized. Admin access required.' },
      { status: 403 }
    );
  }

  return null; // No error, user is admin
}

// Helper function to check admin access in API routes
export async function checkAdminAccess(): Promise<{ isAdmin: boolean; session: AdminSession | null; error?: NextResponse }> {
  // Only run on server side
  if (typeof window !== 'undefined') {
    return {
      isAdmin: false,
      session: null,
      error: NextResponse.json(
        { error: 'Server-side only function' },
        { status: 500 }
      )
    };
  }

  const session = await getAdminSession();

  if (!session) {
    return {
      isAdmin: false,
      session: null,
      error: NextResponse.json(
        { error: 'Admin authentication required' },
        { status: 401 }
      )
    };
  }

  return {
    isAdmin: true,
    session
  };
}
