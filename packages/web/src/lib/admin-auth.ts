import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { NextResponse } from 'next/server';

// Admin email - only this user can access admin features
const ADMIN_EMAIL = '<EMAIL>';

// Check if the current user is an admin
export async function isAdmin(): Promise<boolean> {
  try {
    const session = await getServerSession(authOptions);
    return session?.user?.email === ADMIN_EMAIL;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// Get admin session or null if not admin
export async function getAdminSession() {
  try {
    const session = await getServerSession(authOptions);
    if (session?.user?.email === ADMIN_EMAIL) {
      return session;
    }
    return null;
  } catch (error) {
    console.error('Error getting admin session:', error);
    return null;
  }
}

// Middleware function to protect admin routes
export async function requireAdmin() {
  const adminSession = await getAdminSession();
  
  if (!adminSession) {
    return NextResponse.json(
      { error: 'Unauthorized. Admin access required.' },
      { status: 403 }
    );
  }
  
  return null; // No error, user is admin
}

// Helper function to check admin access in API routes
export async function checkAdminAccess(): Promise<{ isAdmin: boolean; session: any; error?: NextResponse }> {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return {
      isAdmin: false,
      session: null,
      error: NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    };
  }
  
  if (session.user?.email !== ADMIN_EMAIL) {
    return {
      isAdmin: false,
      session,
      error: NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    };
  }
  
  return {
    isAdmin: true,
    session
  };
}
