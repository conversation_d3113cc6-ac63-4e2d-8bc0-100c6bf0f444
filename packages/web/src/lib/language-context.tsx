'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useLocale } from 'next-intl';
import { defaultLocale, setLocaleInCookie } from '@/i18n';
import { getLocaleFromStorage, setLocaleInStorage } from '@/utils/storage';

interface LanguageContextType {
  language: string;
  setLanguage: (lang: string) => void;
  isLoading: boolean;
}

const LanguageContext = createContext<LanguageContextType>({
  language: typeof window === 'undefined' ? defaultLocale : getLocaleFromStorage() ?? defaultLocale,
  setLanguage: () => {},
  isLoading: true,
});

export function LanguageProvider({ children }: { children: ReactNode }) {
  const localeFromIntl = useLocale();
  const [language, setLanguageState] = useState<string>(localeFromIntl);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize language from localStorage or next-intl on client side
  useEffect(() => {
    const storedLocale = getLocaleFromStorage(false);
    if (storedLocale) {
      setLanguageState(storedLocale);
    } else {
      setLanguageState(localeFromIntl);
      setLocaleInStorage(localeFromIntl);
    }
    setIsLoading(false);
  }, [localeFromIntl]);

  // Function to update language
  const setLanguage = (lang: string) => {
    setLanguageState(lang);
    setLocaleInStorage(lang);
    setLocaleInCookie(lang);
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, isLoading }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  return useContext(LanguageContext);
}

export function isChinese (language: string) {
  return language.startsWith("zh");
}
