// Server-side only imports
let connectToDatabase: any;
let Collections: any;
let ObjectId: any;

// Dynamically import MongoDB only on server side
if (typeof window === 'undefined') {
  try {
    const dbModule = require('./database');
    const mongoModule = require('mongodb');
    connectToDatabase = dbModule.connectToDatabase;
    Collections = dbModule.Collections;
    ObjectId = mongoModule.ObjectId;
  } catch (error) {
    console.error('Failed to import MongoDB modules:', error);
  }
}

// Interface for API request logging
export interface ApiRequestLog {
  _id?: ObjectId;
  timestamp: Date;
  apiType: 'openai' | '1688' | 'taobao' | 'alibaba';
  endpoint: string;
  method: string;
  requestData?: any;
  responseData?: any;
  status: 'success' | 'error' | 'pending';
  statusCode?: number;
  errorMessage?: string;
  responseTime?: number; // in milliseconds
  userId?: string | null;
  userEmail?: string | null;
  ipAddress?: string;
  userAgent?: string;
}

// Interface for user activity logging
export interface UserActivityLog {
  _id?: ObjectId;
  timestamp: Date;
  userId?: string | null;
  userEmail?: string | null;
  activityType: 'page_visit' | 'search' | 'product_view' | 'checkout' | 'order_created';
  url: string;
  referrer?: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: any; // Additional data specific to the activity
}

// Interface for Stripe event logging
export interface StripeEventLog {
  _id?: ObjectId;
  timestamp: Date;
  eventType: string; // e.g., 'checkout.session.completed', 'payment_intent.succeeded'
  stripeEventId: string;
  status: 'success' | 'failed' | 'pending';
  amount?: number;
  currency?: string;
  userId?: string | null;
  userEmail?: string | null;
  orderId?: string;
  sessionId?: string;
  paymentIntentId?: string;
  errorMessage?: string;
  metadata?: any;
}

// Log API requests
export async function logApiRequest(logData: Omit<ApiRequestLog, '_id' | 'timestamp'>): Promise<boolean> {
  // Only run on server side
  if (typeof window !== 'undefined' || !connectToDatabase || !Collections) {
    return false;
  }

  try {
    const { db } = await connectToDatabase();

    const apiLog: ApiRequestLog = {
      ...logData,
      timestamp: new Date()
    };

    await db.collection(Collections.API_REQUESTS).insertOne(apiLog);
    return true;
  } catch (error) {
    console.error('Failed to log API request:', error);
    return false;
  }
}

// Log user activities
export async function logUserActivity(logData: Omit<UserActivityLog, '_id' | 'timestamp'>): Promise<boolean> {
  // Only run on server side
  if (typeof window !== 'undefined' || !connectToDatabase || !Collections) {
    return false;
  }

  try {
    const { db } = await connectToDatabase();

    const activityLog: UserActivityLog = {
      ...logData,
      timestamp: new Date()
    };

    await db.collection(Collections.USER_ACTIVITIES).insertOne(activityLog);
    return true;
  } catch (error) {
    console.error('Failed to log user activity:', error);
    return false;
  }
}

// Log Stripe events
export async function logStripeEvent(logData: Omit<StripeEventLog, '_id' | 'timestamp'>): Promise<boolean> {
  // Only run on server side
  if (typeof window !== 'undefined' || !connectToDatabase || !Collections) {
    return false;
  }

  try {
    const { db } = await connectToDatabase();

    const stripeLog: StripeEventLog = {
      ...logData,
      timestamp: new Date()
    };

    await db.collection(Collections.STRIPE_EVENTS).insertOne(stripeLog);
    return true;
  } catch (error) {
    console.error('Failed to log Stripe event:', error);
    return false;
  }
}

// Get API request logs with pagination and filtering
export async function getApiRequestLogs(
  page: number = 1,
  limit: number = 50,
  filters?: {
    apiType?: string;
    status?: string;
    startDate?: Date;
    endDate?: Date;
    userId?: string;
  }
): Promise<{ logs: ApiRequestLog[]; total: number }> {
  // Only run on server side
  if (typeof window !== 'undefined' || !connectToDatabase || !Collections) {
    return { logs: [], total: 0 };
  }

  try {
    const { db } = await connectToDatabase();

    const query: any = {};

    if (filters?.apiType) query.apiType = filters.apiType;
    if (filters?.status) query.status = filters.status;
    if (filters?.userId) query.userId = filters.userId;
    if (filters?.startDate || filters?.endDate) {
      query.timestamp = {};
      if (filters.startDate) query.timestamp.$gte = filters.startDate;
      if (filters.endDate) query.timestamp.$lte = filters.endDate;
    }

    const skip = (page - 1) * limit;

    const [logs, total] = await Promise.all([
      db.collection(Collections.API_REQUESTS)
        .find(query)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .toArray(),
      db.collection(Collections.API_REQUESTS).countDocuments(query)
    ]);

    return { logs: logs as ApiRequestLog[], total };
  } catch (error) {
    console.error('Failed to get API request logs:', error);
    return { logs: [], total: 0 };
  }
}

// Get user activity logs with pagination and filtering
export async function getUserActivityLogs(
  page: number = 1,
  limit: number = 50,
  filters?: {
    activityType?: string;
    startDate?: Date;
    endDate?: Date;
    userId?: string;
  }
): Promise<{ logs: UserActivityLog[]; total: number }> {
  // Only run on server side
  if (typeof window !== 'undefined' || !connectToDatabase || !Collections) {
    return { logs: [], total: 0 };
  }

  try {
    const { db } = await connectToDatabase();

    const query: any = {};

    if (filters?.activityType) query.activityType = filters.activityType;
    if (filters?.userId) query.userId = filters.userId;
    if (filters?.startDate || filters?.endDate) {
      query.timestamp = {};
      if (filters.startDate) query.timestamp.$gte = filters.startDate;
      if (filters.endDate) query.timestamp.$lte = filters.endDate;
    }

    const skip = (page - 1) * limit;

    const [logs, total] = await Promise.all([
      db.collection(Collections.USER_ACTIVITIES)
        .find(query)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .toArray(),
      db.collection(Collections.USER_ACTIVITIES).countDocuments(query)
    ]);

    return { logs: logs as UserActivityLog[], total };
  } catch (error) {
    console.error('Failed to get user activity logs:', error);
    return { logs: [], total: 0 };
  }
}

// Get Stripe event logs with pagination and filtering
export async function getStripeEventLogs(
  page: number = 1,
  limit: number = 50,
  filters?: {
    eventType?: string;
    status?: string;
    startDate?: Date;
    endDate?: Date;
    userId?: string;
  }
): Promise<{ logs: StripeEventLog[]; total: number }> {
  // Only run on server side
  if (typeof window !== 'undefined' || !connectToDatabase || !Collections) {
    return { logs: [], total: 0 };
  }

  try {
    const { db } = await connectToDatabase();

    const query: any = {};

    if (filters?.eventType) query.eventType = filters.eventType;
    if (filters?.status) query.status = filters.status;
    if (filters?.userId) query.userId = filters.userId;
    if (filters?.startDate || filters?.endDate) {
      query.timestamp = {};
      if (filters.startDate) query.timestamp.$gte = filters.startDate;
      if (filters.endDate) query.timestamp.$lte = filters.endDate;
    }

    const skip = (page - 1) * limit;

    const [logs, total] = await Promise.all([
      db.collection(Collections.STRIPE_EVENTS)
        .find(query)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .toArray(),
      db.collection(Collections.STRIPE_EVENTS).countDocuments(query)
    ]);

    return { logs: logs as StripeEventLog[], total };
  } catch (error) {
    console.error('Failed to get Stripe event logs:', error);
    return { logs: [], total: 0 };
  }
}

// Get dashboard statistics
export async function getDashboardStats(): Promise<{
  apiRequests: { total: number; today: number; success: number; errors: number };
  userActivities: { total: number; today: number; uniqueUsers: number };
  stripeEvents: { total: number; today: number; successfulPayments: number; failedPayments: number };
}> {
  // Only run on server side
  if (typeof window !== 'undefined' || !connectToDatabase || !Collections) {
    return {
      apiRequests: { total: 0, today: 0, success: 0, errors: 0 },
      userActivities: { total: 0, today: 0, uniqueUsers: 0 },
      stripeEvents: { total: 0, today: 0, successfulPayments: 0, failedPayments: 0 }
    };
  }

  try {
    const { db } = await connectToDatabase();

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // API Requests stats
    const [
      totalApiRequests,
      todayApiRequests,
      successApiRequests,
      errorApiRequests
    ] = await Promise.all([
      db.collection(Collections.API_REQUESTS).countDocuments(),
      db.collection(Collections.API_REQUESTS).countDocuments({
        timestamp: { $gte: today, $lt: tomorrow }
      }),
      db.collection(Collections.API_REQUESTS).countDocuments({ status: 'success' }),
      db.collection(Collections.API_REQUESTS).countDocuments({ status: 'error' })
    ]);

    // User Activities stats
    const [
      totalUserActivities,
      todayUserActivities,
      uniqueUsersResult
    ] = await Promise.all([
      db.collection(Collections.USER_ACTIVITIES).countDocuments(),
      db.collection(Collections.USER_ACTIVITIES).countDocuments({
        timestamp: { $gte: today, $lt: tomorrow }
      }),
      db.collection(Collections.USER_ACTIVITIES).distinct('userId', { userId: { $ne: null } })
    ]);

    // Stripe Events stats
    const [
      totalStripeEvents,
      todayStripeEvents,
      successfulPayments,
      failedPayments
    ] = await Promise.all([
      db.collection(Collections.STRIPE_EVENTS).countDocuments(),
      db.collection(Collections.STRIPE_EVENTS).countDocuments({
        timestamp: { $gte: today, $lt: tomorrow }
      }),
      db.collection(Collections.STRIPE_EVENTS).countDocuments({ status: 'success' }),
      db.collection(Collections.STRIPE_EVENTS).countDocuments({ status: 'failed' })
    ]);

    return {
      apiRequests: {
        total: totalApiRequests,
        today: todayApiRequests,
        success: successApiRequests,
        errors: errorApiRequests
      },
      userActivities: {
        total: totalUserActivities,
        today: todayUserActivities,
        uniqueUsers: uniqueUsersResult.length
      },
      stripeEvents: {
        total: totalStripeEvents,
        today: todayStripeEvents,
        successfulPayments,
        failedPayments
      }
    };
  } catch (error) {
    console.error('Failed to get dashboard stats:', error);
    return {
      apiRequests: { total: 0, today: 0, success: 0, errors: 0 },
      userActivities: { total: 0, today: 0, uniqueUsers: 0 },
      stripeEvents: { total: 0, today: 0, successfulPayments: 0, failedPayments: 0 }
    };
  }
}
