import { loadStripe } from '@stripe/stripe-js';

export async function handleSubscription() {
  try {
    const response = await fetch('/api/subscription', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to create subscription session');
    }

    const { id: sessionId } = await response.json();
    const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
    await stripe?.redirectToCheckout({ sessionId });
  } catch (error) {
    console.error('Subscription error:', error);
    alert('Failed to initiate subscription. Please try again.');
  }
}
