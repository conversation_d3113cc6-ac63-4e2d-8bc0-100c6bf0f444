"use client";

import React, { createContext, useContext, useState, ReactNode, useCallback } from 'react';

// Define the structure of the WeChat context state
interface WeChatContextType {
  isWeChatPanelOpen: boolean;
  toggleWeChatPanel: () => void;
}

// Create the context with a default value
const WeChatContext = createContext<WeChatContextType | undefined>(undefined);

// Create the provider component
interface WeChatProviderProps {
  children: ReactNode;
}

export const WeChatProvider: React.FC<WeChatProviderProps> = ({ children }) => {
  const [isWeChatPanelOpen, setIsWeChatPanelOpen] = useState(false);

  // Toggle WeChat panel visibility
  const toggleWeChatPanel = useCallback(() => {
    setIsWeChatPanelOpen(prev => !prev);
  }, []);

  // Value provided by the context
  const value = {
    isWeChatPanelOpen,
    toggleWeChatPanel,
  };

  return <WeChatContext.Provider value={value}>{children}</WeChatContext.Provider>;
};

// Custom hook to use the WeChat context
export const useWeChat = (): WeChatContextType => {
  const context = useContext(WeChatContext);
  if (context === undefined) {
    throw new Error('useWeChat must be used within a WeChatProvider');
  }
  return context;
};
