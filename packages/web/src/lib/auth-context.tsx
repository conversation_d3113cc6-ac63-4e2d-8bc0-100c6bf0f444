"use client";

import React, { createContext, useContext, type ReactNode, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react'; // Import useSession
import { useRouter } from 'next/navigation';

// Define the user type - align with NextAuth session user if possible
// You might need to adjust this based on your actual session structure and callbacks
export interface User {
  id?: string; // Add id, often provided by NextAuth
  name?: string | null;
  email?: string | null;
  image?: string | null; // NextAuth often includes image
  // Add custom properties if needed, e.g., plan
  plan?: 'Free' | 'Pro' | 'Enterprise';
}

// Define the auth context type
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean; // Add loading state from useSession
  logout: () => Promise<void>;
  refreshSession: () => Promise<void>; // Add function to refresh session data
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true, // Default to loading initially
  logout: async () => {},
  refreshSession: async () => {},
});

// Create a provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const { data: session, status, update } = useSession();
  const isLoading = status === "loading";
  const router = useRouter();

  const logout = async () => {
    await signOut({
      redirect: false
    });
    localStorage.removeItem('redirectPath');
    router.push('/');
    router.refresh();
  };

  // Function to refresh the session data
  const refreshSession = async () => {
    try {
      await update();
      console.log('Session refreshed in auth context');
    } catch (error) {
      console.error('Failed to refresh session:', error);
    }
  };

  // Extract user data from session, potentially casting or mapping it
  // Note: The exact structure of session.user depends on your NextAuth config (callbacks, adapter)
  const sessionUser = session?.user as User | null;

  // Placeholder for fetching additional user data (like plan) if not included in session
  // In a real app, you might fetch this based on sessionUser.id or email
  const [customUserData, setCustomUserData] = React.useState<Partial<User>>({ plan: 'Free' }); // Default plan

  // Example: Fetch/Set custom data when session is available or loading state changes
  useEffect(() => {
    // Only run updates when not loading to avoid unnecessary changes
    if (!isLoading) {
      if (sessionUser) {
        // Replace with your actual API call to get user details/plan if needed
        // console.log("Fetching/setting custom data for user:", sessionUser.id || sessionUser.email);
        // fetch(`/api/user/${sessionUser.id}`)
        //   .then(res => res.json())
        //   .then(data => setCustomUserData({ plan: data.plan })) // Update with real data
        //   .catch(err => console.error("Failed to fetch custom user data:", err));

        // Use the plan from the session if available, otherwise default to 'Free'
        setCustomUserData({ plan: sessionUser.plan || 'Free' });
      } else {
        // No session user, ensure custom data reflects logged-out state
        setCustomUserData({ plan: 'Free' }); // Reset to default Free plan
      }
    }
    // Dependencies: Run effect when sessionUser object reference changes or isLoading changes
  }, [sessionUser, isLoading]);


  // Combine session user data with any additional fetched data
  const combinedUser: User | null = sessionUser
    ? {
        ...sessionUser,
        plan: customUserData.plan || sessionUser.plan || 'Free', // Prioritize fetched/session plan, then default
      }
    : null;

  return (
    <AuthContext.Provider
      value={{
        user: combinedUser,
        isAuthenticated: !!session, // True if session exists
        isLoading,
        logout,
        refreshSession,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Create a hook to use the auth context
export const useAuth = () => useContext(AuthContext);
