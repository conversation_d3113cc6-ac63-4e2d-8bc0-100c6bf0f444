import shippingOptionsData from './shipping-options.json';
import adminConfig from '../../adminConfig.mjs';

interface PricingTierKg {
  maxWeightKg: number;
  ratePerKgYuan?: number;
  flatRateYuan?: number;
}

interface PricingTier500g {
  first500gYuan: number;
  next500gYuan: number;
}

type PricingTier = PricingTierKg | PricingTier500g;

export interface ShippingOption {
  productType: string;
  channel_cn: string; // Added
  channel_en: string; // Added
  time: string;
  advantages_cn: string; // Added
  advantages_en: string; // Added
  pricingTiers: PricingTier[];
  notes: string;
  // Remove original combined fields if they existed, or ensure they are not used
}

// Type guard to check if a tier is based on kg
function isKgPricing(tier: PricingTier): tier is PricingTierKg {
  return (tier as PricingTierKg).maxWeightKg !== undefined;
}

// Type guard to check if a tier is based on 500g increments
function is500gPricing(tier: PricingTier): tier is PricingTier500g {
  return (tier as PricingTier500g).first500gYuan !== undefined;
}

export function calculateShippingCost(
  weightKg: number,
  option: ShippingOption
): number | null {
  if (weightKg <= 0) return 0; // No cost for zero weight

  // Use channel_en for logging if needed, or pass specific channel identifier
  const logChannel = option.channel_en || option.channel_cn;

  const tier = option.pricingTiers[0]; // Assuming structure based on JSON

  if (is500gPricing(tier)) {
    // Calculate based on 500g increments
    const weight500gUnits = Math.ceil(weightKg / 0.5);
    if (weight500gUnits <= 1) {
      return tier.first500gYuan;
    } else {
      return tier.first500gYuan + (weight500gUnits - 1) * tier.next500gYuan;
    }
  } else if (option.pricingTiers.every(isKgPricing)) {
    // Calculate based on kg tiers
    let applicableTier: PricingTierKg | undefined;
    // Find the correct tier based on weight
    for (const kgTier of option.pricingTiers as PricingTierKg[]) {
      if (weightKg <= kgTier.maxWeightKg) {
        applicableTier = kgTier;
        break;
      }
    }

    // If weight exceeds max defined tier, maybe return null or use last tier's rate?
    // For now, let's assume it falls into the last defined tier if heavier
    if (!applicableTier && option.pricingTiers.length > 0) {
        // Use the logic of the last tier if weight exceeds all defined maxWeights
        const lastTier = option.pricingTiers[option.pricingTiers.length - 1] as PricingTierKg;
         if (lastTier.ratePerKgYuan) {
             // If last tier has a per kg rate, apply it
             // Find the previous tier's max weight and cost to calculate correctly
             let previousTierMaxWeight = 0;
             let costUpToPreviousTier = 0;
             // This requires iterating through tiers again to sum up costs, complex.
             // Simpler approach for now: apply the last tier's rate per kg directly
             // This might not be accurate for tiered pricing structures like General Goods.
             // A more robust implementation would calculate cost segment by segment.
             // Let's return null for now if weight exceeds defined tiers with per kg rates.
             // return weightKg * lastTier.ratePerKgYuan; // Simplified/potentially inaccurate
             console.warn(`Weight ${weightKg}kg exceeds defined tiers for ${logChannel}. Cannot calculate accurately.`);
             return null;
         } else if (lastTier.flatRateYuan) {
             // If last tier is a flat rate, return that rate
             return lastTier.flatRateYuan;
         }
    }


    if (applicableTier) {
      if (applicableTier.flatRateYuan !== undefined) {
        return applicableTier.flatRateYuan;
      } else if (applicableTier.ratePerKgYuan !== undefined) {
        // This needs refinement for tiered per-kg rates (like General Goods)
        // The current logic assumes a simple rate * weight, which is wrong for tiers > 5kg
        // Example: 6kg should be 458 (for first 5kg) + 1kg * 88.
        // Let's implement the tiered logic correctly
        let cost = 0;
        let weightRemaining = weightKg;
        let previousMaxWeight = 0;

        for (const currentTier of option.pricingTiers as PricingTierKg[]) {
            const weightInThisTier = Math.max(0, Math.min(weightRemaining, currentTier.maxWeightKg - previousMaxWeight));

            if (weightInThisTier <= 0) break; // No more weight in subsequent tiers

            if (currentTier.flatRateYuan !== undefined) {
                // This assumes the flat rate applies up to maxWeightKg for the *first* tier only
                if (previousMaxWeight === 0) {
                    cost += currentTier.flatRateYuan;
                } else {
                     // This case shouldn't happen with current JSON structure (flat rate only for first tier)
                     console.error("Unexpected flat rate in non-first tier");
                     return null;
                }
            } else if (currentTier.ratePerKgYuan !== undefined) {
                cost += weightInThisTier * currentTier.ratePerKgYuan;
            }

            weightRemaining -= weightInThisTier;
            previousMaxWeight = currentTier.maxWeightKg;

            if (weightRemaining <= 0) break; // All weight accounted for
        }

         // Handle weight exceeding the last defined tier (if needed, e.g., apply last tier's rate)
         if (weightRemaining > 0) {
             const lastTier = option.pricingTiers[option.pricingTiers.length - 1] as PricingTierKg;
             if (lastTier.ratePerKgYuan) {
                 cost += weightRemaining * lastTier.ratePerKgYuan;
             } else {
                 // If the last tier was a flat rate and weight still remains, unclear how to price
                 console.warn(`Weight ${weightKg}kg exceeds defined tiers for ${logChannel}, and last tier is flat rate. Cannot calculate accurately.`);
                 return null;
             }
         }


        return cost;

      }
    }
  }

  console.error("Could not determine pricing structure for option:", logChannel, option);
  return null; // Indicate calculation failure
}

// Currency conversion functions
export function convertYuanToUSD(yuanAmount: number): number {
  return yuanAmount * adminConfig.currency.exchangeRates.CNY_USD;
}

export function convertYuanToEUR(yuanAmount: number): number {
  return yuanAmount * adminConfig.currency.exchangeRates.CNY_EUR;
}

// Function to get available shipping options (can be filtered by product type later)
export function getShippingOptions(): ShippingOption[] {
  // TODO: Add filtering logic based on product attributes if needed
  return shippingOptionsData as ShippingOption[];
}

// Function to get only Premium Line for Air Freight
export function getPremiumLineOption(): ShippingOption | undefined {
  return shippingOptionsData.find(option =>
    option.channel_en === 'Premium Line' &&
    option.productType === 'Goods with Batteries'
  ) as ShippingOption;
}

// Function to get all Sea Freight options
export function getSeaFreightOptions(): ShippingOption[] {
  return shippingOptionsData.filter(option =>
    option.channel_en.toLowerCase().includes('sea')
  ) as ShippingOption[];
}
