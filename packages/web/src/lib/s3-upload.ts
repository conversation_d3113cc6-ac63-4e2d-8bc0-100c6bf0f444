import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import adminConfig from '../../adminConfig.mjs';

// AWS credentials from .env file
// Using hardcoded values since environment variables aren't being properly loaded
const AWS_REGION = 'eu-west-3';
const AWS_ACCESS_KEY_ID = '********************';
const AWS_SECRET_ACCESS_KEY = '4DJSt4SfCvr3SrgEg679+H96W5T/Xfw/R7l98dDb';
const AWS_S3_BUCKET = 'micco-cashback';

// S3 client configuration
const s3Client = new S3Client({
  region: AWS_REGION,
  credentials: {
    accessKeyId: AWS_ACCESS_KEY_ID,
    secretAccessKey: AWS_SECRET_ACCESS_KEY,
  },
});

// S3 bucket name
const bucketName = AWS_S3_BUCKET;

/**
 * Upload a file to S3 and return the public URL
 * @param file The file to upload
 * @param prefix Optional prefix for the S3 key
 * @returns The public URL of the uploaded file
 */
export async function uploadToS3(file: File, prefix: string = adminConfig.s3.imagePrefix): Promise<string> {
  try {
    // Generate a unique filename
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const uniqueId = uuidv4();
    const key = `${prefix}/${uniqueId}.${fileExtension}`;

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Upload to S3
    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: key,
      Body: buffer,
      ContentType: file.type,
      // Remove ACL parameter as the bucket doesn't support ACLs
    });

    await s3Client.send(command);

    // Construct the public URL using the format from adminConfig
    const publicUrl = adminConfig.s3.publicUrlFormat
      .replace('{bucket}', bucketName)
      .replace('{region}', AWS_REGION)
      .replace('{key}', key);

    // Log the generated public URL for debugging
    console.log(`[S3 UPLOAD] Generated public URL: ${publicUrl}`);

    return publicUrl;
  } catch (error) {
    console.error('Error uploading to S3:', error);
    throw new Error(`Failed to upload image to S3: ${error instanceof Error ? error.message : String(error)}`);
  }
}
