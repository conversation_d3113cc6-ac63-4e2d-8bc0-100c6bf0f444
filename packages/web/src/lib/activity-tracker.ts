import { logUserActivity } from './admin-logging';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Track page visits
export async function trackPageVisit(
  url: string,
  activityType: 'page_visit' | 'search' | 'product_view' | 'checkout' | 'order_created',
  metadata?: any,
  request?: Request
): Promise<void> {
  try {
    // Get user session
    let userId: string | null = null;
    let userEmail: string | null = null;
    try {
      const session = await getServerSession(authOptions);
      userId = session?.user?.id || null;
      userEmail = session?.user?.email || null;
    } catch (error) {
      // Session might not be available in all contexts, continue without it
    }

    // Get IP address and user agent from request if available
    let ipAddress: string | undefined;
    let userAgent: string | undefined;
    let referrer: string | undefined;

    if (request) {
      ipAddress = request.headers.get('cf-connecting-ip') ||
                  request.headers.get('x-real-ip') ||
                  request.headers.get('x-forwarded-for') ||
                  'unknown';
      userAgent = request.headers.get('user-agent') || undefined;
      referrer = request.headers.get('referer') || undefined;
    }

    // Log the activity
    await logUserActivity({
      userId,
      userEmail,
      activityType,
      url,
      referrer,
      ipAddress,
      userAgent,
      metadata
    });
  } catch (error) {
    console.error('Failed to track user activity:', error);
    // Don't throw error to avoid breaking the main functionality
  }
}

// Track search activities
export async function trackSearch(
  query: string,
  searchType: 'text' | 'image',
  resultCount: number,
  request?: Request
): Promise<void> {
  const url = request ? new URL(request.url).pathname : '/search';
  
  await trackPageVisit(url, 'search', {
    query: query.substring(0, 200), // Truncate for storage
    searchType,
    resultCount
  }, request);
}

// Track product views
export async function trackProductView(
  productId: string,
  source: string,
  request?: Request
): Promise<void> {
  const url = request ? new URL(request.url).pathname : `/product/${productId}`;
  
  await trackPageVisit(url, 'product_view', {
    productId,
    source
  }, request);
}

// Track checkout activities
export async function trackCheckout(
  orderId: string,
  amount: number,
  currency: string,
  request?: Request
): Promise<void> {
  const url = request ? new URL(request.url).pathname : '/checkout';
  
  await trackPageVisit(url, 'checkout', {
    orderId,
    amount,
    currency
  }, request);
}

// Track order creation
export async function trackOrderCreated(
  orderId: string,
  amount: number,
  currency: string,
  itemCount: number,
  request?: Request
): Promise<void> {
  const url = request ? new URL(request.url).pathname : '/orders';
  
  await trackPageVisit(url, 'order_created', {
    orderId,
    amount,
    currency,
    itemCount
  }, request);
}
