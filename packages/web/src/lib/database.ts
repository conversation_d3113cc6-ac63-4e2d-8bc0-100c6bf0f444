import { MongoClient, Db } from 'mongodb';

const MONGODB_URI = process.env.MONGODB_URI;
const MONGODB_DB = 'miccobuy'; // Matches your Atlas database name

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable');
}

let cachedClient: MongoClient;
let cachedDb: Db;

export async function connectToDatabase() {
  if (cachedClient && cachedDb) {
    return { client: cachedClient, db: cachedDb };
  }

  try {
    console.log('Connecting to MongoDB Atlas...');
    const client = await MongoClient.connect(MONGODB_URI as string, {
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 30000
    });
    const db = client.db(MONGODB_DB);

    // Verify connection
    await db.command({ ping: 1 });
    console.log('✅ Successfully connected to MongoDB Atlas');

    cachedClient = client;
    cachedDb = db;

    return { client, db };
  } catch (error) {
    console.error('Failed to connect to MongoDB Atlas:', error);
    throw error;
  }
}

// Database schemas - Use distinct collection names
export const Collections = {
  USERS: 'users',
  ADDRESSES: 'addresses',
  ORDERS: 'orders',
  PRODUCTS: 'products',
  PAYMENTS: 'payments', // Keep or remove if not used
  USER_SEARCHES: 'user_searches' // Collection for storing user search data
};

// Indexes for better query performance
export async function createIndexes() {
  const { db } = await connectToDatabase();

  await db.collection(Collections.ADDRESSES).createIndex({ userId: 1 });
  await db.collection(Collections.ORDERS).createIndex({ userId: 1 });
  await db.collection(Collections.ORDERS).createIndex({ createdAt: -1 });
  await db.collection(Collections.PRODUCTS).createIndex({ title: 'text' });

  // Indexes for user searches
  await db.collection(Collections.USER_SEARCHES).createIndex({ createdAt: -1 });
  await db.collection(Collections.USER_SEARCHES).createIndex({ originalQuery: 1 });
  await db.collection(Collections.USER_SEARCHES).createIndex({ userId: 1 });

  // Indexes for product cache
  await db.collection(Collections.PRODUCTS).createIndex({ cacheKey: 1 }, { unique: true });
  await db.collection(Collections.PRODUCTS).createIndex({ expiresAt: 1 });
  await db.collection(Collections.PRODUCTS).createIndex({ source: 1, productId: 1 });
}
