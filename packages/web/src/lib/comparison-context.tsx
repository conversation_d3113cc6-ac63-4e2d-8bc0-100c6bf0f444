"use client";

import React, { createContext, useState, useContext, type ReactNode } from 'react';

// Define the product type
export interface Product {
  id: string;
  name: string;
  price: string;
  originalPriceCNY?: number; // Original price in CNY for currency conversion
  image: string;
  minOrder: string;
  supplier: string;
  source?: string; // Source of the product (taobao or 1688)
  rating?: string;
  delivery?: string;
  shippingFee?: string;
  sold?: string;
  match?: string;
  logistics?: string;
  detailUrl?: string; // URL to product detail page
  attributes?: Record<string, string>;
  isTranslating?: boolean; // Flag to indicate if the product is still being translated
  supplierInfo?: {
    name: string;
    years: number;
    country: string;
    interestedCustomers?: number;
    exportedMarkets?: string[];
    orders?: string;
    rating?: string;
  };
}

// Define the comparison context type
interface ComparisonContextType {
  selectedProducts: Product[];
  isCompareOpen: boolean;
  addToComparison: (product: Product) => void;
  removeFromComparison: (productId: string) => void;
  clearComparison: () => void;
  openComparePanel: () => void;
  closeComparePanel: () => void;
  isProductSelected: (productId: string) => boolean;
}

// Create the context with a default value
const ComparisonContext = createContext<ComparisonContextType>({
  selectedProducts: [],
  isCompareOpen: false,
  addToComparison: () => {},
  removeFromComparison: () => {},
  clearComparison: () => {},
  openComparePanel: () => {},
  closeComparePanel: () => {},
  isProductSelected: () => false,
});

// Create a provider component
export function ComparisonProvider({ children }: { children: ReactNode }) {
  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);
  const [isCompareOpen, setIsCompareOpen] = useState(false);

  // Enhance the addToComparison function
  const addToComparison = (product: Product) => {
    // Validate product has an id before adding
    if (!product || !product.id) {
      console.warn('Attempted to add invalid product to comparison');
      return;
    }

    // Check if already in comparison (based on ID)
    if (!selectedProducts.some(p => p.id === product.id)) {
      setSelectedProducts(prev => [...prev, product]);
    }

    // Don't auto-open panel - will be opened manually via "Compare Now" button
  };

  const removeFromComparison = (productId: string) => {
    // Safety check for invalid ids
    if (!productId) {
      console.warn('Attempted to remove invalid product from comparison');
      return;
    }

    setSelectedProducts(prev => prev.filter(p => p.id !== productId));

    // Close the panel when no products are left
    if (selectedProducts.length <= 1) {
      setIsCompareOpen(false);
    }
  };

  const clearComparison = () => {
    setSelectedProducts([]);
    setIsCompareOpen(false);
  };

  const openComparePanel = () => {
    if (selectedProducts.length > 0) {
      setIsCompareOpen(true);
    }
  };

  const closeComparePanel = () => {
    setIsCompareOpen(false);
  };

  const isProductSelected = (productId: string) => {
    if (!productId) return false;
    return selectedProducts.some(p => p.id === productId);
  };

  return (
    <ComparisonContext.Provider
      value={{
        selectedProducts,
        isCompareOpen,
        addToComparison,
        removeFromComparison,
        clearComparison,
        openComparePanel,
        closeComparePanel,
        isProductSelected
      }}
    >
      {children}
    </ComparisonContext.Provider>
  );
}

// Create a hook to use the comparison context
export const useComparison = () => useContext(ComparisonContext);
