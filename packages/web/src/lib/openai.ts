import OpenAI from 'openai';
import { logApiRequest } from './admin-logging';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Initialize OpenAI client
let openai: OpenAI | null = null;

export function getOpenAIClient() {
  if (!openai) {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is not defined in environment variables');
    }
    openai = new OpenAI({ apiKey });
  }
  return openai;
}

export interface ProductAnalysis {
  productName: string;
  price?: string;
  quantity?: number;
  specifications?: string[];
  customization?: string;
  thinking?: string[];
  verification?: string[];
  validation?: string[];
  chineseKeywords?: string; // Add Chinese keywords for search
  englishKeywords?: string; // Add English keywords for reference
}

export function checkIsProductAnalysis(analysis: any):analysis is ProductAnalysis {
  return typeof analysis === "object" && analysis.chineseKeywords && analysis.thinking;
}

export async function analyzeProductRequirement(prompt: string, model?: string): Promise<ProductAnalysis> {
  // Import adminConfig
  const adminConfig = await import('../../adminConfig.mjs');

  // Check if the model is "No AI" - if so, return a simple product analysis without using AI
  // Since we've temporarily commented out the "No AI" option, we'll just check for the specific names
  const isNoAiModel = model === "No AI (best)" ||
                      model === "Auto (best)";

  if (isNoAiModel) {
    return {
      productName: prompt,
      thinking: ["No AI analysis performed as per user selection."],
      verification: ["Search performed using keywords only."],
      validation: ["No AI validation performed."],
      // No translation to Chinese in No AI mode
      englishKeywords: prompt,
      chineseKeywords: prompt // Use original query for Chinese keywords
    };
  }

  const openai = getOpenAIClient();

  // Simplified prompt template to reduce token usage
  const userPromptTemplate = process.env.USER_PROMPT ||
    `Extract essential product information from the following request:
    - Identify the main product name and key specifications
    - Extract ONLY ONE primary keyword or short phrase that best describes the product
    - Translate this keyword to Chinese for use with Chinese e-commerce platforms
    - The Chinese keyword should be a single term without commas or separators
    - Keep analysis brief and focused on the most searchable term`;

  // Determine which model to use
  // Since we've temporarily commented out other models, we'll always use gpt-4o-mini
  let modelToUse = "gpt-4o-mini";
  // The following code is kept for when other models are restored
  if (model) {
    if (model.includes("Claude")) {
      // If Claude is selected but not available, fallback to GPT-4o-mini
    } else if (model.includes("GPT-4") && !model.includes("mini")) {
      // Only for GPT-4o without "mini"
      modelToUse = "gpt-4o-mini";
    }
  }

  console.log(`Using AI model: ${modelToUse} for analysis`);

  // Get user session for logging
  let userId: string | null = null;
  let userEmail: string | null = null;
  try {
    const session = await getServerSession(authOptions);
    userId = session?.user?.id || null;
    userEmail = session?.user?.email || null;
  } catch (error) {
    // Session might not be available in all contexts, continue without it
  }

  const startTime = Date.now();

  try {
    const response = await openai.chat.completions.create({
      model: modelToUse,
      messages: [
        {
          role: "system",
          content: "You are a professional product sourcing assistant specializing in Chinese e-commerce platforms like Taobao. Your job is to extract key product information and translate search terms to Chinese. Be concise and focus on terms that will yield good search results on Chinese platforms."
        },
        {
          role: "user",
          content: `${userPromptTemplate}\n\nUser request: "${prompt}"\n\nRespond with a JSON object containing these fields:\n1. productName: Brief product name\n2. specifications: Array of key specs (max 3)\n3. englishKeywords: ONLY ONE most important search term in English\n4. chineseKeywords: The SAME SINGLE keyword translated to Chinese (no commas, no multiple terms)\n5. thinking: Brief analysis (1-2 points only)\n\nIMPORTANT: For chineseKeywords, provide ONLY ONE term without any commas or separators. This will be used directly for search.`
        }
      ],
      response_format: { type: "json_object" },
      temperature: 0.2,
      max_tokens: 500, // Limit token usage
    });

    const responseTime = Date.now() - startTime;

    const result = response.choices[0]?.message?.content;
    if (!result) {
      throw new Error('No response from OpenAI');
    }

    try {
      const parsedResult = JSON.parse(result) as ProductAnalysis;

      // Ensure we have at least a product name
      if (!parsedResult.productName) {
        throw new Error('Missing product name in response');
      }

      // Log successful API request
      logApiRequest({
        apiType: 'openai',
        endpoint: '/chat/completions',
        method: 'POST',
        requestData: {
          model: modelToUse,
          prompt: prompt.substring(0, 200), // Truncate for storage
          temperature: 0.2,
          max_tokens: 500
        },
        responseData: {
          productName: parsedResult.productName,
          hasChineseKeywords: !!parsedResult.chineseKeywords,
          hasEnglishKeywords: !!parsedResult.englishKeywords
        },
        status: 'success',
        statusCode: 200,
        responseTime,
        userId,
        userEmail
      }).catch(logError => {
        console.error('Failed to log OpenAI API request:', logError);
      });

      return parsedResult;
    } catch (parseError) {
      // Log parsing error
      logApiRequest({
        apiType: 'openai',
        endpoint: '/chat/completions',
        method: 'POST',
        requestData: {
          model: modelToUse,
          prompt: prompt.substring(0, 200),
          temperature: 0.2,
          max_tokens: 500
        },
        status: 'error',
        statusCode: 200,
        errorMessage: 'JSON parsing failed',
        responseTime,
        userId,
        userEmail
      }).catch(logError => {
        console.error('Failed to log OpenAI API request:', logError);
      });

      // Create a minimal valid response
      return {
        productName: 'Product from your search',
        thinking: ['Unable to fully analyze your request. Please try a more specific query.'],
        verification: ['The AI was unable to properly verify your requirements.'],
        validation: ['Please provide more details for a better analysis.']
      };
    }
  } catch (error) {
    // Log API error
    logApiRequest({
      apiType: 'openai',
      endpoint: '/chat/completions',
      method: 'POST',
      requestData: {
        model: modelToUse,
        prompt: prompt.substring(0, 200),
        temperature: 0.2,
        max_tokens: 500
      },
      status: 'error',
      statusCode: 500,
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
      responseTime: Date.now() - startTime,
      userId,
      userEmail
    }).catch(logError => {
      console.error('Failed to log OpenAI API request:', logError);
    });

    // Rethrow the error for the caller to handle
    throw error;
  }
}
