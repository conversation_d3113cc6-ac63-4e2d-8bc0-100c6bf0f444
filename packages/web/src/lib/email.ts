import nodemailer from 'nodemailer';

// Create a test account if no email credentials are provided
let transporter: nodemailer.Transporter;

// Initialize the transporter
async function createTransporter() {
  // Check if we have email credentials
  if (process.env.EMAIL_USER && process.env.EMAIL_PASSWORD) {
    // Use provided credentials
    return nodemailer.createTransport({
      host: process.env.EMAIL_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
    });
  } else {
    // Create a test account for development
    console.log('Creating test email account...');
    const testAccount = await nodemailer.createTestAccount();
    console.log('Test email account created:', testAccount.user);

    return nodemailer.createTransport({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: testAccount.user,
        pass: testAccount.pass,
      },
    });
  }
}

// Initialize the transporter (will be set in the first email send)
let transporterPromise = createTransporter();

export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

/**
 * Send an email using nodemailer
 * @param options Email options including recipient, subject, and content
 * @returns Promise resolving to the send result
 */
export async function sendEmail(options: EmailOptions) {
  try {
    // Get the transporter (create it if it doesn't exist yet)
    const transporter = await transporterPromise;

    // Send the email
    const result = await transporter.sendMail({
      from: process.env.EMAIL_FROM || 'Miccobuy <<EMAIL>>',
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text || options.html.replace(/<[^>]*>/g, ''), // Strip HTML tags for text version
    });

    console.log('Email sent successfully:', result.messageId);

    // If using Ethereal (test account), log the preview URL
    if (result.messageId && !process.env.EMAIL_USER) {
      console.log('Preview URL: %s', nodemailer.getTestMessageUrl(result));
    }

    return {
      success: true,
      messageId: result.messageId,
      previewUrl: !process.env.EMAIL_USER ? nodemailer.getTestMessageUrl(result) : undefined
    };
  } catch (error) {
    console.error('Failed to send email:', error);
    return { success: false, error };
  }
}

/**
 * Generate a random verification code
 * @param length Length of the code (default: 6)
 * @returns Random numeric code as string
 */
export function generateVerificationCode(length: number = 6): string {
  return Array.from(
    { length },
    () => Math.floor(Math.random() * 10).toString()
  ).join('');
}

/**
 * Send a verification code email
 * @param email Recipient email address
 * @param code Verification code
 * @returns Promise resolving to the send result
 */
export async function sendVerificationEmail(email: string, code: string) {
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <h2 style="color: #333; text-align: center;">Verify Your Email Address</h2>
      <p style="color: #666; font-size: 16px;">Thank you for signing up with Miccobuy. Please use the following verification code to complete your registration:</p>
      <div style="background-color: #f5f5f5; padding: 15px; text-align: center; margin: 20px 0; border-radius: 4px;">
        <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px;">${code}</span>
      </div>
      <p style="color: #666; font-size: 14px;">This code will expire in 10 minutes.</p>
      <p style="color: #666; font-size: 14px;">If you didn't request this code, you can safely ignore this email.</p>
      <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
        <p>© ${new Date().getFullYear()} Miccobuy. All rights reserved.</p>
      </div>
    </div>
  `;

  return await sendEmail({
    to: email,
    subject: 'Verify Your Email Address',
    html,
  });
}
