interface TranslationOptions {
  targetLanguage: string;
  cacheKey?: string; // Optional cache key for storing translations
}

// Simple in-memory cache for translations
const translationCache: Record<string, { timestamp: number, data: Record<string, string> }> = {};
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// In-memory tracking of in-flight translation requests to prevent duplicates
const inFlightRequests: Record<string, Promise<any>> = {};

// 全局变量，用于记录翻译完成的次数，便于调试
let translationCompletedCount = 0;

export function getTextsToTransate (data: Record<string, any>, fields: string[]) {
  const newFields = fields.slice();
  const textsToTranslate: { key: string; value: string }[] = [];

  for (const field of newFields) {
    const anyMode = field.includes("[any]");
    const value = getNestedValue(data, field);

    if (anyMode) {
      let keys = field.split('.');
      keys = keys.slice(0, keys.indexOf('[any]'));
      const obj = keys.reduce((o, key) => (o && o[key] !== undefined) ? o[key] : null, data);
      if (!obj) continue;
      let subKeys = Object.keys(obj);
      subKeys = subKeys.map(it => field.replace('[any]', it));
      newFields.push(...subKeys);
    }
    // Handle string values
    else if (value && typeof value === 'string' && value.trim().length > 0) {
      textsToTranslate.push({ key: field, value });
    }
    // Handle object values (like props_list)
    else if (value && typeof value === 'object' && !Array.isArray(value)) {
      for (const [objKey, objValue] of Object.entries(value)) {
        if (typeof objValue === 'string' && objValue.trim().length > 0) {
          textsToTranslate.push({
            key: `${field}.${objKey}`,
            value: objValue
          });
        }
      }
    }
    // Handle array values
    else if (value && Array.isArray(value)) {
      value.forEach((item, index) => {
        if (typeof item === 'string' && item.trim().length > 0) {
          textsToTranslate.push({
            key: `${field}.${index}`,
            value: item
          });
        } else if (typeof item === 'object' && item !== null) {
          // For arrays of objects, we need to handle nested properties
          for (const [objKey, objValue] of Object.entries(item)) {
            if (typeof objValue === 'string' && objValue.trim().length > 0) {
              textsToTranslate.push({
                key: `${field}.${index}.${objKey}`,
                value: objValue
              });
            }
          }
        }
      });
    }
  }

  return textsToTranslate;
}

export async function translateProductData(
  data: Record<string, any>,
  fields: string[],
  options: TranslationOptions
): Promise<Record<string, any>> {
  // Skip translation if target language is Chinese
  if (options.targetLanguage === 'zh' || options.targetLanguage === 'zh-CN') {
    return data;
  }

  const result = { ...data };

  // Extract fields that need translation
  const textsToTranslate = getTextsToTransate(data, fields);

  // Check cache if cacheKey is provided
  const cacheKey = options.cacheKey ? `${options.cacheKey}:${options.targetLanguage}` : null;
  const now = Date.now();
  let cachedTranslations: Record<string, string> = {};

  if (cacheKey && translationCache[cacheKey] && (now - translationCache[cacheKey].timestamp < CACHE_EXPIRY)) {
    cachedTranslations = translationCache[cacheKey].data;
  }

  // Filter out texts that are already in the cache
  const textsToTranslateFiltered = textsToTranslate.filter(item => !cachedTranslations[item.value]);

  // If there are no texts to translate, return the original data
  if (textsToTranslateFiltered.length === 0) {
    return result;
  }

  // Generate a request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);

  try {

    // Create a request hash to identify duplicate requests
    const textsHash = textsToTranslateFiltered.map(item => item.value).join('|');
    const requestHash = `${textsHash}:${options.targetLanguage}`;

    // Check if there's already an in-flight request for these exact texts
    let translationPromise = inFlightRequests[requestHash];
    let translations: string[] = [];

    if (translationPromise) {
      translations = await translationPromise;
    } else {
      // Create a new promise for this translation request
      translationPromise = (async () => {
        const startTime = Date.now();
        const response = await fetch('/api/translate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            texts: textsToTranslateFiltered.map(item => item.value),
            targetLanguage: options.targetLanguage,
          }),
        });

        const responseTime = Date.now() - startTime;

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Translation API error');
        }

        const data = await response.json();
        return data.translations || [];
      })();

      // Store the promise for reuse
      inFlightRequests[requestHash] = translationPromise;

      try {
        translations = await translationPromise;
      } finally {
        // Clean up the in-flight request after it completes (success or failure)
        setTimeout(() => {
          delete inFlightRequests[requestHash];
        }, 1000); // Small delay to allow other concurrent requests to reuse it
      }
    }

    // Update the result object with translations and cache
    let appliedCount = 0;
    textsToTranslateFiltered.forEach((item, index) => {
      if (translations[index]) {
        setNestedValue(result, item.key, translations[index]);
        appliedCount++;

        // Add to cache
        if (cacheKey) {
          if (!translationCache[cacheKey]) {
            translationCache[cacheKey] = { timestamp: now, data: {} };
          }
          translationCache[cacheKey].data[item.value] = translations[index];
        }
      }
    });


    // 增加翻译完成计数
    translationCompletedCount++;

    // 记录一些翻译示例，用于调试
    if (translations.length > 0) {
      const sampleCount = Math.min(3, translations.length);
      const samples = textsToTranslateFiltered.slice(0, sampleCount).map((item, index) => ({
        original: item.value,
        translated: translations[index]
      }));
    }
  } catch (error: any) {
    console.error(`[Translator ${requestId}] Translation failed:`, error.message || error);
    // Continue with untranslated data
  }

  // Apply cached translations
  let cachedAppliedCount = 0;
  for (const item of textsToTranslate) {
    if (cachedTranslations[item.value]) {
      setNestedValue(result, item.key, cachedTranslations[item.value]);
      cachedAppliedCount++;
    }
  }



  return result;
}

// Helper function to get nested object values using dot notation
function getNestedValue(obj: Record<string, any>, path: string): any {
  const keys = path.split('.');
  return keys.reduce((o, key) => (o && o[key] !== undefined) ? o[key] : null, obj);
}

// Helper function to set nested object values using dot notation
export function setNestedValue(obj: Record<string, any>, path: string, value: any): void {
  const keys = path.split('.');
  const lastKey = keys.pop()!;
  const target = keys.reduce((o, key) => {
    if (o[key] === undefined) o[key] = {};
    return o[key];
  }, obj);
  target[lastKey] = value;
}

