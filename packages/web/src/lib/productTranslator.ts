import { translateProductData } from './translator';

// Fields to translate in product list items
const LIST_FIELDS_TO_TRANSLATE = [
  'title',
  'nick', // shop name
  'desc_short',
  'item_loc',
  'props_names', // specifications
  'promotions.title',
  'promotions.desc',
  'area', // Location information
  'shop_name', // Shop name if available
  'shop_title', // Shop title if available
  // 'source' 已移除，因为这是平台名称，不应该被翻译
];

// Fields to translate in product detail
export const DETAIL_FIELDS_TO_TRANSLATE = [
  'item.title',
  'item.desc_short',
  'item.nick',
  'item.props_list', // All specification properties
  'item.props_names',
  'item.props_imgs.props_name',
  'item.location', // Location information
  'item.props', // Product properties array
  'shop.shop_name',
  'shop.shop_title',
  'shop.shop_desc',
  'delivery.delivery_desc',
  'skus.sku', // Array of SKU objects
  'item.skus.sku.properties_name', // SKU properties names (including color and size)
  'skus.sku.properties_name', // SKU properties names (direct access)
  'comments.content',
  'seller_info.nick',
  'seller_info.shop_name',
  'seller_info.title',
  'seller_info.shop_title'
];

// Generate a cache key for a product
function generateCacheKey(product: any): string | undefined {
  if (product.num_iid) {
    return `product:${product.num_iid}`;
  }
  if (product.id) {
    return `product:${product.id}`;
  }
  return undefined;
}

export async function translateProductList(
  productList: any[],
  language: string
): Promise<any[]> {
  if (language === 'zh' || language === 'zh-CN') {
    return productList; // No translation needed
  }

  // Log product list information for debugging
  console.log(`[ProductTranslator] Translating product list with ${productList.length} items`);

  // Log sources of products
  const sources = productList.map(p => p.source || 'unknown');
  const sourceCounts = sources.reduce((acc: Record<string, number>, source: string) => {
    acc[source] = (acc[source] || 0) + 1;
    return acc;
  }, {});

  console.log(`[ProductTranslator] Product sources:`, sourceCounts);

  // Log sample product data
  if (productList.length > 0) {
    const sampleProduct = productList[0];
    console.log(`[ProductTranslator] Sample product data:`, {
      id: sampleProduct.num_iid || sampleProduct.id || 'unknown',
      title: sampleProduct.title ? (sampleProduct.title.length > 50 ? sampleProduct.title.substring(0, 50) + '...' : sampleProduct.title) : 'no title',
      source: sampleProduct.source || 'unknown',
      fields: Object.keys(sampleProduct)
    });
  }

  // Create a combined data object with all products
  const combinedData = {
    products: productList
  };

  // Create a list of fields to translate with product index
  const fieldsToTranslate = productList.flatMap((_, index) =>
    LIST_FIELDS_TO_TRANSLATE.map(field => `products.${index}.${field}`)
  );

  console.log(`[ProductTranslator] Total fields to translate: ${fieldsToTranslate.length}`);

  // Translate all products in one batch
  const translatedData = await translateProductData(
    combinedData,
    fieldsToTranslate,
    {
      targetLanguage: language,
      cacheKey: 'product_list'
    }
  );

  // Return the translated products array
  return translatedData.products;
}

export async function translateProductDetail(
  productDetail: Record<string, any>,
  language: string
): Promise<Record<string, any>> {
  if (language === 'zh' || language === 'zh-CN') {
    return productDetail; // No translation needed
  }


  // Generate cache key from product ID
  const cacheKey = productDetail.item ?
    generateCacheKey(productDetail.item) + ':detail' :
    'unknown:detail';



  return translateProductData(
    productDetail,
    DETAIL_FIELDS_TO_TRANSLATE,
    {
      targetLanguage: language,
      cacheKey
    }
  );
}