"use client";

import React, { createContext, useContext, useState, ReactNode, useCallback, useEffect } from 'react';

// Define the structure of a cart item
interface CartItem {
  productId: string;
  title: string;
  sku: string;
  skuProperties: string; // Added to distinguish variants
  imageUrl: string;
  price: number;
  originalPrice: number | null;
  quantity: number;
  storeId: string; // Added storeId to group items
  storeName: string; // Added storeName for display
  translatedStoreName?: string; // Added translatedStoreName for translated display
  selected: boolean; // Added for selection state
}

// Define the structure of the cart context state
interface CartContextType {
  cartItems: CartItem[];
  // Update addItem signature to accept quantity separately
  addItem: (itemBase: Omit<CartItem, 'selected' | 'quantity'>, quantity: number) => void;
  // Update removeItem signature
  removeItem: (productId: string, sku: string) => void;
  removeSelectedItems: () => void; // Add function to remove selected
  // Update updateQuantity signature
  updateQuantity: (productId: string, sku: string, quantity: number) => void;
  // Update toggleItemSelection signature
  toggleItemSelection: (productId: string, sku: string) => void;
  toggleSelectAllStore: (storeId: string, select: boolean) => void;
  toggleSelectAll: (select: boolean) => void;
  getCartSummary: () => { subtotal: number; discount: number; total: number; selectedItemCount: number };
  groupedItems: Record<string, CartItem[]>; // Items grouped by storeId
  isCartPanelOpen: boolean; // State for panel visibility
  toggleCartPanel: () => void; // Function to toggle panel
}

// Create the context with a default value (or null)
const CartContext = createContext<CartContextType | undefined>(undefined);

// Create the provider component
interface CartProviderProps {
  children: ReactNode;
}

// Helper functions for localStorage operations
const getCartFromStorage = (): CartItem[] => {
  if (typeof window === 'undefined') {
    return [];
  }

  try {
    const cartData = window.localStorage.getItem('cart');
    return cartData ? JSON.parse(cartData) : [];
  } catch (error) {
    console.error('Error reading cart from localStorage:', error);
    return [];
  }
};

const saveCartToStorage = (cartItems: CartItem[]) => {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    window.localStorage.setItem('cart', JSON.stringify(cartItems));
  } catch (error) {
    console.error('Error saving cart to localStorage:', error);
  }
};

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [cartItems, setCartItems] = useState<CartItem[]>(() => getCartFromStorage());
  const [isCartPanelOpen, setIsCartPanelOpen] = useState(false);

  // Save to localStorage whenever cartItems changes
  useEffect(() => {
    saveCartToStorage(cartItems);
  }, [cartItems]);

  // Add item to cart or update quantity if it exists
  // Updated signature and logic
  const addItem = useCallback((itemBase: Omit<CartItem, 'selected' | 'quantity'>, quantity: number) => {
    const quantityToAdd = Number(quantity);
    if (isNaN(quantityToAdd) || quantityToAdd <= 0) {
      console.error("Invalid quantity passed to addItem:", quantity);
      return;
    }

    // Find index based on current state
    const existingItemIndex = cartItems.findIndex(
      (item) => item.productId === itemBase.productId && item.sku === itemBase.sku
    );

    if (existingItemIndex > -1) {
      // Calculate new quantity BEFORE the updater
      const currentQuantity = Number(cartItems[existingItemIndex].quantity);
      const newQuantity = currentQuantity + quantityToAdd;
      // console.log(`Pre-calculating update for item ${itemBase.productId}. Current Qty: ${currentQuantity}, Adding Qty: ${quantityToAdd}, New Qty: ${newQuantity}`); // Removed debug log

      setCartItems((prevItems) => {
        // Double-check index in prevItems in case of rapid updates
        const idx = prevItems.findIndex(
            (item) => item.productId === itemBase.productId && item.sku === itemBase.sku
        );
        if (idx === -1) return prevItems; // Should not happen if index found above, but safe check

        const updatedItems = [...prevItems];
        updatedItems[idx] = {
            ...updatedItems[idx],
            quantity: newQuantity, // Use pre-calculated quantity
            selected: true
        };
        return updatedItems;
      });
    } else {
      // Item doesn't exist, add it
      // console.log(`Adding new item ${itemBase.productId} with Qty: ${quantityToAdd}`); // Removed debug log
      setCartItems((prevItems) => [
        ...prevItems,
        { ...itemBase, quantity: quantityToAdd, selected: true },
      ]);
    }
  }, [cartItems]); // Add cartItems dependency

  // Remove item from cart (updated to use productId and sku)
  const removeItem = useCallback((productId: string, sku: string) => {
    setCartItems((prevItems) =>
      prevItems.filter((item) => !(item.productId === productId && item.sku === sku))
    );
  }, []);

  // Remove all selected items from cart
  const removeSelectedItems = useCallback(() => {
    setCartItems((prevItems) => prevItems.filter((item) => !item.selected));
  }, []);

  // Update item quantity (updated to use productId and sku)
  const updateQuantity = useCallback((productId: string, sku: string, quantity: number) => {
    setCartItems((prevItems) =>
      prevItems.map((item) =>
        item.productId === productId && item.sku === sku
          ? { ...item, quantity: Math.max(1, quantity) } // Ensure quantity is at least 1
          : item
      )
    );
  }, []);

  // Toggle selection state of a single item (updated to use productId and sku)
  const toggleItemSelection = useCallback((productId: string, sku: string) => {
    setCartItems((prevItems) =>
      prevItems.map((item) =>
        item.productId === productId && item.sku === sku
          ? { ...item, selected: !item.selected }
          : item
      )
    );
  }, []);

  // Toggle selection state for all items in a specific store
  const toggleSelectAllStore = useCallback((storeId: string, select: boolean) => {
    setCartItems((prevItems) =>
      prevItems.map((item) =>
        item.storeId === storeId ? { ...item, selected: select } : item
      )
    );
  }, []);

  // Toggle selection state for all items in the cart
  const toggleSelectAll = useCallback((select: boolean) => {
    setCartItems((prevItems) =>
      prevItems.map((item) => ({ ...item, selected: select }))
    );
  }, []);

  // Toggle cart panel visibility
  const toggleCartPanel = useCallback(() => {
    setIsCartPanelOpen(prev => !prev);
  }, []);

  // Calculate cart summary based on selected items
  const getCartSummary = useCallback(() => {
    let subtotal = 0;
    let selectedItemCount = 0;
    let totalDiscount = 0;

    cartItems.forEach((item) => {
      if (item.selected) {
        // Calculate subtotal based on current price
        subtotal += item.price * item.quantity;
        selectedItemCount++;

        // Calculate discount based on difference between original and current price
        if (item.originalPrice && item.originalPrice > item.price) {
          const itemDiscount = (item.originalPrice - item.price) * item.quantity;
          totalDiscount += itemDiscount;
        }
      }
    });

    // Calculate total after discount
    const total = subtotal - totalDiscount;

    return {
      subtotal,
      discount: totalDiscount, // Return discount in CNY (will be formatted to EUR in UI)
      total: Math.max(0, total),
      selectedItemCount
    };
  }, [cartItems]);

  // Group items by store for rendering
  const groupedItems = cartItems.reduce((acc, item) => {
    const storeId = item.storeId;
    if (!acc[storeId]) {
      acc[storeId] = [];
    }
    acc[storeId].push(item);
    return acc;
  }, {} as Record<string, CartItem[]>);


  // Value provided by the context
  const value = {
    cartItems,
    addItem,
    removeItem,
    updateQuantity,
    toggleItemSelection,
    toggleSelectAllStore,
    toggleSelectAll,
    getCartSummary,
    groupedItems,
    isCartPanelOpen, // Add state to context value
    toggleCartPanel, // Add function to context value
    removeSelectedItems, // Add new function to context value
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
};

// Custom hook to use the Cart context
export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
