// Define type interfaces in a separate file to avoid bundling server-side code.
// The OrderStatus enum is defined and exported from the model file (order.ts).
// The 'Order' interface below will use that definition when imported alongside the model.

export interface OrderItem {
  productId: string; // Storing num_iid as string
  quantity: number;
  price: number;
  sku: string;
  imageUrl: string;
  title: string;
}

export interface ShippingInfo {
  addressId: string; // Use string for shared type
  method: string;
  trackingNumber?: string;
  estimatedDelivery?: Date;
}

export interface PaymentInfo {
  method: string;
  amount: number;
  currency: string;
  transactionId?: string;
  status: 'pending' | 'completed' | 'failed';
}

export interface Order {
  _id?: string; // Use string for shared type
  userId: string; // Use string for shared type
  items: OrderItem[];
  shipping: ShippingInfo;
  payment: PaymentInfo;
  status: OrderStatus; // Restore the correct type
  subtotal: number;
  shippingCost: number;
  tax: number;
  total: number;
  createdAt: Date;
  updatedAt: Date;
}
