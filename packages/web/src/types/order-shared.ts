// Shared types and enums for Orders, safe for client and server import.

// Enum definition
export enum OrderStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  PENDING_TRANSFER = 'pending_transfer'
}

// Interface definitions (using only client-safe types like string for IDs)
export interface OrderItem {
  productId: string;
  quantity: number;
  price: number;
  sku: string;
  imageUrl: string;
  title: string;
  // Package information (for PENDING_TRANSFER status)
  dimensions?: string; // e.g., "26*25*10cm"
  weight?: number; // in kg
  volumeWeight?: number; // in kg
  billableWeight?: number; // max of weight and volumeWeight
}

export interface ShippingInfo {
  addressId: string;
  method: string;
  trackingNumber?: string;
  estimatedDelivery?: Date;
}

export interface PaymentInfo {
  method: string;
  amount: number;
  currency: string;
  transactionId?: string;
  status: 'pending' | 'completed' | 'failed';
}

export interface Order {
  _id?: string;
  userId: string;
  items: OrderItem[];
  shipping: ShippingInfo;
  payment: PaymentInfo;
  status: OrderStatus; // Uses the enum defined above
  subtotal: number;
  shippingCost: number;
  tax: number;
  total: number;
  createdAt: Date;
  updatedAt: Date;
  // Additional fields for PENDING_TRANSFER status
  freeStoragePeriod?: number; // in days
  warehouseEntryDate?: Date; // when the package entered the warehouse
  combined?: boolean; // whether the order has been combined with others
  combinedAt?: Date; // when the order was combined
  combinedGroupId?: string; // ID to group combined orders together
}
