declare module 'react-syntax-highlighter' {
  import { ComponentType, ReactNode } from 'react';
  
  export interface SyntaxHighlighterProps {
    language?: string;
    style?: any;
    customStyle?: any;
    codeTagProps?: any;
    useInlineStyles?: boolean;
    showLineNumbers?: boolean;
    startingLineNumber?: number;
    lineNumberStyle?: any;
    wrapLines?: boolean;
    lineProps?: any;
    renderer?: any;
    PreTag?: ComponentType<any>;
    CodeTag?: ComponentType<any>;
    children?: ReactNode;
    className?: string;
  }
  
  export const Prism: ComponentType<SyntaxHighlighterProps>;
  export const Light: ComponentType<SyntaxHighlighterProps>;
  export default ComponentType<SyntaxHighlighterProps>;
}

declare module 'react-syntax-highlighter/dist/esm/styles/prism' {
  export const tomorrow: any;
  export const prism: any;
  export const dark: any;
  export const funky: any;
  export const okaidia: any;
  export const twilight: any;
  export const coy: any;
  export const solarizedlight: any;
  export const base16AteliersulphurpoolLight: any;
}

declare module 'react-markdown' {
  import { ComponentType, ReactNode } from 'react';
  
  export interface ReactMarkdownProps {
    children: string;
    className?: string;
    components?: {
      [key: string]: ComponentType<any>;
    };
  }
  
  export interface MarkdownComponentProps {
    node: any;
    inline?: boolean;
    className?: string;
    children?: ReactNode;
    href?: string;
    src?: string;
    alt?: string;
    [key: string]: any;
  }
  
  const ReactMarkdown: ComponentType<ReactMarkdownProps>;
  export default ReactMarkdown;
}
