"use client";

import { useComparison, type Product } from "@/lib/comparison-context";
import { X, Trash2, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { ProductImageGallery } from "./ProductImageGallery";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

export default function ComparePanel() {
  const {
    selectedProducts,
    isCompareOpen,
    closeComparePanel,
    removeFromComparison,
    clearComparison
  } = useComparison();

  const [isMounted, setIsMounted] = useState(false);
  const [productAttributes, setProductAttributes] = useState<string[]>([]);
  const tCommon = useTranslations(I18NNamespace.COMMON);
  const t = useTranslations(I18NNamespace.COMPONENTS_COMPAREPANEL);
  const tCheckout = useTranslations(I18NNamespace.PAGES_CHECKOUT);

  // Handle client-side mounting
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // When selected products change, collect all unique attribute keys
  useEffect(() => {
    if (selectedProducts.length > 0) {
      const allAttributes = new Set<string>();

      // Basic product attributes to always show
      const basicAttributes = ['Price', 'Min. order', 'Estimated delivery by', 'Shipping fee'];
      for (const attr of basicAttributes) {
        allAttributes.add(attr);
      }

      // Supplier attributes to always show
      const supplierAttributes = ['Supplier', 'Years in industry', 'Interested customers', 'Main exported markets', 'Orders'];
      for (const attr of supplierAttributes) {
        allAttributes.add(attr);
      }

      // Product specific attributes
      for (const product of selectedProducts) {
        if (product.attributes) {
          for (const key of Object.keys(product.attributes)) {
            allAttributes.add(key);
          }
        }
      }

      setProductAttributes(Array.from(allAttributes));
    }
  }, [selectedProducts]);

  // Helper to get attribute value
  const getAttributeValue = (product: Product, attribute: string): string => {
    if (!product) return '-';

    switch(attribute) {
      case 'Price':
        return product.price || '-';
      case 'Min. order':
        return product.minOrder || '-';
      case 'Estimated delivery by':
        return product.delivery || t("TO_BE_NEGOTIATED");
      case 'Shipping fee':
        return product.shippingFee || t("TO_BE_NEGOTIATED");
      case 'Supplier':
        return product.supplierInfo?.name || product.supplier || '-';
      case 'Years in industry':
        return product.supplierInfo?.years ? `${product.supplierInfo.years}` : '-';
      case 'Interested customers':
        return product.supplierInfo?.interestedCustomers?.toString() || '-';
      case 'Main exported markets':
        return product.supplierInfo?.exportedMarkets?.join(', ') || '-';
      case 'Orders':
        return product.supplierInfo?.orders || '-';
      default:
        return product.attributes?.[attribute] || '-';
    }
  };

  // Don't render anything on the server or if panel is closed
  if (!isMounted || !isCompareOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={closeComparePanel}
      />

      {/* Slide-in panel */}
      <div
        className="fixed inset-y-0 right-0 w-full md:w-4/5 lg:w-3/4 bg-white shadow-xl z-50 overflow-y-auto transform transition-transform duration-300"
        style={{ transform: 'translateX(0)' }}
      >
        <div className="sticky top-0 bg-gray-900 text-white p-4 flex justify-between items-center z-10">
          <h2 className="text-xl font-bold">{tCommon("COMPARE")}</h2>
          <div className="flex items-center space-x-4">
            <Button
              onClick={clearComparison}
              variant="ghost"
              className="text-white hover:bg-gray-800"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {tCommon("CLEAR_ALL")}
            </Button>
            <button
              onClick={closeComparePanel}
              className="p-1 rounded-full hover:bg-gray-800"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="min-w-full">
          {/* NEW: Image Gallery Section */}
          <div className="grid grid-cols-[240px_repeat(auto-fill,minmax(180px,1fr))] border-b">
            <div className="p-4 bg-gray-50 font-semibold border-r flex items-center">
              {t("PRODUCT_IMAGES")}
            </div>

            {selectedProducts.map((product) => (
              <div key={`${product.id}-gallery`} className="p-3 border-r">
                <div className="mb-3">
                  <ProductImageGallery product={product} />
                </div>
              </div>
            ))}
          </div>

          {/* Product header with thumbnails and actions */}
          <div className="grid grid-cols-[240px_repeat(auto-fill,minmax(180px,1fr))] border-b">
            <div className="p-4 font-semibold text-gray-700 border-r">
              Product
            </div>

            {selectedProducts.map((product) => (
              <div key={product.id} className="p-4 border-r relative">
                <div className="absolute top-2 right-2">
                  <button
                    onClick={() => removeFromComparison(product.id)}
                    className="p-1 rounded-full hover:bg-gray-100"
                  >
                    <X className="w-4 h-4 text-gray-500" />
                  </button>
                </div>

                <div className="w-full flex items-center mb-2">
                  <div className="w-16 h-16 bg-gray-100 rounded flex-shrink-0 mr-2 overflow-hidden">
                    {product.image ? (
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-full h-full object-contain"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gray-200 text-gray-400 text-xs">
                        {t("NO_IMAGE")}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-medium truncate mb-1">{product.name}</h3>
                    <Button
                      size="sm"
                      className="w-full bg-gray-900 hover:bg-black text-white text-xs py-1"
                    >
                      <Send className="w-3 h-3 mr-1" />
                      {t("SEND_INQUIRY")}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Product basic info */}
          <div className="grid grid-cols-[240px_repeat(auto-fill,minmax(180px,1fr))] border-b">
            <div className="p-4 bg-gray-50 font-semibold border-r">
              {t("PRODUCT_BASIC")}
            </div>

            {selectedProducts.map((product) => (
              <div key={`${product.id}-basic`} className="border-r">
                {/* Empty header cell */}
              </div>
            ))}
          </div>

          {/* Price and other basic attributes */}
          {['Price', 'Min. order', 'Estimated delivery by', 'Shipping fee'].map((attribute) => (
            <div
              key={attribute}
              className="grid grid-cols-[240px_repeat(auto-fill,minmax(180px,1fr))] border-b"
            >
              <div className="p-4 font-medium text-gray-700 border-r">
                {attribute === "Min. order" ? t("MIN_ORDER") : t(attribute)}
              </div>

              {selectedProducts.map((product) => {
                const value = getAttributeValue(product, attribute);
                const isHighlighted = attribute === 'Price' || attribute === 'Min. order';

                return (
                  <div
                    key={`${product.id}-${attribute}`}
                    className="p-4 border-r"
                  >
                    <span className={isHighlighted ? 'text-green-600 font-medium' : ''}>
                      {value}
                    </span>
                  </div>
                );
              })}
            </div>
          ))}

          {/* Supplier basic info */}
          <div className="grid grid-cols-[240px_repeat(auto-fill,minmax(180px,1fr))] border-b">
            <div className="p-4 bg-gray-50 font-semibold border-r">
              {t("SUPPLIER_BASIC")}
            </div>

            {selectedProducts.map((product) => (
              <div key={`${product.id}-supplier-basic`} className="border-r">
                {/* Empty header cell */}
              </div>
            ))}
          </div>

          {/* Supplier info attributes */}
          {['Supplier', 'Years in industry', 'Interested customers', 'Main exported markets', 'Orders'].map((attribute) => (
            <div
              key={attribute}
              className="grid grid-cols-[240px_repeat(auto-fill,minmax(180px,1fr))] border-b"
            >
              <div className="p-4 font-medium text-gray-700 border-r">
                {attribute}
              </div>

              {selectedProducts.map((product) => {
                const value = getAttributeValue(product, attribute);
                const isHighlighted = attribute === 'Orders';

                return (
                  <div
                    key={`${product.id}-${attribute}`}
                    className="p-4 border-r"
                  >
                    <span className={isHighlighted ? 'text-green-600 font-medium' : ''}>
                      {value}
                    </span>
                  </div>
                );
              })}
            </div>
          ))}

          {/* Product attributes */}
          <div className="grid grid-cols-[240px_repeat(auto-fill,minmax(180px,1fr))] border-b">
            <div className="p-4 bg-gray-50 font-semibold border-r">
              {t("PRODUCT_ATTRIBUTES")}
            </div>

            {selectedProducts.map((product) => (
              <div key={`${product.id}-attrs-header`} className="border-r">
                {/* Empty header cell */}
              </div>
            ))}
          </div>

          {/* Product specific attributes */}
          {productAttributes.filter(attr =>
            !['Price', 'Min. order', 'Estimated delivery by', 'Shipping fee',
             'Supplier', 'Years in industry', 'Interested customers', 'Main exported markets', 'Orders'].includes(attr)
          ).map((attribute) => (
            <div
              key={attribute}
              className="grid grid-cols-[240px_repeat(auto-fill,minmax(180px,1fr))] border-b"
            >
              <div className="p-4 font-medium text-gray-700 border-r">
                {attribute}
              </div>

              {selectedProducts.map((product) => (
                <div
                  key={`${product.id}-${attribute}`}
                  className="p-4 border-r"
                >
                  {product.attributes?.[attribute] || '-'}
                </div>
              ))}
            </div>
          ))}

          {/* Supplier service/capabilities */}
          <div className="grid grid-cols-[240px_repeat(auto-fill,minmax(180px,1fr))] border-b">
            <div className="p-4 bg-gray-50 font-semibold border-r">
              {t("SUPPLIER_SERVICE_CAPABILITIES")}
            </div>

            {selectedProducts.map((product) => (
              <div key={`${product.id}-service-header`} className="border-r">
                {/* Empty header cell */}
              </div>
            ))}
          </div>

          {/* Reviews */}
          <div
            className="grid grid-cols-[240px_repeat(auto-fill,minmax(180px,1fr))] border-b"
          >
            <div className="p-4 font-medium text-gray-700 border-r">
              {t("REVIEWS")}
            </div>

            {selectedProducts.map((product) => (
              <div
                key={`${product.id}-reviews`}
                className="p-4 border-r"
              >
                {product.supplierInfo?.rating || t("GOOD_SERVICE")}
              </div>
            ))}
          </div>

          {/* Store rating */}
          <div
            className="grid grid-cols-[240px_repeat(auto-fill,minmax(180px,1fr))] border-b"
          >
            <div className="p-4 font-medium text-gray-700 border-r">
              {t("STORE_RATING")}
            </div>

            {selectedProducts.map((product) => (
              <div
                key={`${product.id}-store-rating`}
                className="p-4 border-r"
              >
                <span className="text-green-600 font-medium">
                  5.0/5.0
                </span>
              </div>
            ))}
          </div>

          {/* Payment method */}
          <div
            className="grid grid-cols-[240px_repeat(auto-fill,minmax(180px,1fr))] border-b"
          >
            <div className="p-4 font-medium text-gray-700 border-r">
              {tCheckout("PAYMENY_METHOD")}
            </div>

            {selectedProducts.map((product, index) => (
              <div
                key={`${product.id}-payment`}
                className="p-4 border-r"
              >
                {index === 0 ? 'L/C,D/A,D/P,T/T,Western Union' : '-'}
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
