"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import { useComparison } from "@/lib/comparison-context";
import { CheckSquare } from "lucide-react";

export function CompareFloatingButton() {
  const { selectedProducts, openComparePanel } = useComparison();
  const tCommon = useTranslations(I18NNamespace.COMMON);

  if (selectedProducts.length === 0) return null;

  return (
    <div className="fixed bottom-4 right-4 z-30">
      <Button
        onClick={openComparePanel}
        className="px-4 py-2 bg-black text-white shadow-lg rounded-lg flex items-center"
      >
        <CheckSquare className="w-5 h-5 mr-2" />
        {tCommon("COMPARE")} ({selectedProducts.length})
      </Button>
    </div>
  );
}
