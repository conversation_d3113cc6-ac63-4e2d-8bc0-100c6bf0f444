"use client";

import { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import type { Product } from "@/lib/comparison-context";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

interface ProductImageGalleryProps {
  product: Product;
}

export function ProductImageGallery({ product }: ProductImageGalleryProps) {
  // For now we're simulating multiple images by duplicating the single image with different zooms
  // In a real implementation, the product would have an array of images
  const [imageIndex, setImageIndex] = useState(0);
  const t = useTranslations(I18NNamespace.COMPONENTS_PRODUCT_IMAGE_GALLERY);

  // Generate mock image array for demonstration
  const images = product.image
    ? [
        { id: `${product.id}-img-1`, src: product.image, alt: `${product.name} - Main view` },
        { id: `${product.id}-img-2`, src: product.image, alt: `${product.name} - Detail view` },
        { id: `${product.id}-img-3`, src: product.image, alt: `${product.name} - Side view` },
      ]
    : [];

  return (
    <div className="relative w-full">
      {images.length === 0 ? (
        <div className="w-full aspect-square bg-gray-100 flex items-center justify-center text-gray-400 text-sm">
         {t("NO_IMAGE")}
        </div>
      ) : (
        <>
          {/* Main image */}
          <div className="aspect-square bg-gray-50 flex items-center justify-center overflow-hidden relative">
            <img
              src={images[imageIndex].src}
              alt={images[imageIndex].alt}
              className="w-full h-full object-contain"
            />
          </div>

          {/* Navigation buttons */}
          {images.length > 1 && (
            <>
              <button
                onClick={() => setImageIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1))}
                className="absolute left-1 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-1 shadow-sm hover:bg-white"
                aria-label="Previous image"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
              <button
                onClick={() => setImageIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1))}
                className="absolute right-1 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-1 shadow-sm hover:bg-white"
                aria-label="Next image"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </>
          )}

          {/* Image thumbnails */}
          {images.length > 1 && (
            <div className="flex mt-2 space-x-2 justify-center">
              {images.map((image) => (
                <button
                  key={image.id}
                  onClick={() => setImageIndex(images.findIndex(img => img.id === image.id))}
                  className={`w-8 h-8 border-2 rounded overflow-hidden ${
                    image.id === images[imageIndex].id ? "border-black" : "border-transparent"
                  }`}
                >
                  <img src={image.src} alt="Thumbnail" className="w-full h-full object-cover" />
                </button>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
}
