"use client";

import { useState, useEffect } from "react";
import { useSidebar } from "@/components/ui/sidebar";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";

interface HamburgerMenuProps {
  className?: string;
  darkMode?: boolean;
}

export function HamburgerMenu({ className, darkMode = false }: HamburgerMenuProps) {
  const { toggleSidebar } = useSidebar();

  // Custom toggle function that tracks user interaction
  const handleToggle = () => {
    toggleSidebar();
  };

  return (
    <div className={cn("flex items-center", className)}>
      <div
        className={cn(
          "h-8 w-8 flex items-center justify-center cursor-pointer",
          darkMode ? "text-white" : "text-gray-700"
        )}
        onClick={handleToggle}
      >
        <img
          src="/images/menu.svg"
          alt="Menu"
          className={cn(
            "h-6 w-6",
            darkMode && "filter invert"
          )}
        />
      </div>
    </div>
  );
}
