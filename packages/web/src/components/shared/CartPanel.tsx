"use client";

import React from 'react';
import { useRouter } from 'next/navigation'; // Import useRouter
import { useCart } from '@/lib/cart-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Image from 'next/image';
import { Trash2, X, AlertTriangle } from 'lucide-react'; // Added X, AlertTriangle
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"; // Import AlertDialog components
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetFooter,
  SheetClose, // Import SheetClose
} from "@/components/ui/sheet"; // Using Shadcn Sheet
import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';
import { formatPrice } from '@/utils/currency';

export function CartPanel() {
  const {
    isCartPanelOpen,
    toggleCartPanel,
    groupedItems,
    updateQuantity,
    removeItem,
    toggleItemSelection,
    toggleSelectAllStore,
    toggleSelectAll,
    getCartSummary,
    cartItems,
    removeSelectedItems // Get removeSelectedItems function
  } = useCart();
  const router = useRouter(); // Initialize router
  const t = useTranslations(I18NNamespace.COMPONENTS_CART_PANEL);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  const summary = getCartSummary();
  const allItemsSelected = cartItems.length > 0 && cartItems.every(item => item.selected);
  const selectedItemCount = summary.selectedItemCount;

  const areAllStoreItemsSelected = (storeId: string) => {
    const itemsInStore = groupedItems[storeId] || [];
    return itemsInStore.length > 0 && itemsInStore.every(item => item.selected);
  };

  return (
    <Sheet open={isCartPanelOpen} onOpenChange={toggleCartPanel}>
      <SheetContent className="w-[400px] sm:w-[540px] flex flex-col"> {/* Adjust width as needed */}
        <SheetHeader className="border-b pb-4 mb-4">
          <SheetTitle className="text-lg font-semibold">{t("SHOPPING_CART")}</SheetTitle>
          {/* Removed commented out button */}
        </SheetHeader>

        {/* Cart Header Actions */}
        <div className="flex justify-between items-center mb-4 px-1 text-sm">
          <div>
              <input
                type="checkbox"
                id="selectAllPanel"
                className="mr-2"
                checked={allItemsSelected}
                onChange={(e) => toggleSelectAll(e.target.checked)}
              />
              <label htmlFor="selectAllPanel" className="mr-4">{t("SELECT_ALL")} ({cartItems.length})</label>
          </div>
           <div>
             {/* Delete Selected Button with Confirmation */}
             <AlertDialog>
               <AlertDialogTrigger asChild>
                 <Button variant="outline" size="sm" disabled={selectedItemCount === 0}>{t("DELETE")}</Button>
               </AlertDialogTrigger>
               <AlertDialogContent>
                 <AlertDialogHeader>
                   <AlertDialogTitle className="flex items-center">
                     <AlertTriangle className="w-5 h-5 mr-2 text-red-500" />
                     {t("CONFIRM_DELETION")}
                   </AlertDialogTitle>
                   <AlertDialogDescription>
                     {t("REMOVE", { selectedItemCount })}
                   </AlertDialogDescription>
                 </AlertDialogHeader>
                 <AlertDialogFooter>
                   <AlertDialogCancel>{tCommon("CANCEL")}</AlertDialogCancel>
                   <AlertDialogAction
                     onClick={removeSelectedItems} // Call removeSelectedItems on confirm
                     className="bg-red-600 hover:bg-red-700"
                   >
                     {t("DELETE")}
                   </AlertDialogAction>
                 </AlertDialogFooter>
               </AlertDialogContent>
             </AlertDialog>
           </div>
        </div>

        {/* Cart Items List - Make it scrollable */}
        <div className="flex-grow overflow-y-auto space-y-4 pr-2"> {/* Added padding-right for scrollbar */}
          {Object.keys(groupedItems).length === 0 && (
            <p className="text-center text-gray-500 mt-10">{t("EMPTY_CART")}</p>
          )}
          {Object.entries(groupedItems).map(([storeId, items]) => (
            <div key={storeId} className="bg-white rounded border border-gray-200">
              {/* Store Header */}
              <div className="p-2 border-b border-gray-100 flex items-center text-sm">
                <input
                  type="checkbox"
                  id={`store-panel-${storeId}`}
                  className="mr-2"
                  checked={areAllStoreItemsSelected(storeId)}
                  onChange={(e) => toggleSelectAllStore(storeId, e.target.checked)}
                />
                <label htmlFor={`store-panel-${storeId}`} className="font-medium mr-2">{items[0]?.translatedStoreName || items[0]?.storeName || t("UNKNOWN_STORE")}</label>
              </div>

              <div className="divide-y divide-gray-100">
                {items.map((item) => (
                  <div key={`${item.productId}-${item.sku}`} className="flex items-center p-3 space-x-3">
                    <input
                      type="checkbox"
                      id={`item-panel-${item.productId}-${item.sku}`}
                      className="mr-1 self-start mt-1"
                      checked={item.selected}
                      onChange={() => toggleItemSelection(item.productId, item.sku)}
                    />
                    <Image
                      src={item.imageUrl.startsWith('//') ? `https:${item.imageUrl}` : item.imageUrl || '/images/placeholder.png'}
                      alt={item.title || 'Product Image'}
                      width={64}
                      height={64}
                      className="rounded border border-gray-200 object-cover flex-shrink-0"
                    />
                    <div className="flex-grow min-w-0">
                      <a
                        href={`/product/${item.productId}`}
                        className="text-xs font-medium mb-1 truncate block hover:text-blue-600 hover:underline"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent checkbox toggle when clicking the link
                          toggleCartPanel(); // Close cart panel when navigating
                        }}
                      >
                        {item.title}
                      </a>
                      <p className="text-xs text-gray-500 mb-1 truncate">{item.sku}</p>
                       <div className="flex items-center justify-between">
                         <div>
                            <span className="text-xs font-semibold">{formatPrice(item.price)}</span>
                            {item.originalPrice && item.originalPrice > item.price && (
                              <span className="text-xs text-gray-400 line-through ml-2">{formatPrice(item.originalPrice)}</span>
                            )}
                            {item.originalPrice && item.originalPrice > item.price && (
                              <span className="text-xs text-green-600 ml-2">{t("SAVE")}: {formatPrice(item.originalPrice - item.price)}</span>
                            )}
                         </div>
                       </div>
                    </div>
                     <div className="flex flex-col items-end space-y-1 flex-shrink-0">
                        <div className="flex items-center border border-gray-300 rounded">
                            <Button
                            variant="ghost"
                            size="sm"
                            className="px-1 h-6 rounded-r-none border-r border-gray-300"
                            onClick={() => updateQuantity(item.productId, item.sku, item.quantity - 1)}
                            disabled={item.quantity <= 1}
                            >
                            -
                            </Button>
                            <Input
                            type="text"
                            inputMode="numeric"
                            value={item.quantity}
                            onChange={(e) => {
                              // Only allow numeric input
                              const inputValue = e.target.value;
                              if (/^\d*$/.test(inputValue)) {
                                if (inputValue === '') {
                                  updateQuantity(item.productId, item.sku, 1);
                                } else {
                                  const newQuantity = Math.max(1, parseInt(inputValue, 10));
                                  updateQuantity(item.productId, item.sku, newQuantity);
                                }
                              }
                            }}
                            className="w-12 h-6 text-center border-none focus-visible:ring-0 rounded-none text-xs [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                            />
                            <Button
                            variant="ghost"
                            size="sm"
                            className="px-1 h-6 rounded-l-none border-l border-gray-300"
                            onClick={() => updateQuantity(item.productId, item.sku, item.quantity + 1)}
                            >
                            +
                            </Button>
                        </div>
                        <Button variant="ghost" size="sm" className="text-gray-400 hover:text-red-600 h-6 px-1" onClick={() => removeItem(item.productId, item.sku)}>
                            <Trash2 className="h-3 w-3" />
                        </Button>
                     </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Cart Summary Footer */}
        <SheetFooter className="mt-auto border-t pt-4">
          <div className="w-full flex justify-between items-center">
            <div className="text-left">
                 <p className="text-sm font-medium">
                    {t("SUBTOTAL")} ({summary.selectedItemCount} {t("ITEMS")})
                 </p>
                 {summary.discount > 0 && (
                     <p className="text-xs text-green-600">{t("SAVINGS")}: {formatPrice(summary.discount)}</p>
                 )}
            </div>
            <div className="flex items-center space-x-2">
                 <div className="text-right">
                    <span className="text-lg font-bold text-orange-600 block">{formatPrice(summary.total)}</span>
                 </div>
                 {/* Use SheetClose for the checkout button, add onClick for navigation */}
                 <SheetClose asChild>
                    <Button
                        className="bg-orange-500 hover:bg-orange-600"
                        disabled={summary.selectedItemCount === 0}
                        onClick={() => router.push('/checkout')} // Navigate on click
                    >
                        {tCommon("CHECKOUT")}
                    </Button>
                 </SheetClose>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
