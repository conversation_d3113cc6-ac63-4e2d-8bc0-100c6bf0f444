'use client';

import { useEffect, useState } from 'react';
import { AlertCircle } from 'lucide-react';
import adminConfig from '../../../adminConfig.mjs';
import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';

interface RateLimitToastProps {
  hourlyRemaining?: number;
  dailyRemaining?: number;
}

export function RateLimitToast({ hourlyRemaining, dailyRemaining }: RateLimitToastProps) {
  const [visible, setVisible] = useState(false);
  const t = useTranslations(I18NNamespace.COMPONENTS_RATE_LIMIT_TOAST);

  useEffect(() => {
    // Get rate limit configuration
    const { limits, toastThresholds, endpointLimits } = adminConfig.rateLimiting;

    // Determine which limits to use (default to global limits)
    let currentLimits = limits;

    // For search API, use endpoint-specific limits if available
    // This is a simplification - in a real app, you'd determine which endpoint was used
    if (window.location.pathname.includes('/search') && endpointLimits && endpointLimits['/api/search']) {
      currentLimits = endpointLimits['/api/search'];
    }

    // Calculate threshold values based on percentage
    const hourlyThreshold = Math.floor(currentLimits.hourly * (toastThresholds.hourly / 100));
    const dailyThreshold = Math.floor(currentLimits.daily * (toastThresholds.daily / 100));

    // Show toast if user is approaching rate limits
    if (
      (hourlyRemaining !== undefined && hourlyRemaining < hourlyThreshold) ||
      (dailyRemaining !== undefined && dailyRemaining < dailyThreshold)
    ) {
      setVisible(true);

      // Hide toast after 5 seconds
      const timer = setTimeout(() => {
        setVisible(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [hourlyRemaining, dailyRemaining]);

  if (!visible) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-md bg-amber-50 border border-amber-200 rounded-lg shadow-lg p-4 text-amber-800 animate-in fade-in slide-in-from-bottom-5">
      <div className="flex items-start">
        <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 text-amber-500" />
        <div>
          <h3 className="font-medium">{t("LIMIT_APPROACHING")}</h3>
          <p className="text-sm mt-1">
            {hourlyRemaining !== undefined && hourlyRemaining < Math.floor(adminConfig.rateLimiting.limits.hourly * (adminConfig.rateLimiting.toastThresholds.hourly / 100)) && (
              <span className="block">{t("HOURLY_REMAINING", {hourlyRemaining})}</span>
            )}
            {dailyRemaining !== undefined && dailyRemaining < Math.floor(adminConfig.rateLimiting.limits.daily * (adminConfig.rateLimiting.toastThresholds.daily / 100)) && (
              <span className="block">{t("DAILY_REMAINING", {dailyRemaining})}</span>
            )}
          </p>
          <p className="text-xs mt-2">
            {window.location.pathname.includes('/search') && adminConfig.rateLimiting.endpointLimits && adminConfig.rateLimiting.endpointLimits['/api/search'] ? (
              <>{t("FREE_ACCNT", { hourly: adminConfig.rateLimiting.endpointLimits['/api/search'].hourly, daily: adminConfig.rateLimiting.endpointLimits['/api/search'].daily })}</>
            ) : (
              <>{t("FREE_ACCNT", { hourly: adminConfig.rateLimiting.limits.hourly, daily: adminConfig.rateLimiting.limits.daily })}</>
            )}
          </p>
        </div>
      </div>
    </div>
  );
}
