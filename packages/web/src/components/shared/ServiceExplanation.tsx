"use client";

import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';
import Image from 'next/image';

export function ServiceExplanation() {
  const t = useTranslations(I18NNamespace.COMPONENTS_SERVICE_EXPLAN);
  const services = [
    {
      icon: "/images/service-icons/purchase-orders.svg",
      title: t("PURCHASE_ORDERS"),
      description: t("PURCHASE_ORDERS_DESC")
    },
    {
      icon: "/images/service-icons/warehouse.svg",
      title: t("SHIP_TO_WAREHOUSE"),
      description: t("SHIP_TO_WAREHOUSE_DESC")
    },
    {
      icon: "/images/service-icons/quality-check.svg",
      title: t("QUALITY_CHECK"),
      description: t("QUALITY_CHECK_DESC")
    },
    {
      icon: "/images/service-icons/global-shipping.svg",
      title: t("GLOBAL_SHIPPING"),
      description: t("GLOBAL_SHIPPING_DESC")
    }
  ];

  return (
    <section className="py-12 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl font-bold text-center mb-12">{t("ALL_SERV")}</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <div key={index} className="flex flex-col items-center">
              <div className="mb-4 w-16 h-16 flex items-center justify-center">
                <Image 
                  src={service.icon} 
                  alt={service.title}
                  width={54}
                  height={54}
                />
              </div>
              <h3 className="text-lg font-semibold mb-2 text-center">{service.title}</h3>
              <p className="text-sm text-gray-600 text-center">{service.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
