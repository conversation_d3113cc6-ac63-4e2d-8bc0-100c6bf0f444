"use client";

import { useState } from "react";
import { ChevronRight } from "lucide-react";

export function CategoryTabs() {
  const [activeCategory, setActiveCategory] = useState("Featured");

  const categories = [
    "Featured",
    "Consumer Electronics",
    "Apparel & Accessories",
    "Beauty",
    "Packaging & Printing",
    "MRO",
    "Jewelry, Eyewear & Watches",
    "Home & Garden"
  ];

  return (
    <div className="flex overflow-x-auto scrollbar-hide py-4">
      <div className="flex space-x-3 mx-auto">
        {categories.map((category) => (
          <button
            key={category}
            className={`whitespace-nowrap px-4 py-2 text-sm rounded-full border transition-all ${
              activeCategory === category
                ? "bg-black text-white border-black"
                : "bg-white text-gray-700 border-gray-300 hover:border-gray-500"
            }`}
            onClick={() => setActiveCategory(category)}
          >
            {category}
          </button>
        ))}
        <button className="whitespace-nowrap px-3 py-2 text-sm rounded-full border border-gray-300 bg-white text-gray-600 hover:border-gray-500 flex items-center">
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}
