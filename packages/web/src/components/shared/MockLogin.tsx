"use client";

import { useState } from 'react';
import { useAuth, type User } from '@/lib/auth-context';
import { But<PERSON> } from '@/components/ui/button';

const sampleUsers: User[] = [
  {
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    plan: 'Pro',
    avatar: '/avatars/mingze.jpg'
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    plan: 'Free'
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    plan: 'Enterprise',
    avatar: '/avatars/jane.jpg'
  }
];

export function MockLogin() {
  const { user, login, logout, isAuthenticated } = useAuth();
  const [loginOpen, setLoginOpen] = useState(false);

  if (isAuthenticated) {
    return (
      <div className="fixed top-20 right-4 bg-white shadow-lg rounded-lg p-4 z-50 border border-gray-200">
        <p className="mb-2 text-sm">Logged in as <strong>{user?.name}</strong></p>
        <Button
          variant="destructive"
          className="w-full"
          onClick={logout}
        >
          Log Out
        </Button>
      </div>
    );
  }

  return (
    <>
      <Button
        className="fixed top-5 right-4 z-50 text-sm"
        variant="outline"
        onClick={() => setLoginOpen(!loginOpen)}
      >
        Mock Login
      </Button>

      {loginOpen && (
        <div className="fixed top-16 right-4 bg-white shadow-lg rounded-lg p-4 z-50 border border-gray-200">
          <h3 className="font-medium mb-2">Select a user</h3>
          <div className="space-y-2">
            {sampleUsers.map((sampleUser) => (
              <div
                key={sampleUser.email}
                className="p-2 border rounded-md hover:bg-gray-50 cursor-pointer"
                onClick={() => {
                  login(sampleUser);
                  setLoginOpen(false);
                }}
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-gray-200 rounded-full mr-2 flex-shrink-0 flex items-center justify-center overflow-hidden">
                    {sampleUser.avatar ? (
                      <img src={sampleUser.avatar} alt={sampleUser.name} className="w-full h-full object-cover" />
                    ) : (
                      <span className="text-xs font-medium">{sampleUser.name.substring(0, 2).toUpperCase()}</span>
                    )}
                  </div>
                  <div>
                    <p className="text-sm font-medium">{sampleUser.name}</p>
                    <p className="text-xs text-gray-500">{sampleUser.email}</p>
                  </div>
                  <span className={`ml-2 px-2 py-1 text-xs rounded ${
                    sampleUser.plan === 'Pro' ? 'bg-blue-100 text-blue-800' :
                    sampleUser.plan === 'Enterprise' ? 'bg-purple-100 text-purple-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {sampleUser.plan}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  );
}
