"use client";

import React, { useState, useRef, useEffect, useMemo } from "react";
import { Search, Image, X, ChevronDown, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import NextImage from "next/image";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { RateLimitToast } from "./RateLimitToast";
import { validateImage } from "@/utils/image-validator";
import { compressImage } from "@/utils/image-compressor";
import adminConfig from "../../../adminConfig.mjs";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

interface SearchBoxProps {
  onSearch?: (query: string, image?: File, model?: string, modelId?: string) => void;
  defaultQuery?: string;
}

export function SearchBox({ onSearch, defaultQuery = "" }: SearchBoxProps) {
  const [searchQuery, setSearchQuery] = useState(defaultQuery);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [placeholderText, setPlaceholderText] = useState("");
  const [currentPlaceholder, setCurrentPlaceholder] = useState<number>(0);
  const [isTyping, setIsTyping] = useState(true); // true for typing in, false for typing out
  const [charIndex, setCharIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isImageSearching, setIsImageSearching] = useState(false); // Track image search loading state
  // Default to GPT-4o mini (free) as requested
  const [selectedModel, setSelectedModel] = useState<string>("GPT-4o mini (free)");
  const [selectedModelId, setSelectedModelId] = useState<string>("gpt-4o mini"); // Track model ID as well
  const [hourlyRemaining, setHourlyRemaining] = useState<number | undefined>(undefined);
  const [dailyRemaining, setDailyRemaining] = useState<number | undefined>(undefined);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [compressionInfo, setCompressionInfo] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const t = useTranslations(I18NNamespace.COMPONENTS_SEARCHBOX);

  // Define the placeholder texts and their corresponding logos
  const [placeholders, setPlaceholders] = useState<{text: string, logo: string}[]>([]);

  // Effect to auto-resize textarea when content changes
  useEffect(() => {
    if (textareaRef.current && searchQuery) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.max(48, textareaRef.current.scrollHeight)}px`;
    }
  }, [searchQuery]);

  useEffect(() => {
    setPlaceholders([
      { text: "Add a product picture to find similar products", logo: "/images/screenshot.png" },
      { text: "Source from TAOBAO: https://item.taobao.com/item.htm", logo: "/images/taobao.svg" },
      { text: "I want to order 5,000 t-shirts with my custom logo", logo: "/images/tshirt.png" },
      { text: "Source from Alibaba: https://detail.alibaba.com/ ", logo: "/images/alibaba.svg" }

    ]);
  }, []);

  // Effect to update searchQuery when defaultQuery changes
  useEffect(() => {
    setSearchQuery(defaultQuery);
  }, [defaultQuery]);

  // Effect for image preview
  useEffect(() => {
    if (!selectedImage) {
      setPreviewUrl(null);
      return;
    }

    const objectUrl = URL.createObjectURL(selectedImage);
    setPreviewUrl(objectUrl);

    return () => URL.revokeObjectURL(objectUrl);
  }, [selectedImage]);

  // Effect for typing animation
  useEffect(() => {
    const typingSpeed = 20; // milliseconds per character (even faster typing)
    const pauseTime = 1200; // time to pause when text is fully typed

    const animateTyping = () => {
      if (isAnimating) return; // Prevent multiple animations from running simultaneously

      // Safety check to ensure currentPlaceholder is valid
      if (currentPlaceholder < 0 || currentPlaceholder >= placeholders.length) {
        console.error("Invalid currentPlaceholder index:", currentPlaceholder);
        setCurrentPlaceholder(0);
        return;
      }

      const currentText = placeholders[currentPlaceholder]?.text || "";

      if (!currentText) {
        console.error("No text found for placeholder at index:", currentPlaceholder);
        return;
      }

      if (isTyping) {
        // Typing in
        if (charIndex < currentText.length) {
          setIsAnimating(true);
          setPlaceholderText(currentText.substring(0, charIndex + 1));
          setCharIndex(charIndex + 1);
          setIsAnimating(false);
        } else {
          // Finished typing in, pause before typing out
          setIsAnimating(true);
          setTimeout(() => {
            setIsTyping(false);
            setIsAnimating(false);
          }, pauseTime);
        }
      } else {
        // Typing out
        if (charIndex > 0) {
          setIsAnimating(true);
          setPlaceholderText(currentText.substring(0, charIndex - 1));
          setCharIndex(charIndex - 1);
          setIsAnimating(false);
        } else {
          // Finished typing out, wait until completely empty before changing logo
          setIsAnimating(true);
          // Only change the logo and start new text after the current text is completely gone
          setTimeout(() => {
            setCurrentPlaceholder((prev) => {
              const nextIndex = (prev + 1) % placeholders.length;
              return nextIndex;
            });
            // Small delay after changing logo before starting to type
            setTimeout(() => {
              setIsTyping(true);
              setIsAnimating(false);
            }, 150);
          }, 100);
        }
      }
    };

    const typingInterval = setInterval(animateTyping, typingSpeed);
    return () => clearInterval(typingInterval);
  }, [charIndex, isTyping, currentPlaceholder, placeholders, isAnimating]);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch) {
      // Set loading state if doing image search
      if (selectedImage) {
        setIsImageSearching(true);
      }

      // Include the selected model in the search
      // Check if we're using an AI model
      // Since we've temporarily commented out the "No AI" option, this check will rarely be true
      const isNoAIModel = selectedModelId === "auto" || selectedModel === "No AI (best)";

      // Only check rate limits if using an AI model
      if (!isNoAIModel) {
        console.log(`Checking rate limits for AI model: ${selectedModel}`);
        try {
          // Make a HEAD request to the API to check rate limits
          const response = await fetch('/api/ai-analysis', {
            method: 'HEAD',
          });

          // Get rate limit headers
          const hourlyLimit = response.headers.get('X-RateLimit-Limit-Hour');
          const hourlyRemaining = response.headers.get('X-RateLimit-Remaining-Hour');
          const dailyLimit = response.headers.get('X-RateLimit-Limit-Day');
          const dailyRemaining = response.headers.get('X-RateLimit-Remaining-Day');

          // Update state with rate limit information
          if (hourlyRemaining) setHourlyRemaining(parseInt(hourlyRemaining));
          if (dailyRemaining) setDailyRemaining(parseInt(dailyRemaining));

          console.log(`Rate limits: ${hourlyRemaining}/${hourlyLimit} hourly, ${dailyRemaining}/${dailyLimit} daily`);
        } catch (error) {
          console.warn('Failed to check rate limits:', error);
          // Silently handle error - rate limits will not be displayed
        }
      } else {
        console.log('Skipping rate limit check: No AI model selected');
      }

      try {
        // Proceed with search - pass both model name and ID
        await onSearch(searchQuery, selectedImage || undefined, selectedModel, selectedModelId);

        // Save search query to recent searches
        if (searchQuery.trim()) {
          const searches: string[] = JSON.parse(localStorage.getItem('recentSearches') || '[]');
          // Remove duplicate if exists and add to beginning
          const updatedSearches = [
            searchQuery,
            ...searches.filter((s: string) => s !== searchQuery)
          ].slice(0, 10);
          localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
          // Save selected model for this search
          localStorage.setItem(`model_${searchQuery}`, selectedModel);
          // Dispatch event to notify Navbar of update
          window.dispatchEvent(new Event('storage'));
        }
      } catch (error) {
        console.error('Search error:', error);
      } finally {
        // Reset loading state
        setIsImageSearching(false);
      }
    }
  };

  const handleImageClick = () => {
    if (validationError) setValidationError(null); // Clear validation error when clicking image upload
    fileInputRef.current?.click();
  };

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Check file format first before attempting compression
      const validFormats = ['image/png', 'image/jpeg', 'image/jpg'];
      if (!validFormats.includes(file.type)) {
        const errorMessage = t("IMAGE_FORMAT_ERROR") || "Invalid image format. Please use PNG or JPEG/JPG.";
        setValidationError(errorMessage);

        // Reset the file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      try {
        // Set loading state
        setIsImageSearching(true);

        // Get original file info for logging
        const originalSizeMB = (file.size / (1024 * 1024)).toFixed(2);
        console.log(`Original image: ${originalSizeMB}MB`);

        // Compress the image if needed
        const compressedImageResult = await compressImage(file);
        const processedFile = compressedImageResult.file;

        // No need to validate size or dimensions anymore, just format
        // which we already checked above

        // Reset loading state
        setIsImageSearching(false);

        // If we got here, validation passed, proceed with setting the image
        setSelectedImage(processedFile);
        setSearchQuery(""); // Clear text search when image is selected

        // Create a preview URL for the image
        const reader = new FileReader();
        reader.onloadend = () => {
          setPreviewUrl(reader.result as string);
        };
        reader.readAsDataURL(processedFile);

        // Show compression info if the image was compressed
        if (compressedImageResult.wasCompressed) {
          const originalSizeKB = (compressedImageResult.originalSize / 1024).toFixed(1);
          const compressedSizeKB = (compressedImageResult.compressedSize / 1024).toFixed(1);
          const compressionPercent = Math.round((1 - compressedImageResult.compressedSize / compressedImageResult.originalSize) * 100);

          const infoMessage = t("IMAGE_COMPRESSED") ||
            `Image automatically compressed: ${originalSizeKB}KB → ${compressedSizeKB}KB (${compressionPercent}% smaller)`;

          setCompressionInfo(infoMessage);

          console.log(`Image compressed: ${originalSizeKB}KB → ${compressedSizeKB}KB (${compressionPercent}% smaller)`);
          console.log(`Dimensions: ${compressedImageResult.originalWidth}x${compressedImageResult.originalHeight} → ${compressedImageResult.compressedWidth}x${compressedImageResult.compressedHeight}`);
        } else {
          setCompressionInfo(null);
        }
      } catch (error) {
        // Reset loading state
        setIsImageSearching(false);

        console.error('Error processing image:', error);
        setValidationError(t("IMAGE_FORMAT_ERROR") || "Invalid image format. Please use PNG or JPEG/JPG.");

        // Reset the file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      }
    }
  };

  const removeImage = () => {
    setSelectedImage(null);
    setPreviewUrl(null);
    setCompressionInfo(null);

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto bg-white rounded-lg shadow-lg">
      {/* Rate limit toast notification */}
      <RateLimitToast
        hourlyRemaining={hourlyRemaining}
        dailyRemaining={dailyRemaining}
      />
      <form onSubmit={handleSearch} className="flex flex-col w-full">
        <div className="relative w-full flex items-center">
          {/* Source logo */}
          <div className="absolute left-3 sm:left-4 top-4 sm:top-1/2 sm:transform sm:-translate-y-1/2 flex items-center justify-center">
            <div className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center mr-1 sm:mr-2">
              {placeholders[currentPlaceholder] && (
                <NextImage
                  src={placeholders[currentPlaceholder].logo}
                  alt="Source logo"
                  width={24}
                  height={24}
                  className="object-contain w-5 h-5 sm:w-6 sm:h-6"
                  style={{ verticalAlign: 'middle' }}
                />
              )}
            </div>
          </div>

          <textarea
            ref={textareaRef}
            className="w-full min-h-[72px] sm:min-h-[56px] pl-14 sm:pl-16 pr-12 sm:pr-20 rounded-lg text-sm sm:text-base placeholder:text-gray-400 border-0 focus:ring-0 focus:outline-none resize-none overflow-hidden py-3 sm:py-4"
            placeholder={placeholderText}
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              if (e.target.value) setSelectedImage(null); // Clear image when typing
              if (validationError) setValidationError(null); // Clear validation error when typing

              // Auto-resize the textarea based on content
              e.target.style.height = 'auto';
              e.target.style.height = `${Math.max(72, e.target.scrollHeight)}px`;
            }}
            onClick={() => {
              if (validationError) setValidationError(null); // Clear validation error when clicking
            }}
            rows={2}
          />

          <div className="absolute right-2 sm:right-3 bottom-2 sm:bottom-3 flex items-center space-x-1 sm:space-x-2">
            {/* Model Selection Dropdown - Now visible on mobile */}
            <div className="block">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <div className="flex items-center bg-gray-100 rounded-lg px-2 sm:px-3 py-1 sm:py-1.5 cursor-pointer hover:bg-gray-200 transition-colors text-xs sm:text-sm">
                    <span className="inline mr-1 max-w-[120px] sm:max-w-none truncate">{selectedModel}</span>
                    <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4 text-gray-700 flex-shrink-0" />
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {adminConfig.search.models.map((model) => (
                    <DropdownMenuItem
                      key={model.id}
                      onClick={() => {
                        setSelectedModel(model.name);
                        setSelectedModelId(model.id);
                      }}
                    >
                      {model.name}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Image Upload Button */}
            <div className="relative">
              <div
                className="flex items-center justify-center bg-gray-100 rounded-lg px-2 py-1 sm:px-3 sm:py-1.5 cursor-pointer hover:bg-gray-200 transition-colors"
                onClick={handleImageClick}
              >
                <Image className="w-4 h-4 sm:w-5 sm:h-5 text-gray-700 mr-1" />
                <span className="text-xs sm:text-sm text-gray-700">{t("UPLOAD_IMAGE")}</span>
              </div>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleImageChange}
                accept="image/*"
                className="hidden"
              />
            </div>

            {/* Search Button with Text */}
            <Button
              type="submit"
              className="bg-accio-primary text-white rounded-full w-8 h-8 sm:h-auto sm:w-auto sm:px-4 sm:py-2 flex items-center justify-center"
              disabled={isImageSearching}
            >
              {isImageSearching ? (
                <>
                  <div className="hidden sm:inline mr-2 text-sm">{t("SEARCHING")}</div>
                  <div className="w-4 h-4 sm:w-5 sm:h-5 rounded-full border-2 border-white border-t-transparent animate-spin" />
                </>
              ) : (
                <>
                  <span className="hidden sm:inline mr-2 text-sm">{t("SOURCE")}</span>
                  <Search className="w-4 h-4 sm:w-5 sm:h-5" />
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Show validation error message if there is one */}
        {validationError && (
          <div className="p-3 sm:p-4 border-t border-red-100 bg-red-50">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-red-500" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-600">
                  {validationError}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Show image preview if available */}
        {previewUrl && (
          <div className="relative p-3 sm:p-4 border-t border-gray-100">
            <div className="flex items-start">
              <div className="relative w-24 h-24 sm:w-32 sm:h-32">
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="w-full h-full object-contain rounded-md border border-gray-200"
                />
                <button
                  type="button"
                  onClick={removeImage}
                  className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow-md hover:bg-gray-100"
                >
                  <X className="w-3 h-3 sm:w-4 sm:h-4 text-gray-700" />
                </button>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm text-gray-700 font-medium">{t("IMAGE_SEARCH")}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {t("SEARCH_SIMILAR_PROD")}
                </p>

                {/* Show compression info if available */}
                {compressionInfo && (
                  <p className="text-xs text-green-600 mt-1 italic">
                    {compressionInfo}
                  </p>
                )}

                <div className="mt-2">
                  <Button
                    type="submit"
                    className="bg-accio-primary text-white rounded-full px-3 py-1 text-xs flex items-center"
                    disabled={isImageSearching}
                  >
                    {isImageSearching ? (
                      <>
                        <div className="w-3 h-3 mr-1 rounded-full border-2 border-white border-t-transparent animate-spin" />
                        {t("SEARCHING")}
                      </>
                    ) : (
                      <>
                        <Search className="w-3 h-3 mr-1" />
                        {t("SEARCH_WITH_IMAGE")}
                      </>
                    )}
                  </Button>
                  {isImageSearching && (
                    <p className="text-xs text-gray-500 mt-2">
                      {t("SEARCHING_SIMILAR_PROD")}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
}
