"use client";

import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import { useEffect, useRef } from "react";

export function PartnerLogos() {
  const scrollRef = useRef<HTMLDivElement>(null);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  // Define real partner logos
  const partners = [
    {
      id: "openai",
      name: "OpenAI",
      logo: "/images/logos/openai-logo.svg",
      alt: "OpenAI Logo",
      bgColor: "bg-white"
    },
    {
      id: "deepseek",
      name: "DeepSeek",
      logo: "/images/logos/deepseek-logo.svg",
      alt: "DeepSeek Logo",
      bgColor: "bg-white"
    },
    {
      id: "amazon",
      name: "Amazon",
      logo: "/images/logos/amazon-logo.svg",
      alt: "Amazon Logo",
      bgColor: "bg-white"
    },
    {
      id: "tiktokshop",
      name: "TikTok Shop",
      logo: "/images/logos/tiktokshop-logo.png",
      alt: "TikTok Shop Logo",
      bgColor: "bg-white"
    },
    {
      id: "taobao",
      name: "Taobao",
      logo: "/images/logos/taobao-logo.svg",
      alt: "Taobao Logo",
      bgColor: "bg-white"
    },
    {
      id: "alibaba",
      name: "Alibaba",
      logo: "/images/logos/alibaba-logo.svg",
      alt: "Alibaba Logo",
      bgColor: "bg-white"
    },
    {
      id: "shopee",
      name: "Shopee",
      logo: "/images/logos/shopee-logo.svg",
      alt: "Shopee Logo",
      bgColor: "bg-white"
    },
    {
      id: "aliexpress",
      name: "AliExpress",
      logo: "/images/logos/aliexpress-logo.svg",
      alt: "AliExpress Logo",
      bgColor: "bg-white"
    }
  ];

  // Duplicate the partners array multiple times to create a seamless infinite scroll effect
  // Using more duplicates ensures we have enough logos to fill the entire viewport width
  const allPartners = [...partners, ...partners, ...partners, ...partners];

  // Auto-scroll animation using CSS animation instead of JavaScript
  useEffect(() => {
    const scrollContainer = scrollRef.current;
    if (!scrollContainer) return;

    // Calculate the total width of all logos for animation
    const totalWidth = scrollContainer.scrollWidth / 2; // Half because we duplicated the logos

    // Apply the animation directly to the scrolling content
    const scrollContent = scrollContainer.querySelector('.scrolling-content');
    if (scrollContent && scrollContent instanceof HTMLElement) {
      // Set animation duration based on content width (longer content = slower scroll)
      const duration = Math.max(20, totalWidth / 50); // Minimum 20s, scales with content

      // Apply the animation
      scrollContent.style.animationDuration = `${duration}s`;
      scrollContent.classList.add('animate-scroll');
    }

    // Pause animation on hover
    const handleMouseEnter = () => {
      const scrollContent = scrollContainer.querySelector('.scrolling-content');
      if (scrollContent && scrollContent instanceof HTMLElement) {
        scrollContent.style.animationPlayState = 'paused';
      }
    };

    const handleMouseLeave = () => {
      const scrollContent = scrollContainer.querySelector('.scrolling-content');
      if (scrollContent && scrollContent instanceof HTMLElement) {
        scrollContent.style.animationPlayState = 'running';
      }
    };

    scrollContainer.addEventListener('mouseenter', handleMouseEnter);
    scrollContainer.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener('mouseenter', handleMouseEnter);
        scrollContainer.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, []);

  return (
    <div className="py-8">
       <h2 className="text-2xl font-semibold text-center mb-6">{tCommon("PARTENERED_WITH_TOP")}</h2>

      {/* Scrolling container */}
      <div
        ref={scrollRef}
        className="overflow-hidden relative mx-auto max-w-[90vw]"
      >
        {/* Gradient overlay for fade effect on edges */}
        <div className="absolute left-0 top-0 bottom-0 w-16 bg-gradient-to-r from-white to-transparent z-10"></div>
        <div className="absolute right-0 top-0 bottom-0 w-16 bg-gradient-to-l from-white to-transparent z-10"></div>

        {/* Scrolling content */}
        <div className="scrolling-content flex items-center py-4 px-8 whitespace-nowrap overflow-x-auto scrollbar-hide">
          {allPartners.map((partner, index) => (
            <div
              key={`${partner.id}-${index}`}
              className={`flex-shrink-0 h-16 w-32 px-4 ${partner.bgColor} rounded-lg border border-gray-100 shadow-sm flex items-center justify-center mx-2`}
            >
              {/* Fallback for missing logos */}
              {partner.logo ? (
                <img
                  src={partner.logo}
                  alt={partner.alt}
                  className="h-8 max-w-[120px] object-contain"
                  // Fallback for missing images
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.onerror = null;
                    target.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='30' viewBox='0 0 120 30'%3E%3Crect width='120' height='30' fill='%23f1f1f1'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='12' text-anchor='middle' dominant-baseline='middle' fill='%23888'%3E" + partner.name + "%3C/text%3E%3C/svg%3E";
                  }}
                />
              ) : (
                <div className="text-gray-500 font-medium">{partner.name}</div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Add custom styles for scrolling animation */}
      <style jsx global>{`
        .scrollbar-hide {
          -ms-overflow-style: none;  /* IE and Edge */
          scrollbar-width: none;  /* Firefox */
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;  /* Chrome, Safari and Opera */
        }

        .scrolling-content {
          display: flex;
          animation: scroll-logos 60s linear infinite;
          animation-play-state: running;
          width: fit-content; /* Ensure the container fits all logos */
        }

        @keyframes scroll-logos {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(calc(-25%)); /* Move by 1/4 of the content since we have 4 sets */
          }
        }

        .animate-scroll {
          animation-name: scroll-logos;
        }
      `}</style>
    </div>
  );
}
