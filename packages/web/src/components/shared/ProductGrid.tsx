"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import Link from "next/link";
import { FileText, Search, Image, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { formatPrice } from "@/utils/currency";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import { isChinese, useLanguage } from "@/lib/language-context";

// Define types for trending searches
interface TrendingSearch {
  _id: string;
  originalQuery: string;
  searchType: 'text' | 'image';
  aiModel?: string | null;
  displayData: TrendingProduct[];
  createdAt: string;
}

interface TrendingProduct {
  id: string;
  title: string;
  price: string;
  image: string;
  detailUrl: string;
}

export function ProductGrid() {
  const [trendingProducts, setTrendingProducts] = useState<TrendingProduct[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { language } = useLanguage();
  const t = useTranslations(I18NNamespace.COMPONENTS_PRODUCT_GRID);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  // Function to generate a background color based on a string
  const getBackgroundColor = (str: string) => {
    const colors = [
      "bg-indigo-100", "bg-purple-100", "bg-orange-100",
      "bg-amber-100", "bg-emerald-100", "bg-blue-100",
      "bg-green-100", "bg-red-100", "bg-pink-100", "bg-yellow-100"
    ];

    // Simple hash function to get a consistent color for the same string
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }

    return colors[Math.abs(hash) % colors.length];
  };

  // Helper function to determine the product source
  const getProductSource = (product: any) => {
    // First check if the product has a source property
    if (product.source) {
      return product.source;
    }

    // If not, try to determine from the detailUrl
    if (product.detailUrl) {
      if (product.detailUrl.includes('taobao')) {
        return 'taobao';
      } else if (product.detailUrl.includes('alibaba')) {
        return 'alibaba';
      } else if (product.detailUrl.includes('1688')) {
        return '1688';
      }
    }

    // Default to alibaba if we can't determine the source
    return 'alibaba';
  };

  // Fetch trending searches on component mount
  useEffect(() => {
    const fetchTrendingData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('https://micco-cashback.s3.eu-west-3.amazonaws.com/trending/trending_translated.json');

        if (!response.ok) {
          throw new Error(`Error fetching trending data: ${response.status}`);
        }

        const data = await response.json() as { displayData: TrendingProduct[] } | null;
        const displayData = data?.displayData;
        if (displayData) {
          setTrendingProducts(displayData);
        }
      } catch (err: any) {
        console.error('Failed to fetch trending data:', err);
        console.error(err);
        setError(err.message || t("FAILED_TOLOAD_TRENDING"));
      } finally {
        setIsLoading(false);
      }
    };

    fetchTrendingData();
  }, []);

  // If there's no data yet, show fallback content
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        <span className="ml-2 text-gray-500">{t("LOADING_TRENDING")}</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500 mb-4">{error}</p>
        <Button onClick={() => window.location.reload()}>{tCommon("RETRY")}</Button>
      </div>
    );
  }

  // If we have no trending products, show a message
  if (trendingProducts.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 mb-4">{t("NO_TRENDING")}</p>
        <p className="text-gray-400 text-sm">{t("TRY_SEARCHING")}</p>
      </div>
    );
  }

  const titleProperty = isChinese(language) ? 'title' : `title-${language}`;

  return (
    <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
      {trendingProducts.slice(0, 20).map((product) => {
        // Determine the icon based on search type
        const icon = <Search className="h-5 w-5 text-green-600" />; // For Image <Image className="h-5 w-5 text-purple-600" />;

        return (
          <Card
            key={product.id}
            className="overflow-hidden hover:shadow-md transition-shadow cursor-pointer border border-gray-200 rounded-lg"
          >
            <Link
              href={`/product/${product.id}?source=${getProductSource(product)}&referrerSource=${getProductSource(product)}`}
              data-source={getProductSource(product)}
            >
              <div className={`aspect-square relative overflow-hidden ${product.image ? '' : getBackgroundColor(product.title)}`}>
                {product.image ? (
                  <img
                    src={product.image}
                    alt={product.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="w-full h-full absolute inset-0" />
                  </div>
                )}
              </div>
              <div className="p-3">
                <h3 className="font-medium text-sm line-clamp-2 mb-2">
                  {product[titleProperty as 'title'] || product.title}
                </h3>
                <div className="flex items-center justify-between">
                  <div className="flex items-center rounded-full bg-gray-100 py-0.5 px-2">
                    {icon}
                    <span className="text-xs ml-1">{formatPrice(parseFloat(product.price))}</span>
                  </div>
                </div>
              </div>
            </Link>
          </Card>
        );
      })}
    </div>
  );
}
