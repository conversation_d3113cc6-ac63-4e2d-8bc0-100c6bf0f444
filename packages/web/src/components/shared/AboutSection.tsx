"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

export function AboutSection() {
  const t = useTranslations(I18NNamespace.COMPONENTS_ABOUT_SEC);
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
      <div>
        <h3 className="text-2xl font-bold mb-4">{t("ABOUT")}</h3>
        <p className="text-gray-600 mb-4">
          {t("WELCOME")}
        </p>
        <p className="text-gray-600 mb-4">
          {t("LEADING")}
        </p>
        <p className="text-gray-600 mb-6">
          {t("WITH_INSTANT")}
        </p>
        <Button className="accio-btn">
          {t("LEARN_MORE")}
        </Button>
      </div>

      <div className="bg-gray-100 rounded-lg p-8">
        <h4 className="text-xl font-semibold mb-4">{t("AI")}</h4>
        <ul className="space-y-3">
          <li className="flex items-start">
            <div className="w-6 h-6 rounded-full bg-accio-primary mr-3 mt-0.5 flex items-center justify-center text-white font-bold text-xs">1</div>
            <div>
              <p className="font-medium">{t("B2B")}</p>
              <p className="text-gray-600 text-sm">{t("FINDING")}</p>
            </div>
          </li>
          <li className="flex items-start">
            <div className="w-6 h-6 rounded-full bg-accio-primary mr-3 mt-0.5 flex items-center justify-center text-white font-bold text-xs">2</div>
            <div>
              <p className="font-medium">{t("WIKIPEDIA")}</p>
              <p className="text-gray-600 text-sm">{t("FINGERTIPS")}</p>
            </div>
          </li>
          <li className="flex items-start">
            <div className="w-6 h-6 rounded-full bg-accio-primary mr-3 mt-0.5 flex items-center justify-center text-white font-bold text-xs">3</div>
            <div>
              <p className="font-medium">{t("PLATFORM")}</p>
              <p className="text-gray-600 text-sm">{t("SEAMLESS")}</p>
            </div>
          </li>
        </ul>
      </div>
    </div>
  );
}
