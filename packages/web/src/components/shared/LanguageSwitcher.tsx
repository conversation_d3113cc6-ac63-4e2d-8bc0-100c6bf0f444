"use client";

import { usePathname, useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';
import { locales, localeNames, localeCurrencies } from '@/i18n';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Globe } from 'lucide-react';

export function LanguageSwitcher({ variant = 'default' }: { variant?: 'default' | 'floating' }) {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  // Function to change the language
  const handleLanguageChange = (newLocale: string) => {
    // Extract the path part after the locale
    const segments = pathname.split('/');
    segments.shift(); // Remove empty first segment

    if (segments.length > 0 && locales.includes(segments[0])) {
      segments.shift(); // Remove locale segment
    }

    // Construct the new path with the new locale
    const pathWithoutLocale = segments.length > 0 ? `/${segments.join('/')}` : '';
    const newPath = `/${newLocale}${pathWithoutLocale}`;

    // Navigate to the new path
    router.push(newPath);
    router.refresh();
  };

  // Get the current locale display name and currency
  const currentLocaleName = localeNames[locale as keyof typeof localeNames] || 'English';
  const currentCurrency = localeCurrencies[locale as keyof typeof localeCurrencies] || 'USD';
  const displayText = `${currentLocaleName} - ${currentCurrency}`;

  if (variant === 'floating') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div
            className="relative h-8 w-8 sm:h-10 sm:w-10 group transition-all duration-300 ease-in-out hover:w-[120px] sm:hover:w-[160px] overflow-hidden max-w-[120px] sm:max-w-[160px] cursor-pointer"
          >
            <div className="absolute inset-0 bg-white border border-gray-200 shadow-sm rounded-full"></div>
            <div className="absolute inset-0 flex items-center px-2.5">
              <Globe className="h-4 w-4 sm:h-5 sm:w-5 text-gray-700 flex-shrink-0" />
              <span className="ml-2 sm:ml-2.5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap text-xs sm:text-sm font-medium">
                {displayText}
              </span>
            </div>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="min-w-[200px]">
          {locales.map((loc) => (
            <DropdownMenuItem
              key={loc}
              onClick={() => handleLanguageChange(loc)}
              className="py-2"
            >
              <div className="flex items-center space-x-2">
                <Globe className="h-5 w-5 text-gray-700" />
                <span>{localeNames[loc as keyof typeof localeNames]} - {localeCurrencies[loc as keyof typeof localeCurrencies]}</span>
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <div className="flex items-center">
      <DropdownMenu>
        <DropdownMenuTrigger className="flex items-center text-gray-600 hover:text-gray-800 cursor-pointer">
          <Globe className="w-4 h-4 mr-1" />
          <span className="text-sm">{displayText}</span>
          <svg
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="ml-1"
          >
            <path
              d="M6 8.5L2 4.5H10L6 8.5Z"
              fill="currentColor"
            />
          </svg>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {locales.map((loc) => (
            <DropdownMenuItem
              key={loc}
              onClick={() => handleLanguageChange(loc)}
            >
              <div className="flex items-center space-x-2">
                <Globe className="h-5 w-5 text-gray-700" />
                <span>{localeNames[loc as keyof typeof localeNames]} - {localeCurrencies[loc as keyof typeof localeCurrencies]}</span>
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
