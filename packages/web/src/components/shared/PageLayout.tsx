"use client";

import { ReactNode, useState, useEffect } from "react";
import { useSidebar } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/shared/Sidebar";
import { FloatingMenu } from "@/components/shared/FloatingMenu";
import { HamburgerMenu } from "@/components/shared/HamburgerMenu";
import Link from "next/link";

interface PageLayoutProps {
  children: ReactNode;
  className?: string;
  showFloatingMenu?: boolean;
  darkMode?: boolean; // For pages with dark backgrounds like home
}

export function PageLayout({
  children,
  className = "",
  showFloatingMenu = true,
  darkMode = false,
}: PageLayoutProps) {
  const { state } = useSidebar();
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  // Add responsive behavior to detect small screens
  useEffect(() => {
    const handleResize = () => {
      const smallScreen = window.innerWidth < 800;
      setIsSmallScreen(smallScreen);
    };

    // Set initial state
    handleResize();

    // Add event listener for window resize
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className={`page-layout-wrapper relative w-full ${className}`}>
      <AppSidebar />

      {/* Mobile header bar with menu, logo, and floating menu in one line */}
      {isSmallScreen ? (
        <div className="absolute top-4 left-0 right-0 z-50 flex items-center justify-between px-4">
          <div className="flex items-center">
            <HamburgerMenu darkMode={darkMode} />
            <Link href="/">
              <img
                src={darkMode ? "/images/logoMiccobuy-white.png" : "/images/logoMiccobuy.png"}
                alt="Miccobuy"
                className="h-10 cursor-pointer ml-2"
              />
            </Link>
          </div>
          {showFloatingMenu && (
            <FloatingMenu />
          )}
        </div>
      ) : (
        <>
          {/* Desktop: Menu button and logo when sidebar is collapsed */}
          {state === "collapsed" && (
            <div className="absolute top-4 left-4 z-50 flex items-center">
              <HamburgerMenu darkMode={darkMode} />
              <Link href="/">
                <img
                  src={darkMode ? "/images/logoMiccobuy-white.png" : "/images/logoMiccobuy.png"}
                  alt="Miccobuy"
                  className="h-12 cursor-pointer"
                />
              </Link>
            </div>
          )}

          {/* Desktop: Floating menu */}
          {showFloatingMenu && (
            <div className="absolute top-4 right-4 z-30 w-auto flex justify-end px-4 sm:px-6 lg:px-8">
              <FloatingMenu />
            </div>
          )}
        </>
      )}

      {/* Main content */}
      <div className="page-main-content w-full">
        {children}
      </div>
    </div>
  );
}
