"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';
import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';

interface ProhibitedPurchaseAlertProps {
  message: string;
  onConfirm: () => void;
  onCancel?: () => void;
}

export function ProhibitedPurchaseAlert({ 
  message, 
  onConfirm, 
  onCancel 
}: ProhibitedPurchaseAlertProps) {
  const [isVisible, setIsVisible] = useState(true);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  const handleConfirm = () => {
    setIsVisible(false);
    onConfirm();
  };

  const handleCancel = () => {
    setIsVisible(false);
    if (onCancel) onCancel();
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6 mx-4">
        <div className="flex items-start mb-4">
          <div className="flex-shrink-0 mr-3">
            <AlertCircle className="h-6 w-6 text-red-500" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">{tCommon("PROHIBITED_PURCHASE")}</h3>
            <p className="text-gray-700">{message}</p>
          </div>
        </div>
        
        <div className="flex justify-end space-x-3 mt-6">
          {onCancel && (
            <Button 
              variant="outline" 
              onClick={handleCancel}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              {tCommon("CANCEL")}
            </Button>
          )}
          <Button 
            onClick={handleConfirm}
            className="bg-green-500 hover:bg-green-600 text-white"
          >
            {tCommon("CONFIRM")}
          </Button>
        </div>
      </div>
    </div>
  );
}
