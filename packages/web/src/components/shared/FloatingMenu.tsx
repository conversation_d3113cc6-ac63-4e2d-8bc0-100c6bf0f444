"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { useSession, signOut } from "next-auth/react";
import { useCart } from "@/lib/cart-context";
import { handleSubscription } from "@/lib/subscription";
import { useIsMobile } from "@/hooks/use-mobile";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Globe,
  ShoppingBag,
  LogOut,
  User,
  LogIn,
  Crown,
  MessageCircle,
} from "lucide-react";
import { getLocalCurrencyTxt, locales, localeNames, localeCurrencies,  } from "@/i18n";
import { usePathname, useRouter } from "next/navigation";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import { useLanguage } from "@/lib/language-context";

// Helper function to get flag image path based on locale
const getFlagImagePath = (locale: string): string => {
  const localeMap: Record<string, string> = {
    en: '/images/en.png',
    zh: '/images/cn.png',
    fr: '/images/fr.png',
  };
  return localeMap[locale] || '/images/en.png'; // Default to English flag if locale not found
};

export function FloatingMenu() {
  const [languageTxt, setLanguageTxt] = useState("English - USD");
  const { data: session, status } = useSession();
  const { cartItems, toggleCartPanel } = useCart();
  const [totalItems, setTotalItems] = useState(0);
  const isLoading = status === "loading";
  const isMobile = useIsMobile();
  const router = useRouter();
  const pathname = usePathname();
  const { language, setLanguage } = useLanguage();
  const tCommon = useTranslations(I18NNamespace.COMMON);

  // Use client-side effect to calculate cart items to avoid hydration mismatch
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    const txt = getLocalCurrencyTxt(language as "en");
    setLanguageTxt(txt);
  }, [language]);


  useEffect(() => {
    setIsMounted(true);
    const itemCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);
    setTotalItems(itemCount);
  }, [cartItems]);

  const handleLanguageChange = (newLocale: string) => {
    // Extract the path part after the locale
    const segments = pathname.split('/');
    segments.shift(); // Remove empty first segment

    if (segments.length > 0 && locales.includes(segments[0])) {
      segments.shift(); // Remove locale segment
    }

    // Construct the new path with the new locale
    const pathWithoutLocale = segments.length > 0 ? `/${segments.join('/')}` : '';
    const newPath = `/${newLocale}${pathWithoutLocale}`;

    setLanguage(newLocale);

    // Navigate to the new path
    router.push(newPath);
    router.refresh();
  };

  // Don't show cart badge until client-side rendering is complete

  return (
    <div className="flex items-center space-x-1 sm:space-x-3">
      {/* Cart Button with independent expansion */}
      <div
        className="relative h-8 w-8 sm:h-10 sm:w-10 group transition-all duration-300 ease-in-out hover:w-[70px] sm:hover:w-[90px] overflow-hidden max-w-[70px] sm:max-w-[90px]"
        onClick={toggleCartPanel}
      >
        <div className="absolute inset-0 bg-white border border-gray-200 shadow-sm rounded-full"></div>
        <div className="absolute inset-0 flex items-center">
          <div className="flex items-center justify-center w-8 sm:w-10 flex-shrink-0">
            <ShoppingBag className="h-4 w-4 sm:h-5 sm:w-5 text-gray-700" />
            {/* Only render the badge on the client side after hydration */}
            {isMounted && totalItems > 0 && (
              <span className="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 sm:h-5 sm:w-5 flex items-center justify-center font-medium">
                {totalItems}
              </span>
            )}
          </div>
          <span className="ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap text-xs sm:text-sm font-medium">
            {tCommon("CART")}
          </span>
        </div>
      </div>

      {/* Language Selector with independent expansion */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div
            className="relative h-8 w-8 sm:h-10 sm:w-10 group transition-all duration-300 ease-in-out hover:w-[120px] sm:hover:w-[160px] overflow-hidden max-w-[120px] sm:max-w-[160px]"
          >
            <div className="absolute inset-0 bg-white border border-gray-200 shadow-sm rounded-full"></div>
            <div className="absolute inset-0 flex items-center">
              <div className="flex items-center justify-center w-8 sm:w-10 flex-shrink-0">
                <div className="relative h-4 w-4 sm:h-5 sm:w-5 overflow-hidden rounded-full">
                  <Image
                    src={getFlagImagePath(language)}
                    alt={language}
                    width={20}
                    height={20}
                    className="object-cover"
                  />
                </div>
              </div>
              <span className="ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap text-xs sm:text-sm font-medium">
                {languageTxt}
              </span>
            </div>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="min-w-[200px]">
          {locales.map((loc) => (
            <DropdownMenuItem
              key={loc}
              onClick={() => handleLanguageChange(loc)}
              className="py-2"
            >
              <div className="flex items-center space-x-2">
                <div className="relative h-5 w-5 overflow-hidden rounded-full">
                  <Image
                    src={getFlagImagePath(loc)}
                    alt={localeNames[loc as keyof typeof localeNames]}
                    width={20}
                    height={20}
                    className="object-cover"
                  />
                </div>
                <span>{localeNames[loc as keyof typeof localeNames]} - {localeCurrencies[loc as keyof typeof localeCurrencies]}</span>
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Upgrade or Talk to Agent Button */}
      {session?.user && (
        session?.user.plan === 'Pro' ? (
          <Button
            onClick={() => window.open('https://calendly.com/miccoapp/15min', '_blank')}
            className={`mr-2 ${isMobile ? 'h-8 w-8 p-0' : 'h-10 px-4'} bg-green-600 hover:bg-green-700 text-white rounded-full flex items-center justify-center`}
          >
            <MessageCircle className={`${isMobile ? '' : 'mr-2'} h-4 w-4`} />
            {!isMobile && <span className="text-sm">{tCommon("TALK_TO_AGENT")}</span>}
          </Button>
        ) : (
          <Button
            onClick={handleSubscription}
            className={`mr-2 ${isMobile ? 'h-8 w-8 p-0' : 'h-10 px-4'} bg-black hover:bg-gray-800 text-white rounded-full flex items-center justify-center`}
          >
            <Crown className={`${isMobile ? '' : 'mr-2'} h-4 w-4`} />
            {!isMobile && <span className="text-sm">{tCommon("UPGRADE")}</span>}
          </Button>
        )
      )}

      {/* Auth Section */}
      {isLoading ? (
        <div className="h-10 w-20 animate-pulse bg-gray-200 rounded-full"></div>
      ) : session?.user ? (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-white border border-gray-200 shadow-sm p-0 overflow-hidden mr-1 flex items-center justify-center">
              {session.user.image ? (
                <img src={session.user.image} alt={session.user.name || "User"} className="h-full w-full object-cover" />
              ) : (
                <User className="h-4 w-4 sm:h-5 sm:w-5 text-gray-700" />
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end">
            <DropdownMenuLabel className="font-normal">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">{session.user.name}</p>
                <p className="text-xs leading-none text-muted-foreground">
                  {session.user.email}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => signOut({ callbackUrl: '/login' })}>
              <LogOut className="mr-2 h-4 w-4" />
              <span>{tCommon("LOGOUT")}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ) : (
        <div className="flex items-center space-x-2">
          {!isMobile && (
            <Button variant="outline" className="text-sm h-10 px-4 bg-white border border-gray-200 shadow-sm rounded-full" asChild>
              <Link href="/signup">{tCommon("SIGNUP")}</Link>
            </Button>
          )}
          <Button className={`text-sm ${isMobile ? 'h-8 w-8 p-0' : 'h-10 px-4'} bg-gray-900 hover:bg-gray-800 text-white rounded-full flex items-center justify-center`} asChild>
            <Link href="/login">{isMobile ? <LogIn className="h-4 w-4" /> : tCommon("LOGIN")}</Link>
          </Button>
        </div>
      )}
    </div>
  );
}
