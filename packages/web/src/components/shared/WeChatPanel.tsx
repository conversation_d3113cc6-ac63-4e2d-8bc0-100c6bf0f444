"use client";

import React from 'react';
import Image from 'next/image';
import { useWeChat } from '@/lib/wechat-context';
import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetClose,
} from "@/components/ui/sheet";

export function WeChatPanel() {
  const { isWeChatPanelOpen, toggleWeChatPanel } = useWeChat();
  const t = useTranslations(I18NNamespace.COMPONENTS_SIDEBAR);

  return (
    <Sheet open={isWeChatPanelOpen} onOpenChange={toggleWeChatPanel}>
      <SheetContent className="w-[350px] sm:w-[450px] flex flex-col">
        <SheetHeader className="border-b pb-4 mb-4">
          <SheetTitle className="text-lg font-semibold">{t("JOIN_WECHAT")}</SheetTitle>
        </SheetHeader>

        <div className="flex-grow flex flex-col items-center justify-center p-4">
          <div className="mb-4 text-center text-gray-600">
            <p className="mb-4">{t("SCAN_WECHAT_QR")}</p>
          </div>

          <div className="w-full max-w-[300px] h-auto rounded-lg overflow-hidden shadow-md mx-auto">
            <img
              src="https://micco-cashback.s3.eu-west-3.amazonaws.com/wechat/wechatinvite.jpg"
              alt="WeChat QR Code"
              className="w-full h-auto"
            />
          </div>
        </div>

        <div className="mt-4 text-center">
          <SheetClose className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
            {t("CLOSE")}
          </SheetClose>
        </div>
      </SheetContent>
    </Sheet>
  );
}
