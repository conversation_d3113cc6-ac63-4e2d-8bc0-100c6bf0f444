"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { handleSubscription } from "@/lib/subscription";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

export function Footer() {
  const router = useRouter();
  const t = useTranslations(I18NNamespace.COMPONENTS_FOOTER);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  const footerLinks = [
    { name: t("BLOGS"), href: "/blog" }
  ];
  {/*{ name: t("TERM_OF_USE"), href: "/terms" },
    { name: t("PRIVACY"), href: "/privacy" },
    { name: t("REPORT"), href: "/report" },
    { name: t("LEGAL_NOTICE"), href: "/legal" },
    { name: t("POLICIES"), href: "/policies" },
    { name: t("ABOUT"), href: "/about-us" },*/}
  return (
    <footer className="bg-white border-t py-8 mt-16">
      <div className="miccobuy-container">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="text-2xl font-bold mb-4">{tCommon("TO_PRO")}</h3>
            <div className="flex items-baseline mb-6">
              <span className="text-4xl font-bold">€99</span>
              <span className="text-lg text-gray-600 ml-2">/{tCommon("MONTH")}</span>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg mb-6">
              <h4 className="text-lg font-semibold mb-3">{t("PRO_FEATURES_INCLUDE")}</h4>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>🤖 {t("AI_ASSISTED")}</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>📆 {t("PERSONALIZED_APPOINTMENT")}</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>🧭 {t("STRATEGIC_ADVICE")}</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>🏭 {t("ORGANIZATION_OF_FACTORY")}</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>🛡️ {t("ASSISTANCE_WITH_CONTRACTS")}</span>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <Button
                onClick={handleSubscription}
                className="w-full md:w-auto bg-black hover:bg-gray-800 text-white px-8 py-6 text-lg rounded-lg"
              >
                {t("SUBSCRIBE_NOW")}}
              </Button>
              <p className="text-sm text-gray-500 mt-2">
                {t("BY_SUBSCRIBING")}
              </p>
            </div>
          </div>

          <div className="flex flex-col justify-center">
            <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
              <h4 className="text-xl font-bold mb-4 text-blue-800">{t("WHY_PRO")}</h4>
              <p className="text-gray-700 mb-4">
                {t("UNLOCK_POTENTIAL")}
              </p>
              <div className="space-y-3">
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <h5 className="font-medium">{t("FASTER_SOURCING")}</h5>
                    <p className="text-sm text-gray-600">{t("SAVE_SOURCING_TIME")}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <div>
                    <h5 className="font-medium">{t("SECURE_TRANS")}</h5>
                    <p className="text-sm text-gray-600">{t("PROTECTED_PAYMENTS")}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div>
                    <h5 className="font-medium">{t("DEDICATED_SUPPORT")}</h5>
                    <p className="text-sm text-gray-600">{t("PERSONAL_AGENT")}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <hr className="my-8 border-gray-200" />

        <div className="flex flex-wrap gap-x-6 gap-y-2">
          {footerLinks.map((link) => (
            <Link key={link.name} href={link.href} className="text-sm text-gray-600 hover:text-accio-primary">
              {link.name}
            </Link>
          ))}
        </div>

        <p className="text-sm text-gray-500 mt-4">{t("RESERVED_RIGHTS")}</p>
      </div>
    </footer>
  );
}
