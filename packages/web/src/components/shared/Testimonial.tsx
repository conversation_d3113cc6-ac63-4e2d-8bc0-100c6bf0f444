"use client";

import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

export function Testimonial() {
  const t = useTranslations(I18NNamespace.COMMON);
  return (
    <div className="bg-gray-900 rounded-lg overflow-hidden shadow-lg">
      <div className="flex flex-col md:flex-row">
        <div className="w-full md:w-1/2 bg-gray-800 flex items-center justify-center p-8">
          <div className="w-40 h-40 rounded-full overflow-hidden">
            <img
              src="/images/jensen.png"
              alt="Jensen Huang"
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.onerror = null;
                // Fallback to gradient if image fails to load
                target.style.display = 'none';
                if (target.parentElement) {
                  target.parentElement.classList.add('bg-gradient-to-br', 'from-gray-600', 'to-gray-800');
                }
              }}
            />
          </div>
        </div>
        <div className="w-full md:w-1/2 p-8 flex flex-col justify-center">
          <blockquote className="text-white text-lg md:text-xl italic mb-6">
            {t("AI_IS_GOING_TO_BE")}
          </blockquote>
          <div className="text-accio-primary">— Jensen Huang</div>
          <div className="text-gray-400 text-sm">CEO and Founder, NVIDIA</div>
        </div>
      </div>
    </div>
  );
}
