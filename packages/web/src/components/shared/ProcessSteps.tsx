"use client";

import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';
import Image from 'next/image';

export function ProcessSteps() {
  const t = useTranslations(I18NNamespace.COMPONENTS_PROCESS_STEPS);
  const steps = [
    {
      title: t("PLACE_ORDERS"),
      subtitle: t("PLACE_ORDERS_SUB")
    },
    {
      title: t("SUBMIT_PARCELS"),
      subtitle: t("SUBMIT_PARCELS_SUB")
    },
    {
      title: t("SIGN"),
      subtitle: t("SIGN_SUB")
    }
  ];

  return (
    <section className="pt-4 pb-12 md:pb-16 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="relative">
          {/* Text labels first */}
          <div className="flex justify-between items-start mb-8">
            {steps.map((step, index) => (
              <div key={index} className="flex flex-col items-center z-10 w-1/3">
                <h3 className="text-lg md:text-xl font-semibold mb-2 md:mb-3 text-white">{step.title}</h3>
                <p className="text-sm md:text-base text-white">{step.subtitle}</p>
              </div>
            ))}
          </div>

          {/* Line with circles (SVG) positioned above the text */}
          <div className="w-full z-0">
            <Image
              src="/images/process-steps-white.svg"
              alt="Process Steps"
              width={900}
              height={40}
              className="w-full"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
