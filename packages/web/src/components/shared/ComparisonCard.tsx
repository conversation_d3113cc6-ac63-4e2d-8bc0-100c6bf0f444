"use client";

import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import { useComparison, type Product } from "@/lib/comparison-context";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

export function ComparisonCard() {
  const comparison = useComparison();
  const t = useTranslations(I18NNamespace.COMPONENTS_COMPARASION_CARD);

  if (comparison.selectedProducts.length === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50">
      <div className="bg-white rounded-lg shadow-lg p-4 border border-gray-200 w-full max-w-md">
        <div className="flex justify-between items-center mb-3">
          <h3 className="font-medium">{t("SELECTED")} ({comparison.selectedProducts.length})</h3>
          <button 
            onClick={() => comparison.clearComparison()}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-4 h-4" />
          </button>
        </div>

        <div className="flex flex-wrap gap-2 mb-3">
          {comparison.selectedProducts.map((product: Product) => (
            <div key={product.id} className="relative">
              <img 
                src={product.image} 
                alt={product.name}
                className="w-12 h-12 object-cover rounded border"
              />
              <button 
                onClick={() => comparison.removeFromComparison(product.id)}
                className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-0.5"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          ))}
        </div>

        <Button 
          className="w-full"
          onClick={() => comparison.openComparePanel()}
        >
          {t("COMPARE_NOW")}
        </Button>
      </div>
    </div>
  );
}
