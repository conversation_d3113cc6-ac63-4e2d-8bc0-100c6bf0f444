"use client";

import Link from "next/link";
import { useState } from "react";
import { usePathname } from 'next/navigation';
import { useSession, signIn, signOut } from "next-auth/react"; // Import NextAuth hooks
import { useSidebar, SidebarTrigger } from "@/components/ui/sidebar"; // Import sidebar hooks
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"; // Import DropdownMenu
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"; // Import Avatar
import { Globe, ChevronDown, ShoppingCart, LogOut, User as UserIcon, LogIn, UserPlus, Menu } from "lucide-react"; // Added icons
import { useCart } from "@/lib/cart-context"; // Import useCart
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

export function Navbar() {
  const [language, setLanguage] = useState("English - USD");
  const pathname = usePathname();
  const { data: session, status } = useSession(); // Get session data and status
  const { cartItems, toggleCartPanel } = useCart(); // Get cart items and toggle function
  const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0); // Calculate total quantity
  const isLoading = status === "loading";
  const { toggleSidebar } = useSidebar(); // Get sidebar toggle function
  const tCommon = useTranslations(I18NNamespace.COMMON);

  // Logo click now always navigates to home page without reloading
  const handleLogoClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    // Let the Link component handle the navigation to home page
    // No need to prevent default or reload
  };

  return (
    <nav className="bg-white shadow-sm py-2 fixed top-0 left-0 right-0 z-50">
      <div className="flex justify-between items-center miccobuy-container">
        <div className="flex items-center">
          {/* Sidebar Toggle Button */}
          <Button
            variant="ghost"
            size="icon"
            className="mr-2 transition-all duration-200 hover:bg-gray-100"
            onClick={toggleSidebar}
            aria-label="Toggle Sidebar"
          >
            <Menu className="h-5 w-5" />
          </Button>

          {/* Logo with onClick handler */}
          <Link href="/" className="mr-4" onClick={handleLogoClick}>
            <div className="flex items-center">
              <img
                src="/images/logoMiccobuy.png"
                alt="Miccobuy Logo"
                className="h-8"
              />
            </div>
          </Link>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center text-gray-600 hover:text-gray-800 cursor-pointer">
            <Globe className="w-4 h-4 mr-1" />
            <span className="text-sm">{language}</span>
            <ChevronDown className="w-4 h-4 ml-1" />
          </div>
          {/* Cart Icon and Count - Changed Link to Button */}
          <button onClick={toggleCartPanel} className="flex items-center text-gray-600 hover:text-gray-800 relative">
            <ShoppingCart className="w-5 h-5" />
            {totalItems > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                {totalItems}
              </span>
            )}
          </button>

          {/* Auth Section */}
          <div className="flex items-center">
            {isLoading ? (
              // Optional: Show a loading indicator
              <div className="h-8 w-20 animate-pulse bg-gray-200 rounded-md"></div>
            ) : session?.user ? (
              // User is logged in - Show Avatar Dropdown
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={session.user.image ?? undefined} alt={session.user.name ?? "User"} />
                      <AvatarFallback>
                        {session.user.name ? session.user.name.substring(0, 2).toUpperCase() : <UserIcon className="h-4 w-4"/>}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">{session.user.name}</p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {session.user.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {/* Add other items like Profile, Settings etc. if needed */}
                  {/* <DropdownMenuItem>Profile</DropdownMenuItem> */}
                  <DropdownMenuItem onClick={() => signOut({ callbackUrl: '/login' })}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              // User is not logged in - Show Sign In/Sign Up
              <div className="flex items-center space-x-2">
                 <Button variant="ghost" size="sm" asChild>
                   <Link href="/login">
                     <LogIn className="mr-1 h-4 w-4" /> {tCommon("LOGIN")}
                   </Link>
                 </Button>
                 <Button size="sm" asChild>
                   <Link href="/signup">
                     <UserPlus className="mr-1 h-4 w-4" /> {tCommon("SIGNUP")}
                   </Link>
                 </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
