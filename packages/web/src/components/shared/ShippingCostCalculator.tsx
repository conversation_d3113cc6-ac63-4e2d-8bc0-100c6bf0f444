"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent } from "@/components/ui/card";
import {
  ShippingOption,
  calculateShippingCost,
  convertYuanToUSD,
  convertYuanToEUR,
  getShippingOptions
} from "@/lib/shipping-calculator";
import { Calculator, Plane, Ship, Info } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

interface ShippingCostCalculatorProps {
  initialShippingOption?: string;
  onCostCalculated?: (costYuan: number | null, costUSD: number | null, selectedOption: ShippingOption | undefined) => void;
  showMethodSelection?: boolean;
  compact?: boolean;
  useCurrency?: 'USD' | 'EUR';
  inDialog?: boolean; // Add this property to optimize display in dialog
}

export function ShippingCostCalculator({
  initialShippingOption,
  onCostCalculated,
  showMethodSelection = true,
  compact = false,
  useCurrency = 'EUR',
  inDialog = false
}: ShippingCostCalculatorProps) {
  const [shippingOptions, setShippingOptions] = useState<ShippingOption[]>([]);
  const [selectedShippingChannel, setSelectedShippingChannel] = useState<string>(initialShippingOption || "");
  const [weightKg, setWeightKg] = useState<string>("");
  const [weightG, setWeightG] = useState<string>("");
  const [estimatedCostYuan, setEstimatedCostYuan] = useState<number | null>(null);
  const [estimatedCostUSD, setEstimatedCostUSD] = useState<number | null>(null);
  const [estimatedCostEUR, setEstimatedCostEUR] = useState<number | null>(null);
  const [billableWeight, setBillableWeight] = useState<number | null>(null);
  const tCommon = useTranslations(I18NNamespace.COMMON);
  const t = useTranslations(I18NNamespace.COMPONENTS_SHIPPING_COST_CALCULATOR);

  // Load shipping options on mount
  useEffect(() => {
    const options = getShippingOptions();
    setShippingOptions(options);

    // Set default shipping option if not provided
    if (!initialShippingOption && options.length > 0) {
      setSelectedShippingChannel(options[0].channel_en);
    }
  }, [initialShippingOption]);

  // Update billable weight when weight inputs change
  useEffect(() => {
    // Calculate total weight in kg
    let totalWeightKg = 0;
    let hasValidWeight = false;

    // Add kg weight if provided
    if (weightKg) {
      const kg = parseFloat(weightKg);
      if (!isNaN(kg)) {
        totalWeightKg += kg;
        hasValidWeight = true;
      }
    }

    // Add g weight if provided (converted to kg)
    if (weightG) {
      const g = parseFloat(weightG);
      if (!isNaN(g)) {
        totalWeightKg += g / 1000;
        hasValidWeight = true;
      }
    }

    // Update billable weight if we have valid input
    if (hasValidWeight) {
      setBillableWeight(totalWeightKg);
    } else {
      setBillableWeight(null);
    }
  }, [weightKg, weightG]);

  // Calculate shipping cost whenever billable weight or shipping option changes
  useEffect(() => {
    const selectedOption = shippingOptions.find(opt => opt.channel_en === selectedShippingChannel);

    if (!selectedOption || billableWeight === null) {
      setEstimatedCostYuan(null);
      setEstimatedCostUSD(null);

      // Call the callback with null values
      if (onCostCalculated) {
        onCostCalculated(null, null, selectedOption);
      }
      return;
    }

    // Calculate shipping cost based on the selected shipping option and billable weight
    const costYuan = calculateShippingCost(billableWeight, selectedOption);
    setEstimatedCostYuan(costYuan);

    // Convert to USD and EUR
    let costUSD = null;
    let costEUR = null;
    if (costYuan !== null) {
      costUSD = convertYuanToUSD(costYuan);
      costEUR = convertYuanToEUR(costYuan);
      setEstimatedCostUSD(costUSD);
      setEstimatedCostEUR(costEUR);
    } else {
      setEstimatedCostUSD(null);
      setEstimatedCostEUR(null);
    }

    // Call the callback with the calculated costs
    if (onCostCalculated) {
      onCostCalculated(costYuan, costUSD, selectedOption);
    }
  }, [billableWeight, selectedShippingChannel, shippingOptions, onCostCalculated]);

  return (
    <div className="grid gap-4">
      {!compact && (
        <div className="flex items-center gap-2">
          <Calculator className="h-5 w-5 text-blue-600" />
          <h3 className="font-medium text-lg">{tCommon("SHIPPING_COST_CALCULATOR")}</h3>
        </div>
      )}

      {/* Weight Input */}
      <div className="grid grid-cols-4 items-center gap-4">
        <Label className="text-right col-span-1">
          {tCommon("WEIGHT")}
        </Label>
        <div className="col-span-3 flex items-center">
          <div className="flex items-center mr-4">
            <Input
              id="weight-kg"
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              maxLength={4}
              placeholder="0"
              value={weightKg}
              onChange={(e) => {
                // Only allow numbers
                const value = e.target.value.replace(/[^0-9]/g, '');
                setWeightKg(value);
              }}
              className="mr-2 w-16"
            />
            <span className="whitespace-nowrap">{tCommon("KG")}</span>
          </div>
          <div className="flex items-center">
            <Input
              id="weight-g"
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              maxLength={4}
              placeholder="0"
              value={weightG}
              onChange={(e) => {
                // Only allow numbers
                const value = e.target.value.replace(/[^0-9]/g, '');
                setWeightG(value);
              }}
              className="mr-2 w-16"
            />
            <span className="whitespace-nowrap">{tCommon("G")}</span>
          </div>
        </div>
      </div>

      {/* Weight in kg display */}
      {billableWeight !== null && (
        <div className="grid grid-cols-4 gap-4 mt-2">
          <div className="col-span-1"></div>
          <div className="col-span-3 text-sm">
            <p>{tCommon("WEIGHT")}: <span className="font-medium text-blue-600">{billableWeight.toFixed(2)} {tCommon("KG")}</span></p>
          </div>
        </div>
      )}

      {/* Shipping Method Selection */}
      {showMethodSelection && (
        <div className="mt-4">
          <Label className="mb-2 block font-medium">{t("SHIPPING_METHOD")}</Label>
          <TooltipProvider delayDuration={100}>
            <RadioGroup
              value={selectedShippingChannel}
              onValueChange={setSelectedShippingChannel}
              className={`grid grid-cols-1 ${inDialog ? 'lg:grid-cols-2' : 'md:grid-cols-2'} gap-3`}
            >
              {/* Air Freight Options */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-500">{t("AIR_FREIGHT")}</h4>
                {shippingOptions
                  .filter(option =>
                    option.channel_en === 'Premium Line' &&
                    option.time === '7-12' &&
                    option.productType === 'Goods with Batteries'
                  )
                  .map((option) => (
                    <Label
                      key={option.channel_en}
                      htmlFor={`ship-${option.channel_en}`}
                      className={`flex flex-col p-3 border rounded cursor-pointer hover:border-blue-500 ${
                        selectedShippingChannel === option.channel_en ? 'border-blue-600 bg-blue-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between w-full mb-2">
                        <div className="flex items-center">
                          <RadioGroupItem
                            value={option.channel_en}
                            id={`ship-${option.channel_en}`}
                            className="mr-3 flex-shrink-0"
                          />
                          <Plane className="h-5 w-5 mr-2 text-blue-600 flex-shrink-0" />
                          <div>
                            <span className="font-medium">{option.channel_en}</span> ({option.time} {tCommon("DAYS")})
                            <span className="text-xs block text-gray-500">{option.advantages_en}</span>
                          </div>
                        </div>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-gray-400 ml-2 cursor-help flex-shrink-0" />
                          </TooltipTrigger>
                          <TooltipContent side="top" align="end" className="max-w-xs text-xs">
                            <p>{option.notes}</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      {/* Pricing Details */}
                      <div className="text-xs text-gray-600 pl-8 space-y-1 border-t pt-2 mt-2">
                        <span className="font-medium text-gray-700 block mb-1">{tCommon("PRICING")}:</span>
                        {option.pricingTiers.map((tier: any, index: number) => (
                          <p key={index}>
                            {tier.maxWeightKg ? `≤ ${tier.maxWeightKg}${tCommon("KG")}: ` : ''}
                            {tier.flatRateYuan ? `${useCurrency === 'EUR' ? '€' : '$'}${(useCurrency === 'EUR' ? convertYuanToEUR(tier.flatRateYuan) : convertYuanToUSD(tier.flatRateYuan)).toFixed(2)} (${t("FLAT")})` : ''}
                            {tier.ratePerKgYuan ? `${useCurrency === 'EUR' ? '€' : '$'}${(useCurrency === 'EUR' ? convertYuanToEUR(tier.ratePerKgYuan) : convertYuanToUSD(tier.ratePerKgYuan)).toFixed(2)}/${tCommon("KG")}` : ''}
                            {tier.first500gYuan ? `${useCurrency === 'EUR' ? '€' : '$'}${(useCurrency === 'EUR' ? convertYuanToEUR(tier.first500gYuan) : convertYuanToUSD(tier.first500gYuan)).toFixed(2)} (${t("FIRST")} 500g)` : ''}
                            {tier.next500gYuan ? `, ${useCurrency === 'EUR' ? '€' : '$'}${(useCurrency === 'EUR' ? convertYuanToEUR(tier.next500gYuan) : convertYuanToUSD(tier.next500gYuan)).toFixed(2)}/500g (${t("NEXT")})` : ''}
                          </p>
                        ))}
                      </div>
                    </Label>
                  ))}
              </div>

              {/* Sea Freight Options */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-500">{t("SEA_FREIGHT")}</h4>
                {shippingOptions
                  .filter(option => option.channel_en.toLowerCase().includes('sea'))
                  .map((option) => (
                    <Label
                      key={option.channel_en}
                      htmlFor={`ship-${option.channel_en}`}
                      className={`flex flex-col p-3 border rounded cursor-pointer hover:border-green-500 ${
                        selectedShippingChannel === option.channel_en ? 'border-green-600 bg-green-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between w-full mb-2">
                        <div className="flex items-center">
                          <RadioGroupItem
                            value={option.channel_en}
                            id={`ship-${option.channel_en}`}
                            className="mr-3 flex-shrink-0"
                          />
                          <Ship className="h-5 w-5 mr-2 text-green-600 flex-shrink-0" />
                          <div>
                            <span className="font-medium">{option.channel_en}</span> ({option.time} days)
                            <span className="text-xs block text-gray-500">{option.advantages_en}</span>
                          </div>
                        </div>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-4 w-4 text-gray-400 ml-2 cursor-help flex-shrink-0" />
                          </TooltipTrigger>
                          <TooltipContent side="top" align="end" className="max-w-xs text-xs">
                            <p>{option.notes}</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      {/* Pricing Details */}
                      <div className="text-xs text-gray-600 pl-8 space-y-1 border-t pt-2 mt-2">
                        <span className="font-medium text-gray-700 block mb-1">{tCommon("PRICING")}:</span>
                        {option.pricingTiers.map((tier: any, index: number) => (
                          <p key={index}>
                            {tier.maxWeightKg ? `≤ ${tier.maxWeightKg}${tCommon("KG")}: ` : ''}
                            {tier.flatRateYuan ? `${useCurrency === 'EUR' ? '€' : '$'}${(useCurrency === 'EUR' ? convertYuanToEUR(tier.flatRateYuan) : convertYuanToUSD(tier.flatRateYuan)).toFixed(2)} (${t("FLAT")})` : ''}
                            {tier.ratePerKgYuan ? `${useCurrency === 'EUR' ? '€' : '$'}${(useCurrency === 'EUR' ? convertYuanToEUR(tier.ratePerKgYuan) : convertYuanToUSD(tier.ratePerKgYuan)).toFixed(2)}/${tCommon("KG")}` : ''}
                            {tier.first500gYuan ? `${useCurrency === 'EUR' ? '€' : '$'}${(useCurrency === 'EUR' ? convertYuanToEUR(tier.first500gYuan) : convertYuanToUSD(tier.first500gYuan)).toFixed(2)} (${t("FIRST")} 500g)` : ''}
                            {tier.next500gYuan ? `, ${useCurrency === 'EUR' ? '€' : '$'}${(useCurrency === 'EUR' ? convertYuanToEUR(tier.next500gYuan) : convertYuanToUSD(tier.next500gYuan)).toFixed(2)}/500g (${t("NEXT")})` : ''}
                          </p>
                        ))}
                      </div>
                    </Label>
                  ))}
                {/* Show placeholder if no sea freight options */}
                {shippingOptions.filter(option => option.channel_en.toLowerCase().includes('sea')).length === 0 && (
                  <div className="p-3 border rounded text-gray-500 text-center">
                    {t("NP_SEA_FREIGHT_OPT")}
                  </div>
                )}
              </div>
            </RadioGroup>
          </TooltipProvider>
        </div>
      )}

      {/* Selected Shipping Method (when method selection is hidden) */}
      {!showMethodSelection && selectedShippingChannel && (
        <div className="grid grid-cols-4 gap-4 mt-2">
          <Label className="text-right col-span-1">
            {t("METHOD")}
          </Label>
          <div className="col-span-3">
            <p className="font-medium">
              {shippingOptions.find(opt => opt.channel_en === selectedShippingChannel)?.channel_en || t("UNKNOWN_METHOD")}
            </p>
            <p className="text-sm text-gray-500">
              {shippingOptions.find(opt => opt.channel_en === selectedShippingChannel)?.advantages_en}
            </p>
          </div>
        </div>
      )}

      {/* Estimated Cost Display */}
      <div className={`grid grid-cols-4 items-center gap-4 mt-4 p-3 border rounded-md
        ${estimatedCostUSD !== null ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'}`}>
        <Label className={`text-right col-span-1 font-medium ${estimatedCostUSD !== null ? 'text-green-800' : 'text-gray-600'}`}>
          {t("ESTIMATED_SHIPPING_COST")}:
        </Label>
        <div className="col-span-3">
          {estimatedCostUSD !== null && estimatedCostYuan !== null ? (
            <>
              <p className="text-green-700 font-semibold text-lg">
                {useCurrency === 'EUR'
                  ? `€${estimatedCostEUR?.toFixed(2)}`
                  : `$${estimatedCostUSD.toFixed(2)} USD`
                }
              </p>
              <p className="text-xs text-gray-500 italic mt-1">
                {t("SHIPPING_SIMULATION_ONLY")}
              </p>
            </>
          ) : (
            <p className="text-gray-500">
              {!selectedShippingChannel
                ? t("SELECT_SHIPPING_METHOD")
                : billableWeight === null
                  ? t("ENTER_WEIGHT")
                  : t("CALCULATING")}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
