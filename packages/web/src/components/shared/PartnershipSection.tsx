"use client";

import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

export function PartnershipSection() {
  const t = useTranslations(I18NNamespace.COMPONENTS_PARTNSHIP_SEC);
  const tCommon = useTranslations(I18NNamespace.COMMON);
  return (
    <div className="bg-gray-50 rounded-lg p-6 shadow-sm flex flex-col md:flex-row items-center justify-between gap-6">
      <div className="flex-1">
        <h3 className="text-2xl font-bold mb-4">{t("HELP_SOURCE")}</h3>
        <div className="flex flex-col space-y-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div>
              <div className="text-xl font-bold text-gray-800">120M+ {tCommon("VENDORS")}</div>
              <div className="text-sm text-gray-600">{t("ACCESS_SUPPPLIERS")}</div>
            </div>
          </div>

          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
              </svg>
            </div>
            <div>
              <div className="text-xl font-bold text-gray-800">1 Bn+ {tCommon("PRODUCTS")}</div>
              <div className="text-sm text-gray-600">{t("CATELOG")}</div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg p-6 shadow-sm flex-1 max-w-xl">
        <h4 className="text-xl font-bold mb-3">{t("GLOBAL_SOURCING")}</h4>
        <p className="text-gray-600 mb-4">
          {t("ADVANCED_TECHNOLOGY")}
        </p>
        <div className="flex flex-wrap gap-3">
          <div className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">Taobao</div>
          <div className="bg-red-50 text-red-700 px-3 py-1 rounded-full text-sm font-medium">1688</div>
          <div className="bg-orange-50 text-orange-700 px-3 py-1 rounded-full text-sm font-medium">Alibaba</div>
          <div className="bg-yellow-50 text-yellow-700 px-3 py-1 rounded-full text-sm font-medium">Amazon</div>
          <div className="bg-purple-50 text-purple-700 px-3 py-1 rounded-full text-sm font-medium">AliExpress</div>
        </div>
      </div>

      <div className="bg-accio-primary text-white rounded-lg p-6 w-full md:w-64">
        <div className="text-3xl font-bold mb-1">200+</div>
        <div className="text-sm mb-4">{t("SERVED")}</div>
        <div className="text-3xl font-bold mb-1">98%</div>
        <div className="text-sm mb-4">{t("SATISFACTION_RATE")}</div>
        <div className="text-3xl font-bold mb-1">24/7</div>
        <div className="text-sm">{t("GLOBAL_SUPPORT")}</div>
      </div>
    </div>
  );
}
