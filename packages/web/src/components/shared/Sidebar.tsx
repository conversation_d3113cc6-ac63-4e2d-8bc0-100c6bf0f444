"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { useSession, signIn, signOut } from "next-auth/react";
import {
  Home,
  Heart,
  MessageCircle,
  Settings,
  ChevronRight,
  Search,
  X,
  ShoppingBag, // Added for My Orders
  ShoppingCart, // Added for Cart
  ChevronLeft, // Added for fold button
  Crown, // Added for upgrade button
  LogOut,
  User,
  LogIn,
  UserPlus,
  FileText // Added for Blog
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/lib/auth-context";
import { useComparison } from "@/lib/comparison-context";
import { useCart } from "@/lib/cart-context";
import { useWeChat } from "@/lib/wechat-context";
import { handleSubscription } from "@/lib/subscription";
import {
  Sidebar as ShadcnSidebar,
  SidebarContent,
  SidebarFooter,
  SidebarRail,
  useSidebar
} from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useIsMobile } from "@/hooks/use-mobile";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

export function AppSidebar() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const { } = useComparison();
  const { toggleCartPanel, cartItems } = useCart();
  const { toggleWeChatPanel } = useWeChat();
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const { state: sidebarState, toggleSidebar, setOpen } = useSidebar();
  const userHasToggledRef = useRef(false);
  // Initialize with a default value (0) to avoid server-side rendering issues
  const prevScreenSizeRef = useRef(0);
  const isMobile = useIsMobile();
  const t = useTranslations(I18NNamespace.COMPONENTS_SIDEBAR);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  useEffect(() => {
    const loadSearches = () => {
      const savedSearches = localStorage.getItem('recentSearches');
      if (savedSearches) {
        setRecentSearches(JSON.parse(savedSearches));
      }
    };

    loadSearches();
    const handleStorageChange = () => loadSearches();
    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Initialize client-side values when component mounts
  useEffect(() => {
    // Reset userHasToggled flag
    userHasToggledRef.current = false;
    // Set the initial screen width (only runs client-side)
    prevScreenSizeRef.current = window.innerWidth;
  }, []);

  useEffect(() => {
    const handleResize = () => {
      const currentWidth = window.innerWidth;
      const prevWidth = prevScreenSizeRef.current;
      const isSmallScreen = currentWidth < 800;
      const wasLargeScreen = prevWidth >= 800;

      // Only force collapse in these cases:
      // 1. Initial load on small screen (first render)
      // 2. Transitioning from large screen to small screen AND user hasn't manually toggled
      if (isSmallScreen && !userHasToggledRef.current && (wasLargeScreen || prevWidth === 0)) {
        setOpen(false);
      }

      // If transitioning from small screen to large screen, reset the userHasToggled flag
      // This ensures consistent behavior when returning to small screen
      if (!isSmallScreen && prevWidth < 800) {
        userHasToggledRef.current = false;
      }

      // Update previous screen size reference
      prevScreenSizeRef.current = currentWidth;
    };

    // Run once on initial render
    handleResize();

    // Add resize listener
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [setOpen, sidebarState]);

  const handleToggle = () => {
    userHasToggledRef.current = true;
    // Then update the sidebar state
    // setOpen(newState);
    toggleSidebar(false);
  };

  return (
    <>
      <ShadcnSidebar className="bg-gray-50 z-50" variant="sidebar" collapsible="offcanvas">
        <SidebarRail />
        {!isMobile && (
          <div className="absolute top-6 right-0 z-20 flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-all duration-200"
              onClick={handleToggle}
              aria-label="Fold Sidebar"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </div>
        )}


        <SidebarContent>
          <div className="px-4 py-4 flex items-center justify-start">
            <img
              src="/images/logoMiccobuy.png"
              alt="Miccobuy"
              className="h-12 transition-opacity duration-200 group-data-[collapsible=icon]:opacity-0 cursor-pointer ml-4"
              onClick={() => {
                window.location.href = '/';
                window.location.reload();
              }}
            />
          </div>

          <div className="px-4 py-2">
            <div className="space-y-1" onClick={handleToggle}>
              <Link href="/" className="flex items-center text-gray-700 hover:bg-gray-100 px-4 py-2 rounded-md group transition-colors">
                <Home className="w-5 h-5 mr-3 text-gray-500 group-hover:text-accio-primary" />
                <span className="font-medium transition-opacity duration-200 group-data-[collapsible=icon]:opacity-0">{tCommon("HOME")}</span>
              </Link>
              <Link href="/my-orders" className="flex items-center text-gray-700 hover:bg-gray-100 px-4 py-2 rounded-md group transition-colors">
                <ShoppingBag className="w-5 h-5 mr-3 text-gray-500 group-hover:text-accio-primary" />
                <span className="font-medium transition-opacity duration-200 group-data-[collapsible=icon]:opacity-0">{tCommon("MY_ORDERS")}</span>
              </Link>
              <div
                onClick={() => {
                  toggleCartPanel();
                  if (isMobile) {
                    // Close sidebar on mobile after clicking cart
                    setOpen(false);
                  }
                }}
                className="flex items-center text-gray-700 hover:bg-gray-100 px-4 py-2 rounded-md group transition-colors cursor-pointer"
              >
                <div className="relative">
                  <ShoppingCart className="w-5 h-5 mr-3 text-gray-500 group-hover:text-accio-primary" />
                  {cartItems.length > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-medium">
                      {cartItems.reduce((sum, item) => sum + item.quantity, 0)}
                    </span>
                  )}
                </div>
                <span className="font-medium transition-opacity duration-200 group-data-[collapsible=icon]:opacity-0">{tCommon("CART")}</span>
              </div>
              <Link href="/blog" className="flex items-center text-gray-700 hover:bg-gray-100 px-4 py-2 rounded-md group transition-colors">
                <FileText className="w-5 h-5 mr-3 text-gray-500 group-hover:text-accio-primary" />
                <span className="font-medium transition-opacity duration-200 group-data-[collapsible=icon]:opacity-0">{tCommon("BLOG")}</span>
              </Link>
              {/* Temporarily commented out Messages link
              <Link href="/messages" className="flex items-center text-gray-700 hover:bg-gray-100 px-4 py-2 rounded-md group transition-colors">
                <MessageCircle className="w-5 h-5 mr-3 text-gray-500 group-hover:text-accio-primary" />
                <span className="font-medium transition-opacity duration-200 group-data-[collapsible=icon]:opacity-0">{tCommon("MESSAGES")}</span>
              </Link>

              <Link href="/settings" className="flex items-center text-gray-700 hover:bg-gray-100 px-4 py-2 rounded-md group transition-colors">
                <Settings className="w-5 h-5 mr-3 text-gray-500 group-hover:text-accio-primary" />
                <span className="font-medium transition-opacity duration-200 group-data-[collapsible=icon]:opacity-0">{tCommon("SETTINGS")}</span>
              </Link>*/}
            </div>
          </div>

          <Separator className="my-2" />

          <div className="px-4 py-2">
            <div className="px-4 flex justify-between items-center mb-2">
              <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider transition-opacity duration-200 group-data-[collapsible=icon]:opacity-0">{t("RECENT_SEARCHES")}</h3>
              <Link href="/history" className="text-gray-500 hover:text-gray-700 transition-colors">
                <ChevronRight className="h-4 w-4" />
              </Link>
            </div>
            <div className="space-y-1">
              {recentSearches.length > 0 ? (
                <>
                  {recentSearches.slice(0, 5).map((search, index) => {
                    const truncatedSearch = search.length > 25 ? `${search.substring(0, 25)}...` : search;
                    return (
                      <div key={index} className="flex items-center justify-between text-gray-600 hover:bg-gray-100 px-4 py-2 rounded-md transition-colors group/search">
                        <Link
                          href={`/?q=${encodeURIComponent(search)}&showResults=true`}
                          className="flex items-center flex-grow truncate"
                        >
                          <Search className="w-4 h-4 mr-2 text-gray-500 flex-shrink-0" />
                          <span className="transition-opacity duration-200 group-data-[collapsible=icon]:opacity-0 truncate">{truncatedSearch}</span>
                        </Link>
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            const updatedSearches = [...recentSearches];
                            updatedSearches.splice(index, 1);
                            setRecentSearches(updatedSearches);
                            localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
                          }}
                          className="opacity-0 group-hover/search:opacity-100 transition-opacity duration-200 p-1 hover:bg-gray-200 rounded-full"
                          aria-label="Remove search"
                        >
                          <X className="w-3 h-3 text-gray-500" />
                        </button>
                      </div>
                    );
                  })}
                </>
              ) : (
                <div className="text-sm text-gray-500 px-4 py-2 transition-opacity duration-200 group-data-[collapsible=icon]:opacity-0">{t("NO_RECENT_SEARCHES")}</div>
              )}
            </div>
          </div>
        </SidebarContent>

        <SidebarFooter>
          <div className="flex flex-col space-y-4 mx-4 mb-4">
            <div className="p-4 bg-white rounded-lg border border-gray-100 shadow-sm">
              <h3 className="text-lg font-semibold mb-4">{t("DM_US")}</h3>
              <div className="space-y-4">
                {/*<Link href="https://twitter.com/miccobuy" className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                  <svg className="w-5 h-5 mr-3 text-gray-500" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                  </svg>
                  <span className="font-medium">@miccobuy</span>
                </Link>
                <Link href="/discord" className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                  <svg className="w-5 h-5 mr-3 text-gray-500" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20.317 4.492c-1.53-.69-3.17-1.2-4.885-1.49a.075.075 0 0 0-.079.036c-.21.39-.444.885-.608 1.28a18.566 18.566 0 0 0-5.487 0 12.36 12.36 0 0 0-.617-1.28.077.077 0 0 0-.079-.036c-1.714.29-3.354.8-4.885 1.491a.07.07 0 0 0-.032.027C.533 9.093-.32 13.555.099 17.961a.08.08 0 0 0 .031.055 20.03 20.03 0 0 0 5.993 2.98.078.078 0 0 0 .084-.026 13.83 13.83 0 0 0 1.226-1.963.074.074 0 0 0-.041-.104 13.201 13.201 0 0 1-1.872-.878.075.075 0 0 1-.008-.125c.126-.093.252-.19.372-.287a.075.075 0 0 1 .078-.01c3.927 1.764 8.18 1.764 12.061 0a.075.075 0 0 1 .079.009c.12.098.245.195.372.288a.075.075 0 0 1-.006.125c-.598.344-1.22.635-1.873.877a.075.075 0 0 0-.041.105c.36.687.772 1.341 1.225 1.962a.077.077 0 0 0 .084.028 19.963 19.963 0 0 0 6.002-2.981.076.076 0 0 0 .032-.054c.5-5.094-.838-9.52-3.549-13.442a.06.06 0 0 0-.031-.028zM8.02 15.278c-1.182 0-2.157-1.069-2.157-2.38 0-1.312.956-2.38 2.157-2.38 1.21 0 2.176 1.077 2.157 2.38 0 1.312-.956 2.38-2.157 2.38zm7.975 0c-1.183 0-2.157-1.069-2.157-2.38 0-1.312.955-2.38 2.157-2.38 1.21 0 2.176 1.077 2.157 2.38 0 1.312-.946 2.38-2.157 2.38z" />
                  </svg>
                  <span className="font-medium">{t("JOIN_DISCORD")}</span>
                </Link>*/}
                <Link href="https://chat.whatsapp.com/DCUsAJtDJ6qGYdLHjrrAiF" className="flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                  <svg className="w-5 h-5 mr-3 text-gray-500" viewBox="0 0 308 308" fill="currentColor">
                    <path d="M227.904,176.981c-0.6-0.288-23.054-11.345-27.044-12.781c-1.629-0.585-3.374-1.156-5.23-1.156
                      c-3.032,0-5.579,1.511-7.563,4.479c-2.243,3.334-9.033,11.271-11.131,13.642c-0.274,0.313-0.648,0.687-0.872,0.687
                      c-0.201,0-3.676-1.431-4.728-1.888c-24.087-10.463-42.37-35.624-44.877-39.867c-0.358-0.61-0.373-0.887-0.376-0.887
                      c0.088-0.323,0.898-1.135,1.316-1.554c1.223-1.21,2.548-2.805,3.83-4.348c0.607-0.731,1.215-1.463,1.812-2.153
                      c1.86-2.164,2.688-3.844,3.648-5.79l0.503-1.011c2.344-4.657,0.342-8.587-0.305-9.856c-0.531-1.062-10.012-23.944-11.02-26.348
                      c-2.424-5.801-5.627-8.502-10.078-8.502c-0.413,0,0,0-1.732,0.073c-2.109,0.089-13.594,1.601-18.672,4.802
                      c-5.385,3.395-14.495,14.217-14.495,33.249c0,17.129,10.87,33.302,15.537,39.453c0.116,0.155,0.329,0.47,0.638,0.922
                      c17.873,26.102,40.154,45.446,62.741,54.469c21.745,8.686,32.042,9.69,37.896,9.69c0.001,0,0.001,0,0.001,0
                      c2.46,0,4.429-0.193,6.166-0.364l1.102-0.105c7.512-0.666,24.02-9.22,27.775-19.655c2.958-8.219,3.738-17.199,1.77-20.458
                      C233.168,179.508,230.845,178.393,227.904,176.981z"/>
                    <path d="M156.734,0C73.318,0,5.454,67.354,5.454,150.143c0,26.777,7.166,52.988,20.741,75.928L0.212,302.716
                      c-0.484,1.429-0.124,3.009,0.933,4.085C1.908,307.58,2.943,308,4,308c0.405,0,0.813-0.061,1.211-0.188l79.92-25.396
                      c21.87,11.685,46.588,17.853,71.604,17.853C240.143,300.27,308,232.923,308,150.143C308,67.354,240.143,0,156.734,0z
                      M156.734,268.994c-23.539,0-46.338-6.797-65.936-19.657c-0.659-0.433-1.424-0.655-2.194-0.655c-0.407,0-0.815,0.062-1.212,0.188
                      l-40.035,12.726l12.924-38.129c0.418-1.234,0.209-2.595-0.561-3.647c-14.924-20.392-22.813-44.485-22.813-69.677
                      c0-65.543,53.754-118.867,119.826-118.867c66.064,0,119.812,53.324,119.812,118.867
                      C276.546,215.678,222.799,268.994,156.734,268.994z"/>
                  </svg>
                  <span className="font-medium">{t("JOIN_WHATSAPP")}</span>
                </Link>
                <div
                  onClick={toggleWeChatPanel}
                  className="flex items-center text-gray-600 hover:text-gray-900 transition-colors cursor-pointer"
                >
                  <img src="/images/wechat.svg" alt="WeChat" className="w-5 h-5 mr-3 text-gray-500" />
                  <span className="font-medium">{t("JOIN_WECHAT")}</span>
                </div>
              </div>
            </div>

            {isAuthenticated && user && (
              user.plan === 'Pro' ? (
                <Button
                  onClick={() => window.open('https://calendly.com/miccobuy/15min', '_blank')}
                  className="flex items-center justify-center py-3 mb-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors w-full"
                >
                  <MessageCircle className="mr-2 h-4 w-4" />
                  <span className="font-medium">{t("TALK_TO_AGENT")}</span>
                </Button>
              ) : (
                <Button
                  onClick={handleSubscription}
                  className="flex items-center justify-center py-3 mb-4 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors w-full"
                >
                  <Crown className="mr-2 h-4 w-4" />
                  <span className="font-medium">{t("UPGRADE")}</span>
                </Button>
              )
            )}

            {isLoading ? (
              <div className="h-12 bg-gray-200 animate-pulse rounded-lg"></div>
            ) : isAuthenticated && user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="flex w-full items-center gap-2 text-left rounded-lg hover:bg-accent hover:text-accent-foreground h-12 text-sm transition-all">
                    <span className="relative flex shrink-0 overflow-hidden size-8 rounded-lg border transition-all">
                      {user.image ? (
                        <img
                          src={user.image}
                          alt={user.name || 'User'}
                          className="aspect-square h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full bg-gray-400 flex items-center justify-center text-white text-lg font-medium">
                          {user.name ? user.name.charAt(0) : 'N'}
                        </div>
                      )}
                    </span>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <div className="flex items-center gap-1.5 overflow-hidden">
                        <span className="truncate font-semibold">{user.name || 'Nico Mingze NI'}</span>
                        <span className={`inline-flex items-center justify-center rounded-md border font-medium shrink-0 px-1 py-0 text-xs ${user.plan === 'Pro' ? 'bg-black text-white' : 'bg-background hover:bg-accent'}`}>
                          {user.plan || 'Free'}
                        </span>
                      </div>
                      <span className="truncate text-xs text-gray-500">{user.email || '<EMAIL>'}</span>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-auto size-4">
                      <path d="m7 15 5 5 5-5"></path>
                      <path d="m7 9 5-5 5 5"></path>
                    </svg>
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end">
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">{user.name}</p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => signOut({ callbackUrl: '/' })}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>{tCommon("LOGOUT")}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button className="w-full text-sm py-3 bg-gray-900 hover:bg-gray-800 text-white rounded-lg" asChild>
                <Link href="/login" className="flex items-center justify-center">
                  <LogIn className="mr-2 h-4 w-4" />
                  <span>{tCommon("LOGIN")}</span>
                </Link>
              </Button>
            )}
          </div>
        </SidebarFooter>
      </ShadcnSidebar>
    </>
  );
}
