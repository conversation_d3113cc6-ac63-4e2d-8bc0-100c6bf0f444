"use client";

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { OrderStatus } from '@/types/order-shared';
import { formatCurrency } from '@/utils/format';
import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';

interface OrderEvent {
  timestamp: Date;
  description: string;
  status: OrderStatus;
}

interface OrderDetailsPopupProps {
  isOpen: boolean;
  onClose: () => void;
  orderId: string;
  orderStatus: OrderStatus;
  orderTotal: number;
  createdAt: Date;
  updatedAt: Date;
  warehouseEntryDate?: Date;
}

export function OrderDetailsPopup({
  isOpen,
  onClose,
  orderId,
  orderStatus,
  orderTotal,
  createdAt,
  updatedAt,
  warehouseEntryDate
}: OrderDetailsPopupProps) {
  const [events, setEvents] = useState<OrderEvent[]>([]);
  const t = useTranslations(I18NNamespace.COMPONENTS_ORDER_DETAILS_POPUP);

  // Generate order events based on order status and dates
  useEffect(() => {
    if (!isOpen) return;

    const orderEvents: OrderEvent[] = [];

    // Always add order creation event
    orderEvents.push({
      timestamp: new Date(createdAt),
      description: t("HAVE_SUBMITED_ORDER"),
      status: OrderStatus.PENDING
    });

    // Add payment event if status is beyond PENDING
    if (orderStatus !== OrderStatus.PENDING) {
      // Add 6 hours to creation date for payment (simulated)
      const paymentDate = new Date(createdAt);
      paymentDate.setHours(paymentDate.getHours() + 6);

      orderEvents.push({
        timestamp: paymentDate,
        description: `${t("PAYMENT_OF")} ${formatCurrency(orderTotal)}`,
        status: OrderStatus.PENDING
      });
    }

    // Add agent processing event if status is beyond PENDING
    if (orderStatus !== OrderStatus.PENDING) {
      // Add 1 hour to payment date for agent processing (simulated)
      const agentDate = new Date(createdAt);
      agentDate.setHours(agentDate.getHours() + 7);

      orderEvents.push({
        timestamp: agentDate,
        description: t("AGENT_SENT"),
        status: OrderStatus.PROCESSING
      });
    }

    // Add seller shipping event if status is beyond PROCESSING
    if (
      orderStatus !== OrderStatus.PENDING &&
      orderStatus !== OrderStatus.PROCESSING
    ) {
      // Add 1 hour to agent date for seller shipping (simulated)
      const sellerDate = new Date(createdAt);
      sellerDate.setHours(sellerDate.getHours() + 8);

      orderEvents.push({
        timestamp: sellerDate,
        description: t("SELLER_SENT"),
        status: OrderStatus.PROCESSING
      });
    }

    // Add warehouse arrival event if status is PENDING_TRANSFER or beyond
    if (
      orderStatus === OrderStatus.PENDING_TRANSFER ||
      orderStatus === OrderStatus.SHIPPED ||
      orderStatus === OrderStatus.DELIVERED
    ) {
      // Use actual warehouse entry date if available, otherwise simulate
      const warehouseDate = warehouseEntryDate || new Date(updatedAt);

      orderEvents.push({
        timestamp: warehouseDate,
        description: t("PARCEL_ARRIVED"),
        status: OrderStatus.PENDING_TRANSFER
      });
    }

    // Add shipping event if status is SHIPPED or DELIVERED
    if (
      orderStatus === OrderStatus.SHIPPED ||
      orderStatus === OrderStatus.DELIVERED
    ) {
      // Add 2 days to warehouse date for shipping (simulated)
      const shippingDate = new Date(updatedAt);
      shippingDate.setDate(shippingDate.getDate() + 2);

      orderEvents.push({
        timestamp: shippingDate,
        description: t("PACKAGE_SHIPPED"),
        status: OrderStatus.SHIPPED
      });
    }

    // Add delivery event if status is DELIVERED
    if (orderStatus === OrderStatus.DELIVERED) {
      // Add 7 days to shipping date for delivery (simulated)
      const deliveryDate = new Date(updatedAt);
      deliveryDate.setDate(deliveryDate.getDate() + 9);

      orderEvents.push({
        timestamp: deliveryDate,
        description: t("PACKAGE_DELIVERED"),
        status: OrderStatus.DELIVERED
      });
    }

    // Sort events by timestamp (newest first)
    orderEvents.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    setEvents(orderEvents);
  }, [isOpen, orderId, orderStatus, orderTotal, createdAt, updatedAt, warehouseEntryDate]);

  // Format date as YYYY-MM-DD HH:MM:SS
  const formatDate = (date: Date) => {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).replace(/\//g, '-');
  };

  // Get color based on order status
  const getStatusColor = (status: OrderStatus, type: 'bg' | 'text' | 'ring') => {
    const colors = {
      [OrderStatus.PENDING]: {
        bg: 'bg-orange-500',
        text: 'text-orange-500',
        ring: 'ring-orange-500'
      },
      [OrderStatus.PROCESSING]: {
        bg: 'bg-blue-500',
        text: 'text-blue-500',
        ring: 'ring-blue-500'
      },
      [OrderStatus.PENDING_TRANSFER]: {
        bg: 'bg-purple-500',
        text: 'text-purple-500',
        ring: 'ring-purple-500'
      },
      [OrderStatus.SHIPPED]: {
        bg: 'bg-indigo-500',
        text: 'text-indigo-500',
        ring: 'ring-indigo-500'
      },
      [OrderStatus.DELIVERED]: {
        bg: 'bg-green-500',
        text: 'text-green-500',
        ring: 'ring-green-500'
      },
      [OrderStatus.CANCELLED]: {
        bg: 'bg-red-500',
        text: 'text-red-500',
        ring: 'ring-red-500'
      }
    };

    return colors[status]?.[type] || colors[OrderStatus.PENDING][type];
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t("ORDER_DETAIL")} (#{orderId?.substring(0, 8)})</DialogTitle>
        </DialogHeader>

        <div className="max-h-[60vh] overflow-auto mt-4">
          <div className="space-y-4 p-1">
            {events.map((event, index) => (
              <div key={index} className="flex">
                <div className="mr-4 flex flex-col items-center">
                  <div
                    className={`w-4 h-4 rounded-full flex items-center justify-center
                      ${getStatusColor(event.status, 'bg')}
                      ${index === 0 ? 'ring-2 ring-offset-2 ' + getStatusColor(event.status, 'ring') : ''}
                    `}
                  >
                    {index === 0 && (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-2 w-2 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                      </svg>
                    )}
                  </div>
                  {index < events.length - 1 && (
                    <div className="w-0.5 h-full bg-gray-200 mt-1"></div>
                  )}
                </div>
                <div className="flex-1 pb-4">
                  <p className="text-sm text-gray-500">{formatDate(event.timestamp)}</p>
                  <p className="text-sm font-medium mt-1">{event.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
