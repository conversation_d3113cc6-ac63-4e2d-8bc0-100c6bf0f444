"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Ship } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ShippingCostCalculator } from "@/components/shared/ShippingCostCalculator";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

export function ShippingCalculatorButton() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const tShippingCostCal = useTranslations(I18NNamespace.COMPONENTS_SHIPPING_COST_CALCULATOR);
  const tProductDetailView = useTranslations(I18NNamespace.COMPONENTS_PRODUCT_DETAIL_VIEW);

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className="flex items-center gap-1 text-blue-600 border-blue-200 hover:bg-blue-50"
        onClick={() => setIsModalOpen(true)}
        aria-label="International shipping cost"
      >
        <Ship className="h-4 w-4" />
        <span>{tProductDetailView("INTERNATIONAL_SHIPPING_COST")}</span>
      </Button>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>{tShippingCostCal("SHIPPING_COST_CAL")}</DialogTitle>
          </DialogHeader>
          <div className="overflow-y-auto pr-1 -mr-1 flex-grow">
            <ShippingCostCalculator
              showMethodSelection={true}
              useCurrency="EUR"
              inDialog={true}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
