"use client";

// Navbar is already included in the parent component
import { BackButton } from "@/components/products/BackButton";
import { ProductDetailView } from "@/components/products/ProductDetailView";
import { MobileProductDetailView } from "@/components/products/MobileProductDetailView";
import { ProductDetailError } from "@/components/products/ProductDetailError";
import { PageLayout } from "@/components/shared/PageLayout";
import { useEffect, useState } from "react";
import { useIsMobile } from "@/hooks/use-mobile";

// Define the props interface based on what the server component passes
interface ProductDetailPageClientProps {
  product: any; // Use proper type from your data model
  sellerInfo: any; // Use proper type from your data model
  attributes: Record<string, string>;
  error?: {
    code: string;
    message: string;
    productId?: string;
    source?: string;
  };
}

export function ProductDetailPageClient({
  product,
  sellerInfo,
  attributes,
  error,
}: ProductDetailPageClientProps) {
  // State to track if images are loaded
  const [imagesLoaded, setImagesLoaded] = useState(false);
  const [contentVisible, setContentVisible] = useState(false);
  const isMobile = useIsMobile();

  // Effect to simulate progressive loading
  useEffect(() => {
    // Show content immediately
    setContentVisible(true);

    // Simulate image loading delay
    const timer = setTimeout(() => {
      setImagesLoaded(true);
    }, 100); // Small delay to ensure content is visible first

    return () => clearTimeout(timer);
  }, []);

  // If there's an error, render the error component
  if (error) {
    return (
      <div className="min-h-screen bg-[#FAFAFA]">
        <PageLayout>
          <div className="pt-20 px-4 sm:px-6 lg:px-8 py-8">
            <BackButton />
            <ProductDetailError
              errorCode={error.code}
              errorMessage={error.message}
              productId={error.productId}
              source={error.source}
              onRetry={() => window.location.reload()}
            />
          </div>
        </PageLayout>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-[#FAFAFA] product-detail-page-wrapper transition-opacity duration-300 ${contentVisible ? 'opacity-100' : 'opacity-0'}`}>
      <PageLayout>
        <div className="pt-20 px-4 sm:px-6 lg:px-8 py-8 product-detail-container">
          <BackButton />
          {/* Conditionally render mobile or desktop view */}
          {isMobile ? (
            <MobileProductDetailView
              product={product}
              sellerInfo={sellerInfo}
              attributes={attributes}
              imagesLoaded={imagesLoaded}
            />
          ) : (
            <ProductDetailView
              product={product}
              sellerInfo={sellerInfo}
              attributes={attributes}
              imagesLoaded={imagesLoaded}
            />
          )}
        </div>
      </PageLayout>
    </div>
  );
}
