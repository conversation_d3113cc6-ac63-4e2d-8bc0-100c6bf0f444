"use client";

import React, { useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ChevronRight, ChevronLeft, X, ExternalLink, Star, Info } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface ProductDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId?: string;
}

export default function ProductDetailModal({ isOpen, onClose, productId }: ProductDetailModalProps) {
  const [activeTab, setActiveTab] = useState<'attributes' | 'service' | 'customization'>('attributes');
  const [activeImageIndex, setActiveImageIndex] = useState(0);

  // For a real app, you would fetch this data based on productId
  // This is hardcoded for the demo
  const product = {
    id: "1",
    name: "Hot-selling Greenhouse and Garden Irrigation and Cooling Atomization Rain Irrigation System Four Outlet Atomization Sprinklers",
    supplier: {
      name: "Shandong HYRT International Trading Co., Ltd.",
      verified: true,
      years: 2,
      country: "CN",
      logo: "/images/supplier-logo.png"
    },
    rating: "4.4/5.0",
    reviews: 6,
    pricing: [
      { quantity: "5 - 499 pieces", price: "$0.40" },
      { quantity: "500 - 1999 pieces", price: "$0.27" },
      { quantity: ">=2000 pieces", price: "$0.24" }
    ],
    minOrder: "5 pieces",
    variations: [
      { name: "Light Green", isActive: true }
    ],
    shipping: {
      via: "Alibaba.com Logistics",
      total: "$11.93/piece",
      delivery: "May 8"
    },
    images: [
      { id: "img-1", src: "/images/product1.jpg" },
      { id: "img-2", src: "/images/product1-2.jpg" },
      { id: "img-3", src: "/images/product1-3.jpg" },
      { id: "img-4", src: "/images/product1-4.jpg" },
      { id: "img-5", src: "/images/product1-5.jpg" },
      { id: "img-6", src: "/images/product1-6.jpg" }
    ],
    attributes: {
      "Sprayer Type": "Knapsack",
      "Material": "Plastic",
      "Type": "Sprayers",
      "Place Of Origin": "CN,Shandong",
      "Color": "Green",
      "Diameter": "0.6",
      "Working Pressure": "1.5-3.0 Bar",
      "Moq": "200pcs",
      "Spraying Radius": "1m-1.5m",
      "Usage": "Agriculture",
      "Packing": "Carton",
      "Flow": "40L/h",
      "Product Name": "Mist nozzles",
      "Brand Name": "HYRT",
      "Commercial Buyer": "E-commerce Stores"
    },
    supplierService: [
      { id: "service-1", text: "Design-based customization" },
      { id: "service-2", text: "Sample-based customization" },
      { id: "service-3", text: "Raw-material traceability identification" },
      { id: "service-4", text: "Testing instruments (2)" }
    ],
    customizationOptions: [
      { id: "custom-1", text: "graphic" },
      { id: "custom-2", text: "logo" },
      { id: "custom-3", text: "package" }
    ],
    factoryCapabilities: {
      newProducts: 2,
      staffSize: 21,
      factorySize: 500,
      hitProducts: 8,
      orderVolume: 81,
      bigBuyers: 2
    },
    performance: {
      onTimeDelivery: "94.7%",
      avgResponseTime: "≤3h",
      reorderRate: "30%",
      exportedMarkets: "Africa(30%),North America(30%)",
      orders: "US $570,000+"
    },
    service: [
      { id: "serv-1", text: "Response within 5 minutes" },
      { id: "serv-2", text: "Fastest response" },
      { id: "serv-3", text: "Design-based customization" }
    ]
  };

  // Thumbnail images at the bottom
  const thumbnails = product.images.map((image) => (
    <div
      key={image.id}
      className={`relative w-16 h-16 cursor-pointer border-2 ${activeImageIndex === product.images.indexOf(image) ? 'border-blue-500' : 'border-gray-200'}`}
      onClick={() => setActiveImageIndex(product.images.indexOf(image))}
    >
      <div className="bg-gray-200 w-full h-full flex items-center justify-center">
        <div className="w-8 h-8 bg-gray-300 rounded-md" />
      </div>
    </div>
  ));

  const tabButtons = [
    { id: 'attributes' as const, label: 'Product key attributes' },
    { id: 'service' as const, label: 'Supplier service and capabilities' },
    { id: 'customization' as const, label: 'Customization options' }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) onClose();
    }}>
      <DialogContent className="max-w-4xl p-0 max-h-[90vh] overflow-auto" onInteractOutside={(e) => e.preventDefault()}>
        <div className="sticky top-0 z-10 bg-white border-b p-4 flex justify-between items-center">
          <h2 className="text-xl font-semibold">Product details</h2>
          <div className="flex space-x-2">
            <Button variant="outline" className="rounded-3xl h-10">
              Send inquiry
            </Button>
            <Button variant="outline" className="rounded-3xl h-10">
              Chat now
            </Button>
            <Button variant="outline" className="rounded-3xl h-10">
              Order
            </Button>
            <Button variant="ghost" onClick={onClose} className="h-10 w-10 p-0 ml-2">
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>

        <div className="p-6">
          <h1 className="text-xl font-semibold mb-4 flex items-center">
            {product.name}
            <ExternalLink className="h-4 w-4 ml-2 inline flex-shrink-0 text-gray-400" />
          </h1>

          <div className="flex items-center mb-6">
            <div className="bg-orange-500 rounded-full w-5 h-5 flex items-center justify-center text-white text-xs mr-2">A</div>
            <span>Alibaba.com - {product.supplier.name}</span>
            <span className="ml-2 bg-blue-500 text-white text-xs px-2 py-0.5 rounded">Verified</span>
            <span className="ml-2 text-gray-600">· 2 yrs ·</span>
            <div className="flex items-center ml-1">
              <img src="https://ext.same-assets.com/1272231959/1181911895.svg" alt="CN" className="w-4 h-4 mr-1" />
              <span className="text-gray-600">CN ·</span>
            </div>
            <div className="flex items-center ml-2">
              <span className="text-gray-600">{product.rating}</span>
              <span className="text-gray-500 ml-1">({product.reviews} reviews)</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-6">
            {/* Product Image Gallery */}
            <div className="relative bg-gray-100 aspect-square flex items-center justify-center">
              <button
                className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white rounded-full w-8 h-8 flex items-center justify-center shadow-md z-10"
                onClick={() => setActiveImageIndex(prev => (prev === 0 ? product.images.length - 1 : prev - 1))}
              >
                <ChevronLeft className="h-5 w-5" />
              </button>

              <div className="bg-gray-200 w-full h-full flex items-center justify-center">
                <div className="w-3/4 h-3/4 bg-gray-300 rounded-md" />
              </div>

              <button
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white rounded-full w-8 h-8 flex items-center justify-center shadow-md z-10"
                onClick={() => setActiveImageIndex(prev => (prev === product.images.length - 1 ? 0 : prev + 1))}
              >
                <ChevronRight className="h-5 w-5" />
              </button>

              <button className="absolute top-4 right-4 bg-white rounded-full p-1.5 shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </button>
            </div>

            {/* Product Details */}
            <div>
              <div className="grid grid-cols-3 gap-4 mb-6 text-center">
                <div className="text-gray-500 text-sm">5 - 499 pieces</div>
                <div className="text-gray-500 text-sm">500 - 1999 pieces</div>
                <div className="text-gray-500 text-sm">{'>'}=2000 pieces</div>
                <div className="text-xl font-bold">{product.pricing[0].price}</div>
                <div className="text-xl font-bold">{product.pricing[1].price}</div>
                <div className="text-xl font-bold">{product.pricing[2].price}</div>
              </div>

              <div className="mb-6">
                <p className="text-gray-600">Min. order: {product.minOrder}</p>
              </div>

              <div className="mb-6 border-t border-b py-4">
                <div className="flex justify-between items-center">
                  <h3 className="font-semibold">Variations</h3>
                  <div className="flex items-center text-gray-600">
                    <span>1 color</span>
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </div>
                </div>
                <div className="mt-2">
                  <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-md text-sm inline-block">
                    Light Green
                  </span>
                </div>
              </div>

              <div className="mb-4">
                <div className="flex items-center text-gray-700 mb-2">
                  <span className="mr-2">Delivery via</span>
                  <span className="text-orange-500 font-medium">Alibaba.com</span>
                  <span className="ml-1">Logistics</span>
                </div>
                <div className="text-gray-600 mb-1">Shipping total: {product.shipping.total}</div>
                <div className="text-gray-600">Est. delivery by {product.shipping.delivery}</div>
              </div>
            </div>
          </div>

          {/* Thumbnails */}
          <div className="flex mb-8 space-x-2 overflow-x-auto">
            {thumbnails}
          </div>

          <div className="border-t border-gray-200 pt-4 mb-8">
            <div className="flex items-center text-gray-600 mb-4">
              <span>Protections by Alibaba.com with</span>
              <span className="ml-2 bg-yellow-500 text-xs px-1 rounded text-white">🔒</span>
              <span className="ml-1 text-blue-600 underline flex items-center">
                Trade Assurance
                <ExternalLink className="h-3 w-3 ml-1" />
              </span>
            </div>
          </div>

          <h3 className="text-xl font-semibold mb-4">Key attributes</h3>

          <div className="border rounded-md overflow-hidden mb-8">
            <div className="flex border-b">
              {tabButtons.map((tab) => (
                <button
                  key={tab.id}
                  className={`py-3 px-4 text-sm font-medium flex-1 text-center ${
                    activeTab === tab.id ? 'bg-white' : 'bg-gray-50'
                  }`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            <div className="p-4">
              {activeTab === 'attributes' && (
                <div className="grid grid-cols-2 gap-y-4">
                  {Object.entries(product.attributes).map(([key, value]) => (
                    <div key={key} className="flex">
                      <span className="font-medium w-40 text-gray-700">{key}:</span>
                      <span className="text-gray-600">{value}</span>
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'service' && (
                <div className="grid grid-cols-1 gap-y-4">
                  {product.supplierService.map((service) => (
                    <div key={service.id} className="text-gray-600">{service.text}</div>
                  ))}
                </div>
              )}

              {activeTab === 'customization' && (
                <div className="grid grid-cols-1 gap-y-4">
                  {product.customizationOptions.map((option) => (
                    <div key={option.id} className="text-gray-600">{option.text}</div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="text-center mb-8">
            <Button variant="outline" className="px-4 py-2 text-sm rounded-full">
              <span className="mr-2">Show less</span>
              <ChevronLeft className="h-4 w-4 transform rotate-90" />
            </Button>
          </div>

          <h3 className="text-xl font-semibold mb-4">More pictures</h3>

          <div className="grid grid-cols-6 gap-4 mb-8">
            {product.images.map((image) => (
              <div key={image.id} className="aspect-square bg-gray-100 rounded overflow-hidden relative">
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <div className="w-1/2 h-1/2 bg-gray-300 rounded-md" />
                </div>
              </div>
            ))}
          </div>

          <h3 className="text-xl font-semibold mb-4">Know your supplier</h3>

          <div className="border rounded-lg p-6 mb-8">
            <div className="flex items-start mb-6">
              <div className="w-16 h-16 bg-gray-800 mr-4 flex-shrink-0 flex items-center justify-center text-white text-xs">
                图搜同款
              </div>

              <div>
                <h4 className="text-lg font-medium flex items-center">
                  {product.supplier.name}
                  <div className="bg-orange-500 rounded-full w-4 h-4 flex items-center justify-center text-white text-xs ml-2">A</div>
                </h4>

                <div className="flex items-center mt-1">
                  <span className="bg-blue-500 text-white text-xs px-2 py-0.5 rounded mr-2">Verified</span>
                  <span>Multispecialty supplier · 2 yrs · 20+ staff · 500+ m²</span>
                </div>

                <div className="flex items-center mt-1 text-sm">
                  <span className="mr-2">Shandong,</span>
                  <img src="https://ext.same-assets.com/1272231959/1181911895.svg" alt="CN" className="w-4 h-4 mr-1" />
                  <span>CN</span>
                </div>
              </div>

              <div className="ml-auto">
                <div className="w-24 h-24 bg-gray-200 rounded" />
                <div className="flex items-center justify-between mt-1 text-xs">
                  <span>1/10</span>
                  <span className="bg-gray-800 text-white px-1 py-0.5 rounded">VR</span>
                </div>
              </div>
            </div>

            <h4 className="font-semibold mb-3">Online performance</h4>

            <div className="grid grid-cols-3 gap-6 mb-6">
              <div>
                <div className="flex items-center text-gray-600 text-sm mb-1">
                  <span>Ratings & reviews</span>
                  <Info className="w-3 h-3 ml-1" />
                </div>
                <div className="font-semibold">{product.rating} ({product.reviews} reviews)</div>
              </div>

              <div>
                <div className="flex items-center text-gray-600 text-sm mb-1">
                  <span>On-time delivery rate</span>
                  <Info className="w-3 h-3 ml-1" />
                </div>
                <div className="font-semibold">{product.performance.onTimeDelivery}</div>
              </div>

              <div>
                <div className="flex items-center text-gray-600 text-sm mb-1">
                  <span>Avg. response time</span>
                  <Info className="w-3 h-3 ml-1" />
                </div>
                <div className="font-semibold">{product.performance.avgResponseTime}</div>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-6 mb-6 border-t border-b py-6">
              <div>
                <div className="flex items-center text-gray-600 text-sm mb-1">
                  <span>Main exported markets</span>
                  <Info className="w-3 h-3 ml-1" />
                </div>
                <div className="font-semibold">{product.performance.exportedMarkets}</div>
              </div>

              <div>
                <div className="flex items-center text-gray-600 text-sm mb-1">
                  <span>100 orders</span>
                  <Info className="w-3 h-3 ml-1" />
                </div>
                <div className="font-semibold">{product.performance.orders}</div>
              </div>

              <div>
                <div className="flex items-center text-gray-600 text-sm mb-1">
                  <span>Reorder rate</span>
                </div>
                <div className="font-semibold">{product.performance.reorderRate}</div>
              </div>
            </div>

            <h4 className="font-semibold mb-3">Factory capabilities</h4>

            <div className="grid grid-cols-3 gap-6 mb-6">
              <div>
                <div className="flex items-center text-gray-600 text-sm mb-1">
                  <span>New product (in last 7 days)</span>
                  <Info className="w-3 h-3 ml-1" />
                </div>
                <div className="font-semibold">{product.factoryCapabilities.newProducts}</div>
              </div>

              <div>
                <div className="flex items-center text-gray-600 text-sm mb-1">
                  <span>Staff size</span>
                </div>
                <div className="font-semibold">{product.factoryCapabilities.staffSize}</div>
              </div>

              <div>
                <div className="flex items-center text-gray-600 text-sm mb-1">
                  <span>Factory size(m²)</span>
                </div>
                <div className="font-semibold">{product.factoryCapabilities.factorySize}</div>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-6 mb-6">
              <div>
                <div className="flex items-center text-gray-600 text-sm mb-1">
                  <span>Hit products</span>
                  <Info className="w-3 h-3 ml-1" />
                </div>
                <div className="font-semibold">{product.factoryCapabilities.hitProducts}</div>
              </div>

              <div>
                <div className="flex items-center text-gray-600 text-sm mb-1">
                  <span>Order volume</span>
                  <Info className="w-3 h-3 ml-1" />
                </div>
                <div className="font-semibold">{product.factoryCapabilities.orderVolume}</div>
              </div>

              <div>
                <div className="flex items-center text-gray-600 text-sm mb-1">
                  <span>Big Buyer</span>
                  <Info className="w-3 h-3 ml-1" />
                </div>
                <div className="font-semibold">{product.factoryCapabilities.bigBuyers}</div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6 border-t pt-6">
              <div>
                <h4 className="font-semibold mb-3">Service</h4>
                <ul className="space-y-2">
                  {product.service.map((item) => (
                    <li key={item.id} className="flex items-center">
                      <span className="text-gray-500 mr-2">•</span>
                      <span>{item.text}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-3">Customization</h4>
                <ul className="space-y-2">
                  {product.customizationOptions.map((item) => (
                    <li key={item.id} className="flex items-center">
                      <span className="text-gray-500 mr-2">•</span>
                      <span>{item.text}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
