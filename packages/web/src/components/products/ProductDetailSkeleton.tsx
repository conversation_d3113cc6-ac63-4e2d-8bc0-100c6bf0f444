import { Skeleton } from "@/components/ui/skeleton";

export function ProductDetailSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8 animate-in fade-in duration-300">
      {/* Product Title and Store Info Skeleton */}
      <div className="mb-6 flex justify-between items-start">
        <div className="w-3/4">
          <Skeleton className="h-8 w-full mb-2" />
          <Skeleton className="h-4 w-1/2" />
        </div>
        <Skeleton className="h-4 w-1/4" />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Image Gallery Skeleton */}
        <div>
          <div className="flex">
            {/* Vertical Thumbnails Skeleton */}
            <div className="flex flex-col space-y-2 mr-3 w-[80px]">
              {Array(5).fill(0).map((_, i) => (
                <Skeleton key={`thumb-${i}`} className="w-[70px] h-[70px] rounded-md" />
              ))}
            </div>

            {/* Main Preview Image Skeleton */}
            <div className="flex-1">
              <Skeleton className="w-full h-[500px] rounded-lg" />
            </div>
          </div>
        </div>

        {/* Product Details Skeleton */}
        <div>
          {/* Price Skeleton */}
          <div className="mb-6">
            <Skeleton className="h-10 w-1/3 mb-2" />
            <Skeleton className="h-3 w-2/3 mt-1" />
            <div className="flex items-center mt-2 gap-2">
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-4 w-1/4" />
            </div>
          </div>

          {/* SKU Selection Skeleton */}
          <div className="mb-4">
            <Skeleton className="h-5 w-1/4 mb-2" />
            <div className="flex flex-wrap gap-2">
              {Array(4).fill(0).map((_, i) => (
                <Skeleton key={`sku-${i}`} className="w-[60px] h-[60px] rounded-md" />
              ))}
            </div>
          </div>

          {/* Quantity Selector Skeleton */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <Skeleton className="h-5 w-1/6" />
              <Skeleton className="h-4 w-1/3" />
            </div>
            <div className="flex items-center">
              <Skeleton className="h-8 w-8 rounded-md" />
              <Skeleton className="h-8 w-16 mx-2 rounded-md" />
              <Skeleton className="h-8 w-8 rounded-md" />
            </div>
          </div>

          {/* Buttons Skeleton */}
          <div className="flex flex-col sm:flex-row gap-3 mb-6">
            <Skeleton className="h-10 w-full sm:w-1/2 rounded-md" />
            <Skeleton className="h-10 w-full sm:w-1/2 rounded-md" />
          </div>

          {/* Shipping Info Skeleton */}
          <Skeleton className="h-20 w-full rounded-md mb-4" />

          {/* Attributes Skeleton */}
          <Skeleton className="h-40 w-full rounded-md" />
        </div>
      </div>
    </div>
  );
}
