"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image"; // Import Image component
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
// import { Textarea } from "@/components/ui/textarea"; // Textarea might not be needed based on reconstruction
// Dialog components are still used in the AlertDialog
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  ExternalLink,
  ShoppingCart,
  CheckCircle2,
  ShoppingBag,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useCart } from "@/lib/cart-context";
import { formatPrice } from "@/utils/currency"; // Import currency utilities
import { ShippingCalculatorButton } from "@/components/products/ShippingCalculatorButton";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import { useProductDetailTranslation } from "@/hooks/useProductTranslation";
import { ProductDetailSkeleton } from "./ProductDetailSkeleton";
import { Skeleton } from "../ui/skeleton";

// Define interfaces based on the sample JSON structure
export interface SkuItem {
  price: number;
  total_price?: number; // Not used currently
  orginal_price?: number;
  properties: string; // e.g., "1627207:28320"
  properties_name: string; // e.g., "1627207:28320:颜色分类:白色 PP+榉木脚+PU坐垫"
  quantity: number;
  sku_id: string;
}

export interface PropImgItem {
  properties: string; // e.g., "1627207:28320"
  url: string;
}

interface SellerInfo {
  nick: string;
  item_score?: number;
  score_p?: number;
  delivery_score?: number;
  shop_type?: string;
  user_num_id?: string; // Use this or seller_id from item
  sid?: string | null;
  title?: string;
  zhuy?: string; // Store URL
  cert?: any;
  open_time?: string;
  credit_score?: string;
  shop_name?: string;
}

export interface ProductItem {
  num_iid: string;
  title: string;
  desc_short?: string;
  price: string; // Note: price is string in JSON
  orginal_price?: number;
  nick: string; // Seller nick, also in seller_info
  num: string; // Available quantity string? Use skus.quantity instead
  pic_url: string;
  brand?: string;
  brandId?: number;
  rootCatId?: number;
  cid?: number;
  crumbs?: any;
  detail_url: string;
  desc?: string; // HTML description
  item_imgs: { url: string }[];
  item_weight?: number;
  location?: string;
  post_fee?: number;
  express_fee?: number;
  ems_fee?: number;
  shipping_to?: string;
  has_discount?: string;
  video?: { url: string };
  is_virtual?: any;
  sample_id?: string;
  is_promotion?: string;
  prop_imgs?: { prop_img: PropImgItem[] }; // Nested structure
  property_alias?: string;
  props?: { name: string; value: string }[];
  props_name?: string;
  total_sold?: number;
  skus?: { sku: SkuItem[] }; // Nested structure
  seller_id?: string; // Use this or seller_info.user_num_id
  shop_id?: string;
  props_list?: Record<string, string>;
  seller_info?: SellerInfo;
  tmall?: boolean;
  update_time?: string;
  data_update?: string;
  data_f?: string;
  data_from?: string;
  promo_type?: any;
  props_img?: Record<string, string>;
  error?: any;
  format_check?: string;
  sales?: number;
  desc_img?: string[];
  shop_item?: any[];
  relate_items?: any[];
  source?: string; // Source of the product (taobao or 1688)
}

// Simplified props for the view component
export interface ProductDetailViewProps {
  product: ProductItem; // Use the detailed ProductItem interface
  // sellerInfo and attributes can be derived from product
  imagesLoaded?: boolean; // Flag to control progressive loading of images
}

// Helper function to extract user-friendly name from properties_name
// 提取 SKU 显示名称，支持多个属性（用分号分隔）
export const getSkuDisplayName = (propertiesName: string): string => {
  // 处理多个属性（例如："1627207:28320:颜色分类:白色;20509:28383:尺码:XL"）
  // 或者简化格式："0:1:颜色:小号白"
  const propertyGroups = propertiesName.split(";");

  // 提取每个属性组的显示名称
  const displayNames = propertyGroups.map((group) => {
    const parts = group.split(":");
    // 显示名称是冒号后的最后一部分
    const displayName =
      parts.length >= 4
        ? parts[3]
        : parts.length > 0
        ? parts[parts.length - 1]
        : "";
    return displayName;
  });

  // 将所有显示名称连接起来
  const result = displayNames.filter((name) => name).join(" - ");

  return result;
};

// Helper function to safely get image URL
export const getImageUrl = (url: string | undefined): string => {
  if (!url) return "/images/placeholder.png"; // Default placeholder
  return url.startsWith("//") ? `https:${url}` : url;
};

// Helper function to extract property parts from a property string
export const extractPropertyParts = (
  propertyStr: string
): { propType: string; propValue: string }[] => {
  // Handle multiple properties (e.g., "1627207:28320;20509:28383")
  const properties = propertyStr.split(";");
  return properties.map((prop) => {
    const [propType, propValue] = prop.split(":");
    return { propType, propValue };
  });
};

export type SKUType = {
  name: string;
  translatedName?: string;
  values: {
    id: string;
    name: string;
    skus: SkuItem[];
    translatedName: string | null;
  }[];
};

// Helper function to group SKUs by property type
export const groupSkusByPropertyType = (
  skus: SkuItem[],
  propsList: null | Record<string, string> = null
): Record<string, SKUType> => {
  const result: Record<string, SKUType> = {};

  // First pass: identify all property types and their names
  for (const sku of skus) {
    const propertyParts = extractPropertyParts(sku.properties);

    for (const { propType } of propertyParts) {
      // Extract the property name from properties_name
      // Format is typically: "1627207:28320:颜色分类:白色 PP+榉木脚+PU坐垫"
      // 或者翻译后的: "1627207:28320:Color Classification:White PP+Beech Feet+PU Cushion"
      const propertyNameParts = sku.properties_name.split(";").map(it => it.trim());
      let propertyName = "";

      // Find the part that matches this property type
      for (const part of propertyNameParts) {
        if (part.startsWith(propType + ':')) {
          const nameParts = part.split(":").map(it => it.trim());
          if (nameParts.length >= 3) {
            propertyName = nameParts[2]; // e.g., "颜色分类" 或 "Color Classification"
            break;
          }
        }
      }

      if (!result[propType]) {
        result[propType] = {
          name: propertyName || propType,
          values: [],
        };
      }
    }
  }

  if (propsList) {
    const propArr = Object.entries(propsList);
    for (const [propType, propName] of Object.entries(result)) {
      for (const it of propArr) {
        if (it[0].startsWith(propType + ":")) {
          propName.translatedName = it[1].split(":")[0] ?? null;
          break;
        }
      }
    }
  }

  // Second pass: group SKUs by property value
  for (const sku of skus) {
    const propertyParts = extractPropertyParts(sku.properties);

    for (const { propType, propValue } of propertyParts) {
      if (!result[propType]) continue;

      // Extract the display name for this value
      const propertyNameParts = sku.properties_name.split(";").map(it => it.trim());
      let displayName = "";

      // Find the part that matches this property type and value
      for (const part of propertyNameParts) {
        if (part.startsWith(`${propType}:${propValue}`)) {
          const nameParts = part.split(":").map(it => it.trim());
          if (nameParts.length >= 4) {
            displayName = nameParts[3]; // e.g., "白色 PP+榉木脚+PU坐垫"
            break;
          }
        }
      }

      // Check if this value already exists
      let valueEntry = result[propType].values.find((v) => v.id === propValue);

      if (!valueEntry) {
        valueEntry = {
          id: propValue,
          name: displayName || propValue,
          translatedName: propsList
            ? propsList[`${propType}:${propValue}`]?.split(":")[1]
            : null,
          skus: [],
        };
        result[propType].values.push(valueEntry);
      }

      // Add this SKU to the value's SKUs if not already there
      if (!valueEntry.skus.some((s) => s.sku_id === sku.sku_id)) {
        valueEntry.skus.push(sku);
      }
    }
  }

  return result;
};

// Helper function to find the best matching image for a SKU
export const findSkuImage = (
  sku: SkuItem,
  propImgs: PropImgItem[],
  product: ProductItem
): string | undefined => {
  // For SKUs with multiple properties, we prioritize color property for images
  const propertyParts = extractPropertyParts(sku.properties);

  // Try to find a color property (usually 1627207)
  const colorProperty = propertyParts.find((prop) =>
    // Common property type IDs for color
    ["1627207", "color", "colour"].includes(prop.propType.toLowerCase())
  );

  if (colorProperty) {
    // First try to find an exact match for the color property
    const colorMatch = propImgs.find(
      (img) =>
        img.properties ===
        `${colorProperty.propType}:${colorProperty.propValue}`
    );
    if (colorMatch) {
      return colorMatch.url;
    }
  }

  // If no color-specific match, try direct match by full properties
  const directMatch = propImgs.find((img) => img.properties === sku.properties);
  if (directMatch) {
    return directMatch.url;
  }

  // Try each property part individually
  for (const { propType, propValue } of propertyParts) {
    const propMatch = propImgs.find((img) => {
      const [imgPropType, imgPropValue] = img.properties.split(":");
      return imgPropType === propType && imgPropValue === propValue;
    });

    if (propMatch) {
      return propMatch.url;
    }
  }

  // Check if there's a props_img mapping
  if (product.props_img && product.props_img[sku.properties]) {
    return product.props_img[sku.properties];
  }

  // Fallback to main product image
  return product.pic_url;
};

export function getPropTypes(groupedSkus: any) {
  // Get all property types
  const types = Object.keys(groupedSkus);

  // Try to identify color property (usually 1627207)
  const colorType =
    types.find(
      (type) =>
        groupedSkus[type].name.includes("颜色") ||
        groupedSkus[type].name.toLowerCase().includes("color")
    ) || types[0]; // Default to first property if no color found

  return { colorType };
}

export function useProductDetailHook ({ product, imagesLoaded }: {
  product: ProductItem;
  imagesLoaded?: boolean;
}) {
  const [selectedImageIndex, setSelectedImageIndex] = useState<number>(0); // Index for item_imgs
  // State for two-level selection (color)
  const [selectedColorType, setSelectedColorType] = useState<string | null>(null);
  const [selectedColorValue, setSelectedColorValue] = useState<string | null>(null);
  // Legacy state for compatibility with existing code
  const [selectedSkuProperties, setSelectedSkuProperties] = useState<string | null>(null);
  const [selectedSkuName, setSelectedSkuName] = useState<string | null>(null);
  const [selectedQuantity, setSelectedQuantity] = useState<number>(1);
  const [currentPrice, setCurrentPrice] = useState<number>(parseFloat(product.price) || 0);
  const [currentOriginalPrice, setCurrentOriginalPrice] = useState<number | null>(product.orginal_price || null);
  const [availableQuantity, setAvailableQuantity] = useState<number>(0); // Default to 0, update on SKU select
  const [showConfirmation, setShowConfirmation] = useState(false);
  const { addItem, toggleItemSelection, toggleSelectAll, toggleCartPanel, cartItems, removeItem } = useCart();
  const router = useRouter();
  const t = useTranslations(I18NNamespace.COMPONENTS_PRODUCT_DETAIL_VIEW);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  // 直接调用Hook
  const {
    translatedProduct: translatedProductFromHook,
    isTranslating: isTranslatingFromHook,
  } = useProductDetailTranslation({ item: product });

  // 使用条件判断来处理无效产品情况
  const translatedProduct =
    !product || !product.num_iid
      ? { item: product }
      : translatedProductFromHook;

  // Extract the translated product from the response
  const translatedProductData = translatedProduct?.item || product;

  // State to track loading of images
  const [imageLoadingState, setImageLoadingState] = useState<"loading" | "loaded" | "error">("loading");

  const [mainPreviewImageUrl, setMainPreviewImageUrl] = useState<string>(
    getImageUrl(translatedProductData.pic_url)
  );
  const [groupedSkus, setGroupedSkus] = useState<Record<string, SKUType>>({});

  // Extract nested arrays safely
  const skus = (translatedProductData.skus?.sku || []) as SkuItem[];
  const propImgs = translatedProductData.prop_imgs?.prop_img || [];
  const itemImgs = (translatedProductData.item_imgs || []) as { url: string }[];
  const sellerInfo = translatedProductData.seller_info || {
    nick: translatedProductData.nick || "Unknown",
    user_num_id: translatedProductData.seller_id,
  };
  const attributes = translatedProductData.props || [];

  // Group SKUs by property type for easier selection
  useEffect(() => {
    if (isTranslatingFromHook) return;
    const props_list = translatedProductData.props_list;
    const groupedSKUs = groupSkusByPropertyType(skus, props_list);
    setGroupedSkus(groupedSKUs);
    const { colorType } = getPropTypes(groupedSKUs);
    // Set the property types
    if (colorType) setSelectedColorType(colorType);
    // Only consider SKUs that are in stock (quantity > 0)
    const inStockSkus = skus.filter((sku) => sku.quantity > 0);

    if (inStockSkus.length > 0) {
      // Select the first available color
      if (colorType && groupedSkus[colorType]) {
        const firstAvailableColor = groupedSkus[colorType].values.find(
          (color) => color.skus.some((sku) => sku.quantity > 0)
        );

        if (firstAvailableColor) {
          setSelectedColorValue(firstAvailableColor.id);

          // Find the matching SKU based on selected color
          const selectedSku = inStockSkus.find((sku) => {
            const props = extractPropertyParts(sku.properties);
            return props.some(
              (p) =>
                p.propType === colorType &&
                p.propValue === firstAvailableColor.id
            );
          });

          // Update state with the selected SKU
          if (selectedSku) {
            setSelectedSkuProperties(selectedSku.properties);
            setSelectedSkuName(getSkuDisplayName(selectedSku.properties_name));
            setCurrentPrice(selectedSku.price);
            setCurrentOriginalPrice(selectedSku.orginal_price || null);
            setAvailableQuantity(selectedSku.quantity);

            // Use our enhanced function to find the best matching image
            const skuImageUrl = findSkuImage(selectedSku, propImgs, product);
            setMainPreviewImageUrl(getImageUrl(skuImageUrl));
          }
        }
      } else {
        // Fallback to the first available SKU if no color property
        const firstAvailableSku = inStockSkus[0];
        setSelectedSkuProperties(firstAvailableSku.properties);
        setSelectedSkuName(
          getSkuDisplayName(firstAvailableSku.properties_name)
        );
        setCurrentPrice(firstAvailableSku.price);
        setCurrentOriginalPrice(firstAvailableSku.orginal_price || null);
        setAvailableQuantity(firstAvailableSku.quantity);

        // Use our enhanced function to find the best matching image
        const skuImageUrl = findSkuImage(firstAvailableSku, propImgs, product);
        setMainPreviewImageUrl(getImageUrl(skuImageUrl));
      }
    } else {
      // Handle case with no SKUs or all out of stock
      setAvailableQuantity(0);
      setCurrentPrice(parseFloat(product.price) || 0);
      setCurrentOriginalPrice(product.orginal_price || null);
      setMainPreviewImageUrl(getImageUrl(product.pic_url));
    }

    setSelectedQuantity(1); // Reset quantity on product change or initial load
  }, [translatedProductData, isTranslatingFromHook, skus]);

  // Effect to update image loading state based on imagesLoaded prop
  useEffect(() => {
    if (imagesLoaded) {
      setImageLoadingState("loaded");
    }
  }, [imagesLoaded]);

  // Handle color selection
  const handleColorSelection = (colorValue: string) => {
    // Update color value
    setSelectedColorValue(colorValue);

    // just select the SKU with this color
    const selectedSku = skus.find((sku) => {
      const props = extractPropertyParts(sku.properties);
      return (
        props.some(
          (p) =>
            p.propType === selectedColorType && p.propValue === colorValue
        ) && sku.quantity > 0
      );
    });

    if (selectedSku) {
      updateSelectedSku(selectedSku);
    }
  };

  // Common function to update selected SKU state
  const updateSelectedSku = (sku: SkuItem) => {
    setSelectedSkuProperties(sku.properties);

    // 获取 SKU 显示名称，并记录日志
    const displayName = getSkuDisplayName(sku.properties_name);
    console.log(
      `[ProductDetailView] Selected SKU display name: ${displayName} from properties_name: ${sku.properties_name}`
    );

    setSelectedSkuName(displayName);
    setCurrentPrice(sku.price);
    setCurrentOriginalPrice(sku.orginal_price || null);
    setAvailableQuantity(sku.quantity);
    setSelectedQuantity(1); // Reset quantity when SKU changes

    // Use our enhanced function to find the best matching image
    const skuImageUrl = findSkuImage(sku, propImgs, product);
    setMainPreviewImageUrl(getImageUrl(skuImageUrl));
  };

  // Legacy function for backward compatibility
  const handleSkuSelection = (sku: SkuItem) => {
    // Extract property parts
    const propertyParts = extractPropertyParts(sku.properties);

    // Update color selections if applicable
    if (selectedColorType) {
      const colorProp = propertyParts.find(
        (p) => p.propType === selectedColorType
      );
      if (colorProp) {
        setSelectedColorValue(colorProp.propValue);
      }
    }

    // Update the selected SKU
    updateSelectedSku(sku);
  };

  const handleQuantityChange = (change: number) => {
    setSelectedQuantity((prev) =>
      Math.max(1, Math.min(prev + change, availableQuantity))
    );
  };

  const handleDirectQuantityInput = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    // Only allow numeric input
    const inputValue = event.target.value;
    if (/^\d*$/.test(inputValue)) {
      if (inputValue === "") {
        setSelectedQuantity(1);
      } else {
        const value = parseInt(inputValue, 10);
        if (!isNaN(value)) {
          setSelectedQuantity(Math.max(1, Math.min(value, availableQuantity)));
        }
      }
    }
  };

  // Handle selection from the general item images thumbnail strip
  const handleItemImageSelect = (url: string, index: number) => {
    setMainPreviewImageUrl(getImageUrl(url));
    setSelectedImageIndex(index); // Track index within item_imgs
  };

  // Removed handleThumbnailScroll as it's no longer needed with vertical layout

  const handleAddToCartClick = () => {
    if (!selectedSkuProperties) {
      alert(t("SELECT_FIRST"));
      return;
    }
    addItem(
      {
        productId: translatedProductData.num_iid.toString(),
        title: translatedProductData.title,
        sku: selectedSkuName || "Default",
        skuProperties: selectedSkuProperties,
        imageUrl: mainPreviewImageUrl,
        price: currentPrice,
        originalPrice: currentOriginalPrice,
        storeId: sellerInfo?.user_num_id?.toString() || "unknown-store", // Corrected: user_num_id
        storeName:
          sellerInfo?.shop_name || sellerInfo?.nick || t("UNKNOWN_STORE"),
        translatedStoreName:
          translatedProductData.shop_name || translatedProductData.nick || sellerInfo?.shop_name || sellerInfo?.nick || t("UNKNOWN_STORE"),
      },
      selectedQuantity
    );
    setShowConfirmation(true);
  };

  const handleShopNowClick = async () => {
    if (!selectedSkuProperties) {
      alert(t("SELECT_FIRST"));
      return;
    }

    try {
      // Prevent multiple clicks by using a state variable
      const shopNowButtons = document.querySelectorAll(
        "button"
      ) as NodeListOf<HTMLButtonElement>;
      const shopNowButton = Array.from(shopNowButtons).find((btn) =>
        btn.textContent?.includes("Shop Now")
      );
      if (shopNowButton) {
        shopNowButton.disabled = true;
      }

      const itemData = {
        productId: translatedProductData.num_iid.toString(),
        title: translatedProductData.title,
        sku: selectedSkuName || "Default",
        skuProperties: selectedSkuProperties,
        imageUrl: mainPreviewImageUrl,
        price: currentPrice,
        originalPrice: currentOriginalPrice,
        storeId: sellerInfo?.user_num_id?.toString() || "unknown-store",
        storeName:
          sellerInfo?.shop_name || sellerInfo?.nick || t("UNKNOWN_STORE"),
        translatedStoreName:
          translatedProductData.shop_name || translatedProductData.nick || sellerInfo?.shop_name || sellerInfo?.nick || t("UNKNOWN_STORE"),
      };

      // First deselect all items in cart
      toggleSelectAll(false);

      // Check if the item already exists in the cart
      const existingItem = cartItems.find(
        (item: any) =>
          item.productId === itemData.productId &&
          item.skuProperties === itemData.skuProperties
      );

      // If the item exists, remove it first to avoid quantity accumulation
      if (existingItem) {
        removeItem(itemData.productId, itemData.skuProperties);
      }

      // Add the item to cart with the exact quantity selected
      addItem(itemData, selectedQuantity);

      // Use a Promise to ensure the item is added and selected before navigating
      await new Promise<void>((resolve) => {
        // Short timeout to allow the cart state to update
        setTimeout(() => {
          // Select the item
          toggleItemSelection(itemData.productId, itemData.skuProperties);
          resolve();
        }, 50);
      });

      // Navigate to checkout after ensuring the item is in the cart and selected
      router.push("/checkout");
    } catch (error) {
      // Error handling
      alert(t("ERROR_FOR_CHECKOUT"));
    } finally {
      // Re-enable the button in case of error
      const shopNowButtons = document.querySelectorAll(
        "button"
      ) as NodeListOf<HTMLButtonElement>;
      const shopNowButton = Array.from(shopNowButtons).find((btn) =>
        btn.textContent?.includes("Shop Now")
      );
      if (shopNowButton) {
        shopNowButton.disabled = false;
      }
    }
  };

  let selectedColorValText = t("SELECT_OPT");
  if (selectedColorValue) {
    const item = groupedSkus[selectedColorType ?? ""].values.find(
      (v) => v.id === selectedColorValue
    );
    selectedColorValText = item
      ? item.translatedName || item.name
      : t("SELECT_OPT");
  }

  const isTranslating = !product || !product.num_iid ? false : isTranslatingFromHook;

  const shopName = translatedProductData.shop_name ||
    translatedProductData.nick ||
    sellerInfo?.shop_name ||
    sellerInfo?.nick ||
    t("UNKNOWN_STORE");

  const title = translatedProductData.title || "Product Title";

  return {
    t, tCommon, translatedProductData, skus, groupedSkus, selectedColorType, selectedColorValue, selectedSkuProperties, selectedSkuName, selectedColorValText,
    isTranslating, imageLoadingState, setImageLoadingState,
    itemImgs, selectedImageIndex, mainPreviewImageUrl, sellerInfo, shopName, title,
    attributes, showConfirmation, setShowConfirmation, availableQuantity, currentPrice, selectedQuantity, currentOriginalPrice,
    handleSkuSelection, handleQuantityChange, handleDirectQuantityInput, handleItemImageSelect, handleAddToCartClick,
    handleShopNowClick, handleColorSelection, toggleCartPanel
  };
}

export function ProductDetailView({
  product,
  imagesLoaded = true,
}: ProductDetailViewProps) {

  const {
    t, tCommon, translatedProductData, skus, groupedSkus, selectedColorType, selectedColorValue, selectedSkuProperties, selectedSkuName, selectedColorValText,
    isTranslating, imageLoadingState, setImageLoadingState,
    itemImgs, selectedImageIndex, mainPreviewImageUrl, sellerInfo, shopName, title,
    attributes, showConfirmation, setShowConfirmation, availableQuantity, currentPrice, selectedQuantity, currentOriginalPrice,
    handleSkuSelection, handleQuantityChange, handleDirectQuantityInput, handleItemImageSelect, handleAddToCartClick,
    handleShopNowClick, handleColorSelection, toggleCartPanel
  } = useProductDetailHook({ product, imagesLoaded });


  // Safety check for product data
  if (!translatedProductData) {
    return <div className="p-8 text-center">{t("DATA_NOT_AVAILABLE")}</div>;
  }

  // Check required properties
  if (!translatedProductData.num_iid || !translatedProductData.title) {
    return <div className="p-8 text-center">{t("INVALID_DATA")}</div>;
  }

  // --- Reconstructed JSX Return Block ---
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Product Title and Store Info - Moved to top */}
      <div className="mb-6 flex justify-between items-start">
        <div>
          {
            isTranslating ?
              <TranslationSkeleton width="30vw" /> :
              <h1 className="text-2xl font-bold mb-2">{title}</h1>
          }
          <div className="text-sm text-gray-600">
            <span>
              {tCommon("STORE")}:{" "}
              { isTranslating ? <TranslationSkeleton /> : shopName }
            </span>
            {sellerInfo?.zhuy && (
              <a
                href={sellerInfo.zhuy}
                target="_blank"
                rel="noopener noreferrer"
                className="ml-2 text-blue-600 hover:underline"
              >
                {t("VISIT_STORE")}{" "}
                <ExternalLink className="inline-block ml-1 h-3 w-3" />
              </a>
            )}
            <div className="mt-1">
              {tCommon("SOURCE")}: {tCommon("VIA")}
            </div>
          </div>
        </div>
        <div className="text-right">
          <a
            href="https://calendly.com/miccobuy/15min"
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline text-sm inline-flex items-center"
          >
            {t("CUSTOMIZATION_TALK")} <ExternalLink className="ml-1 h-3 w-3" />
          </a>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Image Gallery - Vertical Layout */}
        <div>
          <div className="flex">
            {/* Vertical Thumbnails on the left */}
            <div className="flex flex-col space-y-2 mr-3 overflow-y-auto max-h-[600px] w-[80px] pr-1 thumbnail-scrollbar">
              {/* Main product image as first thumbnail */}
              <button
                key="main-thumb"
                className={cn(
                  "border rounded-md overflow-hidden flex-shrink-0 w-[70px] h-[70px]",
                  mainPreviewImageUrl ===
                    (product.pic_url.startsWith("//")
                      ? `https:${product.pic_url}`
                      : product.pic_url)
                    ? "border-orange-500"
                    : "border-gray-200"
                )}
                onClick={() => handleItemImageSelect(product.pic_url, -1)}
              >
                <Image
                  src={getImageUrl(product.pic_url)}
                  alt="Main product thumbnail"
                  width={70}
                  height={70}
                  className="object-cover w-full h-full"
                  loading="lazy"
                  onError={(e) => {
                    // If thumbnail image fails to load, hide it
                    const target = e.target as HTMLImageElement;
                    target.style.opacity = "0";
                    target.parentElement!.style.backgroundColor = "#f3f4f6";
                  }}
                />
              </button>

              {/* General item images */}
              {itemImgs.map((img, index) => (
                <button
                  key={`item-img-${index}`}
                  className={cn(
                    "border rounded-md overflow-hidden flex-shrink-0 w-[70px] h-[70px]",
                    selectedImageIndex === index
                      ? "border-orange-500"
                      : "border-gray-200"
                  )}
                  onClick={() => handleItemImageSelect(img.url, index)}
                >
                  <Image
                    src={getImageUrl(img.url)}
                    alt={`Product image ${index + 1}`}
                    width={70}
                    height={70}
                    className="object-cover w-full h-full"
                    loading="lazy"
                    onError={(e) => {
                      // If thumbnail image fails to load, hide it
                      const target = e.target as HTMLImageElement;
                      target.style.opacity = "0";
                      target.parentElement!.style.backgroundColor = "#f3f4f6";
                    }}
                  />
                </button>
              ))}
            </div>

            {/* Main Preview Image on the right */}
            <div className="flex-1 border rounded-lg overflow-hidden relative">
              {/* Skeleton placeholder */}
              {imageLoadingState === "loading" && (
                <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-lg" />
              )}
              <Image
                src={mainPreviewImageUrl}
                alt={product.title || "Product Image"}
                width={600}
                height={600}
                className={`w-full h-auto object-cover transition-opacity duration-300 ${
                  imageLoadingState === "loaded" ? "opacity-100" : "opacity-0"
                }`}
                priority // Prioritize loading main image
                onLoad={() => setImageLoadingState("loaded")}
                onError={(e) => {
                  // If main image fails to load, show a placeholder instead
                  setImageLoadingState("error");
                  // Add a background color to the container
                  const target = e.target as HTMLImageElement;
                  target.style.display = "none";
                  target.parentElement!.style.backgroundColor = "#f3f4f6";
                  // Add a placeholder text
                  const placeholderText = document.createElement("div");
                  placeholderText.className =
                    "flex items-center justify-center h-full text-gray-400";
                  placeholderText.textContent = "Image not available";
                  target.parentElement!.appendChild(placeholderText);
                }}
              />
            </div>
          </div>

          {/* Description Images */}
          {product.desc_img && product.desc_img.length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-3">
                {t("PRODUCT_DESC")}
              </h3>
              <div className="space-y-4">
                {product.desc_img.map((imgUrl, index) => (
                  <div
                    key={`desc-img-${index}`}
                    className="border rounded-lg overflow-hidden"
                  >
                    {imageLoadingState === "loading" ? (
                      <div className="bg-gray-200 animate-pulse w-full h-[300px]" />
                    ) : (
                      <Image
                        src={getImageUrl(imgUrl)}
                        alt={`Product description ${index + 1}`}
                        width={600}
                        height={400}
                        className="w-full h-auto object-contain"
                        loading="lazy"
                        onError={(e) => {
                          // If description image fails to load, hide it
                          const target = e.target as HTMLImageElement;
                          target.style.display = "none";
                          // Remove the parent container to avoid empty spaces
                          const container = target.closest("div");
                          if (container) {
                            container.style.display = "none";
                          }
                        }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Product Details */}
        <div>
          {/* Price - Only showing EUR price */}
          <div className="mb-6">
            {/* Main price row with current price in EUR */}
            <div className="flex items-center flex-wrap">
              <span className="text-4xl font-bold text-orange-500">
                {formatPrice(currentPrice)}
              </span>
            </div>

            {/* Shipping cost note and button */}
            <div className="flex items-center mt-1">
              <div className="text-xs text-gray-500 mr-2">
                {t("NOT_INCLUDING")} {t("INTERNATIONAL_SHIPPING_COST")}
              </div>
              <ShippingCalculatorButton />
            </div>

            {/* Second row with original price and sales info */}
            <div className="flex items-center mt-2 flex-wrap">
              {/* Original price - Small gray with line-through */}
              {currentOriginalPrice && currentOriginalPrice > currentPrice && (
                <div className="flex items-center">
                  <span className="text-sm text-gray-400">
                    {t("BEFORE_DISCOUNT")}
                  </span>
                  <span className="text-sm text-gray-400 line-through ml-1">
                    {formatPrice(currentOriginalPrice)}
                  </span>
                </div>
              )}
            </div>

            {/* Show discount amount if there is one */}
            {currentOriginalPrice && currentOriginalPrice > currentPrice && (
              <div className="mt-2 text-sm text-green-600">
                {t("SAVE")}: {formatPrice(currentOriginalPrice - currentPrice)}
              </div>
            )}
          </div>

          {/* SKU Selection - selection for color */}
          {skus.length > 0 && (
            <div className="mb-4">
              {/* Total Sold Information */}
              {(product.total_sold || product.sales) && (
                <div className="mb-3 flex items-center">
                  <div className="bg-orange-50 text-orange-600 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                    <ShoppingBag className="h-4 w-4 mr-1" />
                    {(
                      product.total_sold || product.sales
                    )?.toLocaleString()}{" "}
                    {t("SOLD")}
                  </div>
                </div>
              )}

              {/* Color Selection Section */}
              {
                isTranslating ? (
                  <div className="mb-4">
                    <Skeleton className="h-5 w-1/4 mb-2" />
                    <div className="flex flex-wrap gap-2">
                      {Array(4).fill(0).map((_, i) => (
                        <Skeleton key={`sku-${i}`} className="w-[60px] h-[60px] rounded-md" />
                      ))}
                    </div>
                  </div>
                ) : selectedColorType && groupedSkus[selectedColorType] ? (
                  <div className="mb-4">
                    <h3 className="text-sm font-semibold mb-2">
                      {/* 使用翻译后的颜色分类名称 */}
                      {groupedSkus[selectedColorType].translatedName ||
                        groupedSkus[selectedColorType].name}
                      : {selectedColorValText}
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {/* Check if there are any in-stock colors */}
                      <ColorSKUSelection
                        groupedSkus={groupedSkus}
                        selectedColorType={selectedColorType}
                        selectedColorValue={selectedColorValue}
                        product={product}
                        handleColorSelection={handleColorSelection}
                      />
                    </div>
                  </div>
                ) : null
              }

              {/* SKU selection if no color properties are identified */}
              <OtherSKUSelection
                selectedColorType={selectedColorType}
                selectedColorValue={selectedColorValue}
                selectedSkuProperties={selectedSkuProperties}
                product={product}
                skus={skus}
                groupedSkus={groupedSkus}
                selectedSkuName={selectedSkuName}
                handleSkuSelection={handleSkuSelection}
              />
            </div>
          )}

          {/* Quantity Selector */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-semibold">{tCommon("QUANTITY")}:</h3>
              <a
                href="https://calendly.com/miccobuy/15min"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline text-sm inline-flex items-center"
              >
                {t("LARGET_AMT")} <ExternalLink className="ml-1 h-3 w-3" />
              </a>
            </div>
            <div className="flex items-center">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => handleQuantityChange(-1)}
                disabled={selectedQuantity <= 1}
              >
                -
              </Button>
              <Input
                type="text"
                inputMode="numeric"
                value={selectedQuantity}
                onChange={handleDirectQuantityInput}
                className="w-16 h-8 text-center mx-2"
              />
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => handleQuantityChange(1)}
                disabled={
                  selectedQuantity >= availableQuantity ||
                  availableQuantity === 0
                }
              >
                +
              </Button>
              <span className="text-sm text-gray-500 ml-3">
                (
                {availableQuantity > 0
                  ? `${availableQuantity}`
                  : t("OUT_OF_STOCK")}
                )
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 mb-6">
            <Button
              size="lg"
              variant="outline"
              className="flex-1 border-orange-500 text-orange-500 hover:bg-orange-50"
              onClick={handleAddToCartClick}
              disabled={!selectedSkuProperties || availableQuantity === 0}
            >
              <ShoppingCart className="mr-2 h-4 w-4" /> {t("ADD_TO_CART")}
            </Button>
            <Button
              size="lg"
              className="flex-1 bg-orange-500 hover:bg-orange-600"
              onClick={handleShopNowClick}
              disabled={!selectedSkuProperties || availableQuantity === 0}
            >
              Shop Now
            </Button>
          </div>

          {/* Shipping Info */}
          <div className="border-t pt-4 mb-4 text-sm">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-semibold">{t("SHIPPING_INFO")}</h4>
            </div>
            <p className="text-gray-600 mb-2">
              {t("SHIPS_FROM")}: {translatedProductData.location || "N/A"}
            </p>
          </div>

          {/* Product Attributes/Details */}
          {attributes.length > 0 && (
            <div className="border-t pt-4 text-sm">
              <h4 className="font-semibold mb-2">{t("PRODUCT_DETAILS")}</h4>
              <ul className="list-inside space-y-1 text-gray-700">
                {attributes.map(
                  (attr: { name: string; value: string }, index: number) => (
                    <li key={`${attr.name}-${index}`}>
                      {
                        isTranslating ? <TranslationSkeleton /> : <span className="font-medium">{attr.name}: {attr.value}</span>
                      }
                    </li>
                  )
                )}
              </ul>
            </div>
          )}

          {/* Link to Original Product */}
          {translatedProductData.detail_url && (
            <div className="border-t pt-4 mt-4">
              <a
                href={translatedProductData.detail_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-blue-600 hover:underline inline-flex items-center"
              >
                {t("VIEW_ORIGINAL")} <ExternalLink className="ml-1 h-3 w-3" />
              </a>
            </div>
          )}
        </div>
      </div>

      {/* Add to Cart Confirmation Dialog */}
      <AlertDialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <CheckCircle2 className="h-5 w-5 mr-2 text-green-600" />
              {t("ADDED_TO_CART")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {selectedQuantity} x "{translatedProductData.title}" (
              {selectedSkuName || "Default"}) {t("HAS_BEEN_ADDED")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("CONTINUE_SHOPPING")}</AlertDialogCancel>
            <AlertDialogAction onClick={() => toggleCartPanel()}>
              {t("VIEW_CART")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

const TranslationSkeleton = ({
  width = "10vw",
  height = "1.25rem",
  inline = true
}: {
  width?: string
  height?: string
  inline?: boolean
}) => (
  <span
    className={cn("animate-pulse rounded-md bg-primary/10", inline ? 'inline-block' : '')}
    style={{ width, height }}
  ></span>
)


function ColorSKUSelection ({ groupedSkus, selectedColorType, selectedColorValue, handleColorSelection, product }: {
  groupedSkus: Record<string, SKUType>
  selectedColorType: string
  selectedColorValue: string | null
  handleColorSelection: (str: string) => void
  product: ProductItem
})  {
  const t = useTranslations(I18NNamespace.COMPONENTS_PRODUCT_DETAIL_VIEW);
  const filtered = groupedSkus[selectedColorType].values.filter((color) =>
    color.skus.some((sku) => sku.quantity > 0)
  );
  if (!filtered.length)
    return (
      <div className="w-full p-3 bg-gray-50 border border-gray-200 rounded-md text-center">
        <p className="text-red-500 font-medium">
          {t("ALL_COLORS_OUT")}
        </p>
        <p className="text-sm text-gray-500 mt-1">
          {t("CHECK_BACK_LATER")}
        </p>
      </div>
    );
  const colors = filtered.map(color => {
    // Find a representative SKU for this color to get its image
    const representativeSku = color.skus.find((sku) => sku.quantity > 0);
    let colorImageUrl = "";

    if (representativeSku) {
      colorImageUrl = findSkuImage(representativeSku, product.prop_imgs?.prop_img || [], product) || "";
    }
    return { ...color, colorImageUrl };
  });
  return colors.map((color) => {
    return (
      <Button
        key={`color-${color.id}`}
        variant={
          selectedColorValue === color.id
            ? "default"
            : "outline"
        }
        size="sm"
        onClick={() => handleColorSelection(color.id)}
        className={cn(
          "h-auto p-2 flex flex-col items-center",
          "hover:border-primary",
          selectedColorValue === color.id
            ? "border-2 border-primary bg-primary/10 text-black"
            : ""
        )}
        title={color.name}
      >
        <Image
          src={getImageUrl(color.colorImageUrl)}
          alt={color.name}
          width={50}
          height={50}
          className="rounded-sm object-cover"
          title={color.name}
          loading="lazy"
          onError={(e) => {
            // If image fails to load, hide the image or use a fallback
            const target = e.target as HTMLImageElement;
            // Hide the image by setting opacity to 0
            target.style.opacity = "0";
            // Add a background color to show something in place of the image
            target.parentElement!.style.backgroundColor =
              "#f3f4f6";
          }}
        />
        <span
          className={`text-xs mt-1 max-w-[80px] truncate font-medium ${
            selectedColorValue === color.id
              ? "text-black"
              : ""
          }`}
        >
          {color.translatedName || color.name}
        </span>
      </Button>
    );
  });
}

function OtherSKUSelection ({ selectedColorType, selectedColorValue, groupedSkus, selectedSkuName, skus, product, selectedSkuProperties, handleSkuSelection }: {
  selectedColorType: string | null
  selectedColorValue: string | null
  selectedSkuProperties: string | null
  product: ProductItem
  skus: SkuItem[]
  groupedSkus: Record<string, SKUType>
  selectedSkuName: string | null
  handleSkuSelection: (sku: SkuItem) => void
}) {
  const t = useTranslations(I18NNamespace.COMPONENTS_PRODUCT_DETAIL_VIEW);
  const skusWOColor = Object.keys(groupedSkus).filter(type => type !== selectedColorType);
  if (!selectedColorType || !skusWOColor.length || !selectedColorValue) return null;
  const skuType = groupedSkus[skusWOColor[0]];
  const filtered = groupedSkus[selectedColorType]
    .values.find(it => it.id === selectedColorValue)
    ?.skus.filter((sku) => sku.quantity > 0);
  if (!filtered?.length) return null;
  const filteredSkus = filtered.map(sku => {
    const displayName = getSkuDisplayName(sku.properties_name);
    const propImgs = product.prop_imgs?.prop_img || [];
    const skuImageUrl = findSkuImage(sku, propImgs, product);
    return { ...sku, displayName, skuImageUrl }
  });
  return (
    <div>
      <h3 className="text-sm font-semibold mb-2">
        {skuType.translatedName || skuType.name}: {selectedSkuName || t("SELECT_OPT")}
      </h3>
      <div className="flex flex-wrap gap-2">
        {/* Check if there are any in-stock SKUs */}
        {filtered.length === 0 ? (
          <div className="w-full p-3 bg-gray-50 border border-gray-200 rounded-md text-center">
            <p className="text-red-500 font-medium">
              {t("ALL_VARIATIONS_OUT")}
            </p>
            <p className="text-sm text-gray-500 mt-1">
              {t("CHECK_BACK_LATER")}
            </p>
          </div>
        ) : (
          /* Filter out SKUs with quantity 0 (out of stock) */
          filteredSkus.map((sku) => {
            return (
              <Button
                key={sku.sku_id}
                variant={
                  selectedSkuProperties === sku.properties
                    ? "default"
                    : "outline"
                }
                size="sm"
                onClick={() => handleSkuSelection(sku)}
                className={cn(
                  "h-auto p-2 flex flex-col items-center",
                  "hover:border-primary",
                  selectedSkuProperties === sku.properties
                    ? "border-2 border-primary bg-primary/10 text-black"
                    : ""
                )}
                title={sku.displayName}
              >
                <Image
                  src={getImageUrl(sku.skuImageUrl)}
                  alt={sku.displayName}
                  width={50}
                  height={50}
                  className="rounded-sm object-cover"
                  title={sku.displayName}
                  loading="lazy"
                  onError={(e) => {
                    // If image fails to load, hide the image or use a fallback
                    const target = e.target as HTMLImageElement;
                    // Hide the image by setting opacity to 0
                    target.style.opacity = "0";
                    // Add a background color to show something in place of the image
                    target.parentElement!.style.backgroundColor =
                      "#f3f4f6";
                  }}
                />
                <span
                  className={`text-xs mt-1 max-w-[80px] truncate font-medium ${
                    selectedSkuProperties === sku.properties
                      ? "text-black"
                      : ""
                  }`}
                >
                  {sku.displayName}
                </span>
              </Button>
            );
          })
        )}
      </div>
    </div>
  )
}
