"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import { RefreshCw, AlertTriangle, Home } from "lucide-react";

interface ProductDetailErrorProps {
  errorCode?: string;
  errorMessage?: string;
  productId?: string;
  source?: string;
  onRetry?: () => void;
}

export function ProductDetailError({
  errorCode = "unknown",
  errorMessage = "",
  productId = "",
  source = "",
  onRetry
}: ProductDetailErrorProps) {
  const router = useRouter();
  const t = useTranslations(I18NNamespace.COMPONENTS_PRODUCTS);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  // Determine if this is a 5000 error code
  const is5000Error = errorCode === "5000" || errorMessage.includes("5000");

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      // Refresh the page if no retry handler is provided
      window.location.reload();
    }
  };

  const handleGoHome = () => {
    router.push("/");
  };

  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
      <div className="bg-red-50 p-4 rounded-full mb-6">
        <AlertTriangle className="h-12 w-12 text-red-500" />
      </div>
      
      <h1 className="text-2xl font-bold mb-4">
        {is5000Error ? t("ERROR_5000_TITLE") : t("PRODUCT_ERROR_TITLE")}
      </h1>
      
      <p className="text-gray-600 mb-6 max-w-md">
        {is5000Error 
          ? t("ERROR_5000_MESSAGE") 
          : t("PRODUCT_ERROR_MESSAGE")}
      </p>
      
      {productId && (
        <p className="text-sm text-gray-500 mb-2">
          {tCommon("PRODUCT_ID")}: {productId}
        </p>
      )}
      
      {source && (
        <p className="text-sm text-gray-500 mb-6">
          {tCommon("SOURCE")}: {source}
        </p>
      )}
      
      <div className="flex flex-col sm:flex-row gap-4">
        <Button 
          onClick={handleRetry}
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          {t("RETRY")}
        </Button>
        
        <Button 
          variant="outline" 
          onClick={handleGoHome}
          className="flex items-center gap-2"
        >
          <Home className="h-4 w-4" />
          {t("GO_HOME")}
        </Button>
      </div>
    </div>
  );
}
