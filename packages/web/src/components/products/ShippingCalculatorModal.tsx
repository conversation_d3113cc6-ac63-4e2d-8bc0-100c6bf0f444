"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose, // Import DialogClose
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label"; // Import Label
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"; // Import RadioGroup
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"; // Import Select
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

interface ShippingCalculatorModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

// Dummy country list for the dropdown
const countries = [
  { value: "us", label: "United States" },
  { value: "ca", label: "Canada" },
  { value: "gb", label: "United Kingdom" },
  { value: "au", label: "Australia" },
  { value: "de", label: "Germany" },
  { value: "fr", label: "France" },
  // Add more countries as needed
];

// Commodity attributes
const commodityAttributes = [
    "Electric", "Liquid", "Knives", "Powder", "Shoes",
    "Bags", "Food", "Battery", "Cosmetics", "Magnetic", "Watch"
];

export function ShippingCalculatorModal({ isOpen, onOpenChange }: ShippingCalculatorModalProps) {
  const [weight, setWeight] = useState("");
  const [length, setLength] = useState("");
  const [width, setWidth] = useState("");
  const [height, setHeight] = useState("");
  const [selectedAttribute, setSelectedAttribute] = useState<string | null>(null);
  const [destinationCountry, setDestinationCountry] = useState("");
  const [estimatedCost, setEstimatedCost] = useState<number | null>(null);
  const t = useTranslations(I18NNamespace.COMPONENTS_SHIPPING_CALCULATOR_MODAL);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  const handleCalculate = () => {
    // Placeholder calculation logic
    console.log("Calculating shipping cost with:", {
      weight,
      length,
      width,
      height,
      attribute: selectedAttribute,
      country: destinationCountry,
    });
    // Simulate calculation
    const calculatedCost = Math.random() * 50 + 10; // Random cost between 10 and 60
    setEstimatedCost(calculatedCost);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]"> {/* Increased width */}
        <DialogHeader>
          <DialogTitle>{t("COST_CALCULATOR")}</DialogTitle>
          <DialogDescription>
            {t("FILL_OUT")}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {/* Weight */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="weight" className="text-right col-span-1">
              {tCommon("WEIGHT")} ({tCommon("G")})
            </Label>
            <Input
              id="weight"
              type="number"
              placeholder={t("ENTER_WEIGHT")}
              value={weight}
              onChange={(e) => setWeight(e.target.value)}
              className="col-span-3"
            />
          </div>
          {/* Dimensions */}
          <div className="grid grid-cols-4 items-center gap-4">
             <Label className="text-right col-span-1">
                {t('VOLUME')} (cm³)
             </Label>
             <div className="col-span-3 grid grid-cols-3 gap-2">
                 <Input
                    id="length"
                    type="number"
                    placeholder={tCommon("LENGTH") + "(cm)"}
                    value={length}
                    onChange={(e) => setLength(e.target.value)}
                 />
                 <Input
                    id="width"
                    type="number"
                    placeholder={tCommon("WIDTH") + "(cm)"}
                    value={width}
                    onChange={(e) => setWidth(e.target.value)}
                 />
                 <Input
                    id="height"
                    type="number"
                    placeholder={tCommon("HEIGHT") + "(cm)"}
                    value={height}
                    onChange={(e) => setHeight(e.target.value)}
                 />
             </div>
          </div>
           {/* Commodity Attributes */}
          <div className="grid grid-cols-4 items-start gap-4"> {/* Changed items-center to items-start */}
            <Label className="text-right col-span-1 pt-2"> {/* Added padding-top */}
              {t("COMMODITY_ATTRS")}
            </Label>
            <RadioGroup
                value={selectedAttribute ?? undefined} // Handle null state
                onValueChange={setSelectedAttribute}
                className="col-span-3 grid grid-cols-3 sm:grid-cols-4 gap-x-4 gap-y-2" // Adjusted grid columns
            >
                {commodityAttributes.map((attr) => (
                <div key={attr} className="flex items-center space-x-2">
                    <RadioGroupItem value={attr} id={`attr-${attr}`} />
                    <Label htmlFor={`attr-${attr}`} className="font-normal">{attr}</Label>
                </div>
                ))}
            </RadioGroup>
          </div>
          {/* Country of Destination */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="destination" className="text-right col-span-1">
              {t("COUNTRY_OF_DST")}
            </Label>
            <Select value={destinationCountry} onValueChange={setDestinationCountry}>
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder={t("SELECT_COUNTRY_DST")} />
              </SelectTrigger>
              <SelectContent>
                {countries.map((country) => (
                  <SelectItem key={country.value} value={country.value}>
                    {country.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {/* Estimated Cost Display */}
           {estimatedCost !== null && (
            <div className="grid grid-cols-4 items-center gap-4 mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                 <Label className="text-right col-span-1 font-medium text-green-800">{t("ESTIMATION")}:</Label>
                 <div className="col-span-3 text-green-700 font-semibold">
                    ${estimatedCost.toFixed(2)}
                 </div>
            </div>
           )}

           {/* Shipment Knowledge Section */}
           <div className="mt-6 pt-4 border-t border-gray-200 text-xs text-gray-500 space-y-2">
                <h4 className="font-medium text-gray-700 mb-1">{t("SHIPMENT_KNOWLEDGE")}:</h4>
                <p>1. {t("KNOWLEDGE_1")}</p>
                <p>2. {t("KNOWLEDGE_2")}</p>
                <p>3. {t("KNOWLEDGE_3")}</p>
                <p>4. {t("KNOWLEDGE_4")}</p>
            </div>
        </div>
        <DialogFooter>
           {/* Add DialogClose to the Cancel button */}
           <DialogClose asChild>
             <Button type="button" variant="outline">{tCommon("CANCEL")}</Button>
           </DialogClose>
          <Button type="button" onClick={handleCalculate} className="bg-green-600 hover:bg-green-700">
            {t("CALCULATION_ESTIMATION")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
