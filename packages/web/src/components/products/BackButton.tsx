"use client";

import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';
import { useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation';

export function BackButton() {
  const router = useRouter();
  const searchParams = useSearchParams(); // Read params from the current (detail page) URL
  const t = useTranslations(I18NNamespace.COMPONENTS_PRODUCTS);

  const handleBack = () => {
    // Construct the target URL for the home page with search params
    const params = new URLSearchParams(searchParams.toString());
    // params.set('showResults', 'true'); // Add flag to ensure results are shown

    // Check if this was an image search
    const searchType = searchParams.get('searchType');
    if (searchType === 'image') {
      // Set a flag to indicate we're returning from an image search
      params.set('fromImageSearch', 'true');

      // Store the current timestamp to ensure we can identify this specific navigation
      const timestamp = new Date().getTime();
      params.set('ts', timestamp.toString());

      try {
        // Store a flag in localStorage to indicate we should restore image search results
        localStorage.setItem('restoreImageSearch', 'true');
        localStorage.setItem('restoreImageSearchTimestamp', timestamp.toString());
      } catch (e) {
        console.warn('Could not save restore flag to localStorage:', e);
      }
    }

    // Navigate to home page with preserved search state
    router.push(`/?${params.toString()}`);
  };

  return (
    <button
      onClick={handleBack}
      className="text-accio-primary flex items-center mb-6 hover:underline"
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
        <path d="M19 12H5" />
        <path d="M12 19l-7-7 7-7" />
      </svg>
      {t("BACK_TO_SEARCH")}
    </button>
  );
}
