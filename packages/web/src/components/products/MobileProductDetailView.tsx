"use client";
import Image from 'next/image';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ExternalLink, ShoppingCart, CheckCircle2, ShoppingBag, Minus, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { formatPrice } from "@/utils/currency";
import { ShippingCalculatorButton } from "@/components/products/ShippingCalculatorButton";
import {
  ProductDetailViewProps,
  getSkuDisplayName,
  getImageUrl,
  extractPropertyParts,
  findSkuImage,
  useProductDetailHook,
  SKUType,
  ProductItem,
  SkuItem
} from "@/components/products/ProductDetailView";
import { ProductDetailSkeleton } from "./ProductDetailSkeleton";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import { Skeleton } from '../ui/skeleton';


// Fallback helper function in case the imported one fails
const getImageUrlFallback = (url: string | undefined): string => {
  if (!url) return '/images/placeholder.png';
  return url.startsWith('//') ? `https:${url}` : url;
};

export function MobileProductDetailView({ product, imagesLoaded = true }: ProductDetailViewProps) {
  
  const {
    t, tCommon, translatedProductData, skus, groupedSkus, selectedColorType, selectedColorValue, selectedSkuProperties, selectedSkuName, selectedColorValText,
    isTranslating, imageLoadingState, setImageLoadingState,
    itemImgs, selectedImageIndex, mainPreviewImageUrl, sellerInfo, title, shopName,
    attributes, showConfirmation, setShowConfirmation, availableQuantity, currentPrice, selectedQuantity, currentOriginalPrice,
    handleQuantityChange, handleDirectQuantityInput, handleItemImageSelect, handleAddToCartClick,
    handleShopNowClick, toggleCartPanel, handleColorSelection, handleSkuSelection
  } = useProductDetailHook({ product, imagesLoaded });

  // Safety check for product data
  if (!translatedProductData) {
    return <div className="p-8 text-center">{t("DATA_NOT_AVAILABLE")}</div>;
  }

  // Check required properties
  if (!translatedProductData.num_iid || !translatedProductData.title) {
    return <div className="p-8 text-center">{t("INVALID_DATA")}</div>;
  }


  return (
    <div className="pb-28"> {/* Add padding at the bottom to account for fixed buttons */}
      {/* Product Title and Store Info */}
      <div className="mb-4">
        { isTranslating ? <TranslationSkeleton height='2rem' /> : <h1 className="text-xl font-bold mb-2">{title}</h1> }
        <div className="text-sm text-gray-600">
          <span>
            {tCommon('STORE')}: { isTranslating ? <TranslationSkeleton /> : shopName }
          </span>
          <div className="mt-1">{tCommon("SOURCE")}: {tCommon("VIA")}</div>
          <a
            href="https://calendly.com/miccobuy/15min"
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline text-sm inline-flex items-center"
          >
            {t("CUSTOMIZATION_TALK")} <ExternalLink className="ml-1 h-3 w-3" />
          </a>
        </div>
      </div>

      {/* Main Image */}
      <div className="mb-4 border rounded-lg overflow-hidden relative">
        {imageLoadingState === 'loading' && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-lg" />
        )}
        <Image
          src={mainPreviewImageUrl}
          alt={product.title || 'Product Image'}
          width={600}
          height={600}
          className={`w-full h-auto object-cover transition-opacity duration-300 ${
            imageLoadingState === 'loaded' ? 'opacity-100' : 'opacity-0'
          }`}
          priority
          onLoad={() => setImageLoadingState('loaded')}
          onError={(e) => {
            setImageLoadingState('error');
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            target.parentElement!.style.backgroundColor = '#f3f4f6';
            const placeholderText = document.createElement('div');
            placeholderText.className = 'flex items-center justify-center h-full text-gray-400';
            placeholderText.textContent = 'Image not available';
            target.parentElement!.appendChild(placeholderText);
          }}
        />
      </div>

      {/* Horizontal Thumbnails below main image */}
      <div className="mb-6 overflow-x-auto">
        <div className="mobile-product-thumbnails">
          {/* Main product image as first thumbnail */}
          <button
            key="main-thumb"
            className={cn(
              "border rounded-md overflow-hidden flex-shrink-0 w-[70px] h-[70px]",
              mainPreviewImageUrl === (product.pic_url.startsWith('//') ? `https:${product.pic_url}` : product.pic_url) ? "border-orange-500" : "border-gray-200"
            )}
            onClick={() => handleItemImageSelect(product.pic_url, -1)}
          >
            {imageLoadingState === 'loading' ? (
              <div className="bg-gray-200 animate-pulse w-full h-full" />
            ) : (
              <Image
                src={typeof getImageUrl === 'function' ? getImageUrl(product.pic_url) : getImageUrlFallback(product.pic_url)}
                alt="Main product thumbnail"
                width={70}
                height={70}
                className="object-cover w-full h-full"
                loading="lazy"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.opacity = '0';
                  target.parentElement!.style.backgroundColor = '#f3f4f6';
                }}
              />
            )}
          </button>

          {/* General item images */}
          {itemImgs.map((img: any, index: number) => (
            <button
              key={`item-img-${index}`}
              className={cn(
                "border rounded-md overflow-hidden flex-shrink-0 w-[70px] h-[70px]",
                selectedImageIndex === index ? "border-orange-500" : "border-gray-200"
              )}
              onClick={() => handleItemImageSelect(img.url, index)}
            >
              {imageLoadingState === 'loading' ? (
                <div className="bg-gray-200 animate-pulse w-full h-full" />
              ) : (
                <Image
                  src={typeof getImageUrl === 'function' ? getImageUrl(img.url) : getImageUrlFallback(img.url)}
                  alt={`Product image ${index + 1}`}
                  width={70}
                  height={70}
                  className="object-cover w-full h-full"
                  loading="lazy"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.opacity = '0';
                    target.parentElement!.style.backgroundColor = '#f3f4f6';
                  }}
                />
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Price */}
      <div className="mb-4">
        <div className="flex items-center flex-wrap">
          <span className="text-3xl font-bold text-orange-500">{formatPrice(currentPrice)}</span>
        </div>

        <div className="flex items-center mt-1">
          <div className="text-xs text-gray-500 mr-2">
            {t("NOT_INCLUDING")} {t("INTERNATIONAL_SHIPPING_COST")}
          </div>
          <ShippingCalculatorButton />
        </div>

        {currentOriginalPrice && currentOriginalPrice > currentPrice && (
          <div className="flex items-center mt-2 flex-wrap">
            <div className="flex items-center">
              <span className="text-sm text-gray-400">{t("BEFORE_DISCOUNT")}</span>
              <span className="text-sm text-gray-400 line-through ml-1">{formatPrice(currentOriginalPrice)}</span>
            </div>
          </div>
        )}

        {/* Show discount amount if there is one */}
        {currentOriginalPrice && currentOriginalPrice > currentPrice && (
          <div className="mt-2 text-sm text-green-600">
            {t("SAVE")}: {formatPrice(currentOriginalPrice - currentPrice)}
          </div>
        )}
      </div>

      {/* Total Sold Information */}
      {((product.total_sold && product.total_sold > 0) || (product.sales && product.sales > 0)) && (
        <div className="mb-3 flex items-center">
          <div className="bg-orange-50 text-orange-600 px-3 py-1 rounded-full text-sm font-medium flex items-center">
            <ShoppingBag className="h-4 w-4 mr-1" />
            {(product.total_sold || product.sales || 0).toLocaleString()} {t("SOLD")}
          </div>
        </div>
      )}

      {/* SKU Selection - Two-level selection for color */}
      {skus.length > 0 && (
        <div className="mb-4">
          {/* Color Selection Section */}
          <SelectedColorSKU
            isTranslation={isTranslating}
            selectedColorType={selectedColorType}
            selectedColorValue={selectedColorValue}
            selectedColorValText={selectedColorValText}
            groupedSkus={groupedSkus}
            product={product}
            handleColorSelection={handleColorSelection}
          />

          {/* Other SKUs */}
          <OtherSKUSelection
            isTranslation={isTranslating}
            selectedColorType={selectedColorType}
            selectedColorValue={selectedColorValue}
            selectedSkuProperties={selectedSkuProperties}
            selectedSkuName={selectedSkuName}
            groupedSkus={groupedSkus}
            product={product}
            handleSkuSelection={handleSkuSelection}
          />
        </div>
      )}

      {/* Quantity Selector */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-semibold">{tCommon("QUANTITY")}:</h3>
          <a
            href="https://calendly.com/miccobuy/15min"
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline text-sm inline-flex items-center"
          >
            {t("LARGET_AMT")} <ExternalLink className="ml-1 h-3 w-3" />
          </a>
        </div>
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={() => handleQuantityChange(-1)}
            disabled={selectedQuantity <= 1}
          >
            <Minus className="h-4 w-4" />
          </Button>
          <Input
            type="text"
            inputMode="numeric"
            value={selectedQuantity}
            onChange={handleDirectQuantityInput}
            className="w-16 h-8 text-center mx-2"
          />
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={() => handleQuantityChange(1)}
            disabled={selectedQuantity >= availableQuantity || availableQuantity === 0}
          >
            <Plus className="h-4 w-4" />
          </Button>
          <span className="text-sm text-gray-500 ml-3">
            ({availableQuantity > 0 ? `${availableQuantity}` : t("OUT_OF_STOCK")})
          </span>
        </div>
      </div>

      {/* Shipping Info */}
      <div className="border-t pt-4 mb-4 text-sm">
        <div className="flex justify-between items-center mb-2">
          <h4 className="font-semibold">{t("SHIPPING_INFO")}</h4>
        </div>
        <p className="text-gray-600 mb-2">{t("SHIPS_FROM")}: {translatedProductData.location || 'N/A'}</p>
      </div>

      {/* Product Attributes/Details */}
      {translatedProductData.props && translatedProductData.props.length > 0 && (
        <div className="border-t pt-4 text-sm mb-4">
          <h4 className="font-semibold mb-2">{t("PRODUCT_DETAILS")}</h4>
          <ul className="list-inside space-y-1 text-gray-700">
            {translatedProductData.props.map((attr: any, index: number) => (
              <li key={`${attr.name}-${index}`}>
                {
                  isTranslating ? <TranslationSkeleton /> : <span className="font-medium">{attr.name}: {attr.value}</span>
                }
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Description Images */}
      {product.desc_img && product.desc_img.length > 0 && (
        <div className="border-t pt-4 mb-4">
          <h3 className="text-lg font-semibold mb-3">{t("PRODUCT_DESC")}</h3>
          <div className="space-y-4">
            {product.desc_img.map((imgUrl: any, index: number) => (
              <div key={`desc-img-${index}`} className="border rounded-lg overflow-hidden">
                {imageLoadingState === 'loading' ? (
                  <div className="bg-gray-200 animate-pulse w-full h-[300px]" />
                ) : (
                  <Image
                    src={getImageUrl(imgUrl)}
                    alt={`Product description ${index + 1}`}
                    width={600}
                    height={400}
                    className="w-full h-auto object-contain"
                    loading="lazy"
                    onError={(e) => {
                      // If description image fails to load, hide it
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      // Remove the parent container to avoid empty spaces
                      const container = target.closest('div');
                      if (container) {
                        container.style.display = 'none';
                      }
                    }}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Link to Original Product */}
      {translatedProductData.detail_url && (
        <div className="border-t pt-4 mb-4">
          <a
            href={translatedProductData.detail_url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm text-blue-600 hover:underline inline-flex items-center"
          >
            {t("VIEW_ORIGINAL")} <ExternalLink className="ml-1 h-3 w-3" />
          </a>
        </div>
      )}

      {/* Fixed bottom buttons */}
      <div className="mobile-product-fixed-buttons">
        <Button
          className="flex-1 bg-[#FFF1EE] text-[#FF4D00] hover:bg-[#FFE4DE] border-none rounded-l-full rounded-r-none text-base font-medium"
          onClick={handleAddToCartClick}
          disabled={!selectedSkuProperties || availableQuantity === 0}
        >
          {t("ADD_TO_CART")}
        </Button>
        <Button
          className="flex-1 bg-[#FF4D00] hover:bg-[#E64500] text-white border-none rounded-r-full rounded-l-none text-base font-medium"
          onClick={handleShopNowClick}
          disabled={!selectedSkuProperties || availableQuantity === 0}
        >
           {t("SHOP_NOW")}
        </Button>
      </div>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <CheckCircle2 className="h-5 w-5 mr-2 text-green-600" />
              {t("ADDED_TO_CART")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {selectedQuantity} x "{translatedProductData.title}" ({selectedSkuName || 'Default'}) {t("HAS_BEEN_ADDED")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={() => setShowConfirmation(false)}>
              {t("CONTINUE_SHOPPING")}
            </AlertDialogAction>
            <AlertDialogAction onClick={() => toggleCartPanel()}>
              {t("VIEW_CART")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

const TranslationSkeleton = ({
  width = "50vw",
  height = "1.25rem",
  inline = true
}: {
  width?: string
  height?: string
  inline?: boolean
}) => (
  <span
    className={cn("animate-pulse rounded-md bg-primary/10", inline ? 'inline-block' : '')}
    style={{ width, height }}
  ></span>
)

function SelectedColorSKU ({ isTranslation, selectedColorType, groupedSkus, selectedColorValText, selectedColorValue, product, handleColorSelection }: {
  isTranslation: boolean
  groupedSkus: Record<string, SKUType>
  selectedColorType: string | null
  selectedColorValue: string | null
  selectedColorValText: string
  product: ProductItem
  handleColorSelection: (colorId: string) => void
}) {
  const t = useTranslations(I18NNamespace.COMPONENTS_PRODUCT_DETAIL_VIEW);
  if (!selectedColorType) return null;
  const colorSKU = groupedSkus[selectedColorType];
  if (!colorSKU) return null;
  const filtered = colorSKU.values.filter(color => color.skus.some(sku => sku.quantity > 0));

  return (
    <div className="mb-4">
      {
        isTranslation ?
          <TranslationSkeleton /> :
          <h3 className="text-sm font-semibold mb-2">
            {colorSKU.translatedName || colorSKU.name}: {selectedColorValText || t("SELECT_OPT")}
          </h3>
      }
      <div className="flex flex-wrap gap-2">
        {filtered.length === 0 ? (
          <div className="w-full p-3 bg-gray-50 border border-gray-200 rounded-md text-center">
            <p className="text-red-500 font-medium">{t("ALL_COLORS_OUT")}</p>
            <p className="text-sm text-gray-500 mt-1">{t("CHECK_BACK_LATER")}</p>
          </div>
        ) : (
          /* Filter out colors with all SKUs out of stock */
          filtered.map((color) => {
            // Find a representative SKU for this color to get its image
            const representativeSku = color.skus.find((sku) => sku.quantity > 0);
            let colorImageUrl = '';

            if (representativeSku) {
              colorImageUrl = findSkuImage(representativeSku, product.prop_imgs?.prop_img || [], product) || '';
            }

            return (
              <Button
                key={`color-${color.id}`}
                variant={selectedColorValue === color.id ? "default" : "outline"}
                size="sm"
                onClick={() => handleColorSelection(color.id)}
                className={cn(
                  "h-auto p-2 flex flex-col items-center",
                  "hover:border-primary",
                  selectedColorValue === color.id ? "border-2 border-primary bg-primary/10 text-black" : ""
                )}
                title={color.name}
              >
                <Image
                  src={getImageUrl(colorImageUrl)}
                  alt={color.name}
                  width={50}
                  height={50}
                  className="rounded-sm object-cover"
                  title={color.name}
                  loading="lazy"
                  onError={(e) => {
                    // If image fails to load, hide the image or use a fallback
                    const target = e.target as HTMLImageElement;
                    // Hide the image by setting opacity to 0
                    target.style.opacity = '0';
                    // Add a background color to show something in place of the image
                    target.parentElement!.style.backgroundColor = '#f3f4f6';
                  }}
                />
                {
                  isTranslation ?
                  <TranslationSkeleton /> :
                  <span className={`text-xs mt-1 max-w-[80px] truncate font-medium ${selectedColorValue === color.id ? 'text-black' : ''}`}>
                    {color.translatedName || color.name}
                  </span>
                }
              </Button>
            );
          })
        )}
      </div>
    </div>
  )
}

function OtherSKUSelection ({ isTranslation, groupedSkus, selectedColorType, selectedColorValue, selectedSkuName, selectedSkuProperties, product, handleSkuSelection }: {
  isTranslation: boolean,
  selectedColorType: string | null
  selectedColorValue: string | null
  selectedSkuProperties: string | null
  selectedSkuName: string | null
  groupedSkus: Record<string, SKUType>
  product: ProductItem
  handleSkuSelection: (sku: SkuItem) => void
}) {
  const t = useTranslations(I18NNamespace.COMPONENTS_PRODUCT_DETAIL_VIEW);
  const skusWOColor = Object.keys(groupedSkus).filter(type => type !== selectedColorType);
  if (!selectedColorType || !skusWOColor.length || !selectedColorValue) return null;
  const skuType = groupedSkus[skusWOColor[0]];
  const filtered = groupedSkus[selectedColorType]
    .values.find(it => it.id === selectedColorValue)
    ?.skus.filter((sku) => sku.quantity > 0);
  if (!filtered?.length) return null;
  const filteredSkus = filtered.map(sku => {
    const displayName = getSkuDisplayName(sku.properties_name);
    const propImgs = product.prop_imgs?.prop_img || [];
    const skuImageUrl = findSkuImage(sku, propImgs, product);
    return { ...sku, displayName, skuImageUrl }
  });
  return (
    <div>
      <h3 className="text-sm font-semibold mb-2">
        {skuType.translatedName || skuType.name}: {selectedSkuName || t("SELECT_OPT")}
      </h3>
      <div className="flex flex-wrap gap-2">
        {/* Check if there are any in-stock SKUs */}
        {filtered.length === 0 ? (
          <div className="w-full p-3 bg-gray-50 border border-gray-200 rounded-md text-center">
            <p className="text-red-500 font-medium">{t("ALL_VARIATIONS_OUT")}</p>
            <p className="text-sm text-gray-500 mt-1">{t("CHECK_BACK_LATER")}</p>
          </div>
        ) : (
          /* Filter out SKUs with quantity 0 (out of stock) */
          filteredSkus.map((sku) => {
            // 获取原始的显示名称
            const displayName = getSkuDisplayName(sku.properties_name);
            const propImgs = product.prop_imgs?.prop_img || [];
            const skuImageUrl = findSkuImage(sku, propImgs, product);

            return (
              <Button
                key={sku.sku_id}
                variant={selectedSkuProperties === sku.properties ? "default" : "outline"}
                size="sm"
                onClick={() => handleSkuSelection(sku)}
                className={cn(
                  "h-auto p-2 flex flex-col items-center",
                  "hover:border-primary",
                  selectedSkuProperties === sku.properties ? "border-2 border-primary bg-primary/10 text-black" : ""
                )}
                title={displayName}
              >
                <Image
                  src={getImageUrl(skuImageUrl)}
                  alt={displayName}
                  width={50}
                  height={50}
                  className="rounded-sm object-cover"
                  title={displayName}
                  loading="lazy"
                  onError={(e) => {
                    // If image fails to load, hide the image or use a fallback
                    const target = e.target as HTMLImageElement;
                    // Hide the image by setting opacity to 0
                    target.style.opacity = '0';
                    // Add a background color to show something in place of the image
                    target.parentElement!.style.backgroundColor = '#f3f4f6';
                  }}
                />
                {
                  isTranslation ?
                  <TranslationSkeleton /> :
                  <span className={`text-xs mt-1 max-w-[80px] truncate font-medium ${selectedSkuProperties === sku.properties ? 'text-black' : ''}`}>{displayName}</span>
                }
              </Button>
            );
          })
        )}
      </div>
    </div>
  )
}
