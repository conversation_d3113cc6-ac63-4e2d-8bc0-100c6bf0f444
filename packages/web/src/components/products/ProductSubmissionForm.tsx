"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { validateProduct } from '@/utils/product-validator';
import { ProhibitedPurchaseAlert } from '@/components/shared/ProhibitedPurchaseAlert';
import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';

export function ProductSubmissionForm() {
  const [productName, setProductName] = useState('');
  const [productUrl, setProductUrl] = useState('');
  const [productDescription, setProductDescription] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [prohibitedMessage, setProhibitedMessage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const t = useTranslations(I18NNamespace.COMPONENTS_PRODUCT_SUBMISSION_FORM);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate the product name against prohibited keywords
    const validationResult = validateProduct(productName);

    if (validationResult.isProhibited) {
      setProhibitedMessage(validationResult.message || t("CANNOT_BE_PURCHASED"));
      setIsSubmitting(false);
      return;
    }

    if (validationResult.requiresReview) {
      // Handle products that require review
      // For example, show a different message or flag the order
      console.log('Product requires review:', validationResult);
    }

    // If not prohibited, proceed with submission
    // This would typically be an API call
    console.log('Submitting product:', {
      productName,
      productUrl,
      productDescription,
      quantity
    });

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      alert(t("SUBLITTED_SUCCESSFULLY"));
      // Reset form
      setProductName('');
      setProductUrl('');
      setProductDescription('');
      setQuantity(1);
    }, 1000);
  };

  const handleConfirmProhibited = () => {
    setProhibitedMessage(null);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">{t("SUBMIT_PRODUCT")}</h2>

      {prohibitedMessage && (
        <ProhibitedPurchaseAlert
          message={prohibitedMessage}
          onConfirm={handleConfirmProhibited}
        />
      )}

      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div>
            <Label htmlFor="productName">{t("PRODUCT_NAME")}</Label>
            <Input
              id="productName"
              value={productName}
              onChange={(e) => setProductName(e.target.value)}
              placeholder={t("ENTER_PRODUCT_NAME")}
              required
            />
          </div>

          <div>
            <Label htmlFor="productUrl">{t("PRODUCT_URL")}</Label>
            <Input
              id="productUrl"
              type="url"
              value={productUrl}
              onChange={(e) => setProductUrl(e.target.value)}
              placeholder="https://example.com/product"
              required
            />
          </div>

          <div>
            <Label htmlFor="productDescription">{t("PRODUCT_DESCRIPTION")}</Label>
            <Textarea
              id="productDescription"
              value={productDescription}
              onChange={(e) => setProductDescription(e.target.value)}
              placeholder={t("ENTER_PRODUCT_DESC")}
              rows={4}
            />
          </div>

          <div>
            <Label htmlFor="quantity">{tCommon("QUANTITY")}</Label>
            <Input
              id="quantity"
              type="text"
              inputMode="numeric"
              value={quantity}
              onChange={(e) => {
                // Only allow numeric input
                const inputValue = e.target.value;
                if (/^\d*$/.test(inputValue)) {
                  if (inputValue === '') {
                    setQuantity(1);
                  } else {
                    const value = parseInt(inputValue, 10);
                    if (!isNaN(value) && value > 0) {
                      setQuantity(value);
                    }
                  }
                }
              }}
              required
            />
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={isSubmitting}
          >
            {isSubmitting ? t("SUBMITTING") : t("SUBMIT_PRODUCT_1")}
          </Button>
        </div>
      </form>
    </div>
  );
}
