"use client";

import { useState, useEffect } from "react";
import { Loader2, CheckCircle2 } from "lucide-react";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

interface TypingAnimationProps {
  text: string;
  delay?: number;
  speed?: number;
  onComplete?: () => void;
  isCompleted?: boolean;
}

export function TypingAnimation({
  text,
  delay = 0,
  speed = 30,
  onComplete,
  isCompleted = false,
}: TypingAnimationProps) {
  const [displayedText, setDisplayedText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [startAnimation, setStartAnimation] = useState(false);

  useEffect(() => {
    const delayTimer = setTimeout(() => {
      setStartAnimation(true);
    }, delay);

    return () => clearTimeout(delayTimer);
  }, [delay]);

  useEffect(() => {
    if (!startAnimation) return;

    let timer: NodeJS.Timeout;

    if (currentIndex < text.length) {
      timer = setTimeout(() => {
        setDisplayedText((prev) => prev + text[currentIndex]);
        setCurrentIndex(currentIndex + 1);
      }, speed);
    } else if (onComplete) {
      onComplete();
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [currentIndex, text, speed, startAnimation, onComplete]);

  return (
    <div className="flex items-start">
      <div className="flex-shrink-0 mt-1 mr-2">
        {isCompleted ? (
          <CheckCircle2 className="h-4 w-4 text-green-500" />
        ) : (
          <Loader2 className="h-4 w-4 animate-spin text-accio-primary" />
        )}
      </div>
      <div>{displayedText}</div>
    </div>
  );
}

interface TypingSectionProps {
  title: string;
  items: string[];
  delay?: number;
  speed?: number;
  isVisible: boolean;
}

export function TypingSection({
  title,
  items,
  delay = 0,
  speed = 30,
  isVisible,
}: TypingSectionProps) {
  const [completedItems, setCompletedItems] = useState<number[]>([]);
  const [showTitle, setShowTitle] = useState(false);
  const [currentItemIndex, setCurrentItemIndex] = useState(0);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  useEffect(() => {
    if (!isVisible) {
      setCompletedItems([]);
      setShowTitle(false);
      setCurrentItemIndex(0);
      return;
    }

    let titleTimer: NodeJS.Timeout;

    titleTimer = setTimeout(() => {
      setShowTitle(true);
    }, delay);

    return () => {
      if (titleTimer) clearTimeout(titleTimer);
    };
  }, [delay, isVisible]);

  const handleItemComplete = (index: number) => {
    setCompletedItems((prev) => {
      // Only add the index if it's not already in the array
      if (prev.includes(index)) return prev;
      return [...prev, index];
    });

    if (index < items.length - 1) {
      setCurrentItemIndex(index + 1);
    }
  };

  if (!isVisible) return null;

  return (
    <div className="mb-4">
      {showTitle && (
        <h3 className="font-medium text-gray-900 mb-2 flex items-center">
          <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
          {title}
        </h3>
      )}

      <div className="mt-2 space-y-2 pl-2">
        {Array.isArray(items) && items.length > 0 ? (
          items.map((item, index) => {
            const isCompleted = completedItems.includes(index);
            const isCurrentItem = index === currentItemIndex;
            const shouldShow = isCompleted || isCurrentItem;
            const itemDelay = delay + 500 + (index * (speed * 10));

            return shouldShow ? (
              <TypingAnimation
                key={index}
                text={item}
                delay={itemDelay}
                speed={speed}
                onComplete={() => handleItemComplete(index)}
                isCompleted={isCompleted}
              />
            ) : null;
          })
        ) : (
          <div className="text-gray-500 text-sm italic">{tCommon("NO_ITEM_TO_DISPLAY")}</div>
        )}
      </div>
    </div>
  );
}
