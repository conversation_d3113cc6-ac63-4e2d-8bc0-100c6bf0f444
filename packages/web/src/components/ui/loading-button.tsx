import * as React from "react";
import { Button, buttonVariants, type ButtonProps } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

export interface LoadingButtonProps extends ButtonProps {
  isLoading?: boolean;
  loadingText?: string;
  spinnerClassName?: string;
}

const LoadingButton = React.forwardRef<HTMLButtonElement, LoadingButtonProps>(
  ({ 
    children, 
    className, 
    variant, 
    size, 
    isLoading = false, 
    loadingText,
    spinnerClassName,
    disabled,
    ...props 
  }, ref) => {
    return (
      <Button
        className={className}
        variant={variant}
        size={size}
        disabled={disabled || isLoading}
        ref={ref}
        {...props}
      >
        {isLoading && (
          <Loader2 className={cn("h-4 w-4 animate-spin", spinnerClassName)} />
        )}
        {isLoading && loadingText ? loadingText : children}
      </Button>
    );
  }
);

LoadingButton.displayName = "LoadingButton";

export { LoadingButton };
