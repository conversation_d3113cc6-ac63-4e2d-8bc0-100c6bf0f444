import { useEffect, useState } from 'react';
import { translateProductDetail } from '../../lib/productTranslator';
import { useLanguage } from '../../contexts/LanguageContext';

export function ProductDetail({ productData }) {
  const { language } = useLanguage();
  const [translatedProduct, setTranslatedProduct] = useState(productData);
  const [isTranslating, setIsTranslating] = useState(false);

  useEffect(() => {
    async function translateProduct() {
      if (language !== 'zh' && language !== 'zh-CN') {
        setIsTranslating(true);
        try {
          const translated = await translateProductDetail(productData, language);
          setTranslatedProduct(translated);
        } catch (error) {
          console.error('Failed to translate product details:', error);
          // Fallback to original product data
          setTranslatedProduct(productData);
        } finally {
          setIsTranslating(false);
        }
      } else {
        setTranslatedProduct(productData);
      }
    }

    translateProduct();
  }, [productData, language]);

  if (isTranslating) {
    return <div>Translating product details...</div>; // Or a better loading state
  }

  const item = translatedProduct.item;

  return (
    <div className="product-detail">
      <h1>{item.title}</h1>
      <div className="price-section">
        <span className="price">¥{item.price}</span>
        {item.orginal_price && (
          <span className="original-price">¥{item.orginal_price}</span>
        )}
      </div>
      
      <div className="shop-info">
        <span>{item.nick}</span>
      </div>
      
      {/* Specifications */}
      <div className="specifications">
        {Object.entries(item.props_list || {}).map(([key, value]) => (
          <div key={key} className="spec-item">
            <span className="spec-name">{key}:</span>
            <span className="spec-value">{value}</span>
          </div>
        ))}
      </div>
      
      {/* More product details... */}
    </div>
  );
}