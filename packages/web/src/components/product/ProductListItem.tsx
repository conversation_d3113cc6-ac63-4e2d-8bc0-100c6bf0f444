import { useEffect, useState } from 'react';
import { translateProductList } from '../../lib/productTranslator';
import { useLanguage } from '../../contexts/LanguageContext'; // Assuming you have a language context

export function ProductListItem({ products }) {
  const { language } = useLanguage();
  const [translatedProducts, setTranslatedProducts] = useState(products);
  const [isTranslating, setIsTranslating] = useState(false);

  useEffect(() => {
    async function translateProducts() {
      if (language !== 'zh' && language !== 'zh-CN') {
        setIsTranslating(true);
        try {
          const translated = await translateProductList(products, language);
          setTranslatedProducts(translated);
        } catch (error) {
          console.error('Failed to translate products:', error);
          // Fallback to original products
          setTranslatedProducts(products);
        } finally {
          setIsTranslating(false);
        }
      } else {
        setTranslatedProducts(products);
      }
    }

    translateProducts();
  }, [products, language]);

  if (isTranslating) {
    return <div>Translating...</div>; // Or a better loading state
  }

  return (
    <div className="product-list">
      {translatedProducts.map(product => (
        <div key={product.num_iid} className="product-item">
          <img src={product.pic_url} alt={product.title} />
          <h3>{product.title}</h3>
          <p className="price">¥{product.price}</p>
          <p className="shop">{product.nick}</p>
          <p className="sales">{product.num} sold</p>
        </div>
      ))}
    </div>
  );
}