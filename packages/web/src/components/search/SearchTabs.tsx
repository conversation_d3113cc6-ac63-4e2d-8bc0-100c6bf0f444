"use client";

import Link from "next/link";
import { Search, FileText } from "lucide-react";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

interface SearchTabsProps {
  activeTab?: 'product' | 'business';
  onTabChange?: (tab: 'product' | 'business') => void;
}

export function SearchTabs({ activeTab = 'product', onTabChange }: SearchTabsProps) {
  const t = useTranslations(I18NNamespace.COMPONENTS_SEARCH);
  const handleTabClick = (tab: 'product' | 'business') => {
    if (onTabChange) {
      onTabChange(tab);
    }
  };

  return (
    <div className="border-b border-gray-200">
      <nav className="-mb-px flex space-x-8">
        <button
          onClick={() => handleTabClick('product')}
          className={`border-b-2 pb-3 px-1 text-sm font-medium flex items-center ${
            activeTab === 'product'
              ? 'border-accio-primary text-gray-900'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
        >
          <Search className="mr-2 h-4 w-4" />
          {t("PRODUCT_SEARCH")}
        </button>
        {/* Business research tab commented out as requested
        <button
          onClick={() => handleTabClick('business')}
          className={`border-b-2 pb-3 px-1 text-sm font-medium flex items-center ${
            activeTab === 'business'
              ? 'border-accio-primary text-gray-900'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
        >
          <FileText className="mr-2 h-4 w-4" />
          {t("BUSINESS_SEARCH")}
        </button>
        */}
      </nav>
    </div>
  );
}
