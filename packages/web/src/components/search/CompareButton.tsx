"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import { LayoutGrid } from "lucide-react";

export function CompareButton() {
  const tCommon = useTranslations(I18NNamespace.COMMON);
  return (
    <Button
      variant="outline"
      className="rounded-full border border-gray-300 bg-white text-gray-800 flex items-center px-4 py-2 h-auto shadow-sm"
    >
      <LayoutGrid className="w-5 h-5 mr-2" />
      <span className="font-medium">{tCommon("COMPARE")}</span>
    </Button>
  );
}
