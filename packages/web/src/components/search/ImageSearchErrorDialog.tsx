"use client";

import { useState, useEffect } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AlertCircle } from "lucide-react";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

interface ImageSearchErrorDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ImageSearchErrorDialog({ isOpen, onClose }: ImageSearchErrorDialogProps) {
  const t = useTranslations(I18NNamespace.COMPONENTS_SEARCH);
  const tCommon = useTranslations(I18NNamespace.COMMON);
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <AlertDialogTitle>{t("IMAGE_SEARCH_FAILED")}</AlertDialogTitle>
          </div>
          <AlertDialogDescription className="pt-4">
            {t("COULDNT_PROCESS")}
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>{t("IMAGE_NOT_SUPPORTED")}</li>
              <li>{t("IMAGE_SIZE")}</li>
              <li>{t("IMAGE_SERVICE_UNAVAILABLE")}</li>
            </ul>
            <p className="mt-3">{t("TRY_AGAIN")}</p>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction onClick={onClose}>{tCommon("OK")}</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
