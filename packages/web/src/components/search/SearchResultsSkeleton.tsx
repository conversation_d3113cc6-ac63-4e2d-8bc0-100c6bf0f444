"use client";

import { Skeleton } from "@/components/ui/skeleton";

export function SearchResultsSkeleton() {
  // Create an array of 10 items for the skeleton
  const skeletonItems = Array.from({ length: 10 }, (_, i) => i);

  return (
    <div className="w-full">
      {/* Skeleton for search results count */}
      <div className="flex items-center justify-between mb-6">
        <Skeleton className="h-6 w-48" />
        <Skeleton className="h-6 w-24" />
      </div>

      {/* Grid of skeleton product cards */}
      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {skeletonItems.map((item) => (
          <div key={item} className="bg-white rounded-lg shadow-sm p-3 border border-gray-200">
            {/* Skeleton for product image */}
            <Skeleton className="w-full h-40 rounded-md mb-3" />

            {/* Skeleton for product title */}
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-3/4 mb-4" />

            {/* Skeleton for price */}
            <Skeleton className="h-5 w-1/3 mb-3" />

            {/* Skeleton for buttons */}
            <div className="flex justify-between mt-2">
              <Skeleton className="h-8 w-24" />
              <Skeleton className="h-8 w-8 rounded-full" />
            </div>
          </div>
        ))}
      </div>

      {/* Skeleton for pagination */}
      <div className="flex justify-center mt-8">
        <div className="flex space-x-2">
          {Array.from({ length: 5 }, (_, i) => (
            <Skeleton key={i} className="h-8 w-8 rounded-md" />
          ))}
        </div>
      </div>
    </div>
  );
}
