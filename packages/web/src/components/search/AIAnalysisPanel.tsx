"use client";

import { useState, useEffect } from "react";
import { type ProductAnalysis } from "@/lib/openai";
import { Loader2, CheckCircle2 } from "lucide-react";
import { TypingSection, TypingAnimation } from "@/components/ui/typing-animation";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import adminConfig from "../../../adminConfig.mjs";

interface AIAnalysisPanelProps {
  query: string;
  isVisible: boolean;
  model?: string;
  isImageSearch?: boolean;
  imageFile?: File | null;
  analysisResult?: ProductAnalysis | null;
}

export function AnalysisPanel({
  query,
  isVisible,
  model = "GPT-4o mini (free)",
  isImageSearch = false,
  imageFile = null,
  analysisResult
}: AIAnalysisPanelProps) {
  const [analysis, setAnalysis] = useState<ProductAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [animationStarted, setAnimationStarted] = useState(false);
  const [userQueryTyped, setUserQueryTyped] = useState(false);
  const [hasAnalyzed, setHasAnalyzed] = useState(false);
  const [lastAnalyzedQuery, setLastAnalyzedQuery] = useState<string>("");
  const t = useTranslations(I18NNamespace.COMPONENTS_SEARCH);

  // Reset state when query changes
  useEffect(() => {
    // Only reset if the query changes AND it's not an image search
    // For image searches, we want to keep the analysis visible
    if (query !== lastAnalyzedQuery && query && !isImageSearch) {
      setHasAnalyzed(false);
      setAnimationStarted(false);
      setUserQueryTyped(false);
    }
  }, [query, lastAnalyzedQuery, isImageSearch]);

  useEffect(() => {
    if (!query && !isImageSearch || !isVisible) {
      return;
    }

    // Since we've temporarily commented out the "No AI" option, this check will rarely be true
    // but we'll keep it for when those options are restored
    const isNoAIModel = model.includes("No AI") || model.toLowerCase().includes("auto");

    if (isNoAIModel || isImageSearch) {

      console.log(`Setting up ${isNoAIModel ? 'No AI' : 'Image Search'} analysis for query: "${query}"`);

      const simpleAnalysis: ProductAnalysis = {
        productName: isImageSearch ? "Product from image search" : query,
        thinking: isImageSearch
          ? adminConfig.search.analysisPanel.imageSearch.thinking
          : adminConfig.search.analysisPanel.noAI.thinking,
        verification: isImageSearch
          ? adminConfig.search.analysisPanel.imageSearch.verification
          : adminConfig.search.analysisPanel.noAI.verification,
        validation: isImageSearch
          ? adminConfig.search.analysisPanel.imageSearch.validation
          : adminConfig.search.analysisPanel.noAI.validation,
        englishKeywords: query,
        chineseKeywords: query
      };

      setAnalysis(simpleAnalysis);
      setHasAnalyzed(true);
      setLastAnalyzedQuery(query);
      setAnimationStarted(true);

      // Ensure userQueryTyped is set to true after a short delay
      setTimeout(() => {
        setUserQueryTyped(true);
      }, 1000);

      return;
    }

    if (hasAnalyzed && query === lastAnalyzedQuery) {
      console.log(`AI analysis skipped: Already analyzed query "${query}"`);
      return;
    }

    if (analysisResult && query) {
      setAnalysis(analysisResult);
      setHasAnalyzed(true);
      setLastAnalyzedQuery(query);
      return
    }

    const fetchAnalysis = async () => {
      setLoading(true);
      setError(null);

      try {
        console.log(`Fetching AI analysis for query: "${query}" with model: ${model}`);

        const response = await fetch('/api/ai-analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ query, model }),
        });

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        console.log("AI analysis completed successfully: ", data);
        setAnalysis(data);
        setHasAnalyzed(true);
        setLastAnalyzedQuery(query);
      } catch (err: any) {
        console.error("AI analysis failed:", err);
        setError(err.message || t("FAILED_TO_ANALTZE"));
      } finally {
        setLoading(false);
      }
    };

    // fetchAnalysis();
  }, [query, isVisible, model, isImageSearch, hasAnalyzed, lastAnalyzedQuery, analysisResult]);

  // Start animation when analysis data is loaded
  useEffect(() => {
    if (analysis && !loading && !animationStarted) {
      setAnimationStarted(true);
    }
  }, [analysis, loading, animationStarted]);

  // Handle user query typing completion
  const handleUserQueryTyped = () => {
    setUserQueryTyped(true);
  };

  if (!isVisible) return null;

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 className="text-xl font-semibold mb-4">{t("AI_ANALYSIS")}</h2>

      {loading && (
        <div className="flex flex-col items-center justify-center py-8">
          <div className="flex items-center mb-4">
            <Loader2 className="h-8 w-8 animate-spin text-accio-primary" />
            <span className="ml-2 text-gray-600 font-medium">{t("ANALYZING")}</span>
          </div>
          <div className="text-sm text-gray-500 max-w-2xl text-center">
            <p className="mb-2">{t("ANALYZING_SEARECH_AUERY")}: "{query}"</p>
            <p>{t("MAY_TAKE_SECONDS")}</p>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 text-red-700 p-4 rounded-md">
          <p className="font-medium mb-2">{t("ERROR_ANALYZING")}</p>
          <p>{error}</p>
          <p className="mt-4 text-sm">
            {t("DUE_TO_ISSUE")}
          </p>
        </div>
      )}

      {analysis && !loading && (
        <>
        {(!analysis.thinking && !analysis.verification && !analysis.validation) && (
          <div className="bg-yellow-50 text-yellow-700 p-4 rounded-md mb-4">
            <p className="font-medium">{t("LIMITED_ANALYSIS")}</p>
            <p className="mt-2">{t("PERFORM_COMPLETE_ANALYSIS")}</p>
          </div>
        )}
        <div className="space-y-4">
          {/* User Query Typing Animation */}
          <div className="mb-4">
            <div className="flex items-center">
              {!userQueryTyped ? (
                <Loader2 className="h-5 w-5 animate-spin text-accio-primary mr-2" />
              ) : (
                <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
              )}
              <h3 className="font-medium text-gray-900">{t("USER_REQUEST")}</h3>
            </div>
            <div className="mt-2 pl-7">
              {animationStarted && (
                <div className="text-sm text-gray-600">
                  <TypingAnimation
                    text={`"${query}"`}
                    speed={20}
                    onComplete={handleUserQueryTyped}
                    isCompleted={userQueryTyped}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Product Information */}
          {userQueryTyped && (
            <div>
              <div className="flex items-center">
                <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                <h3 className="font-medium text-gray-900">{t("PRODUCT_INFORMATION")}</h3>
              </div>
              <div className="mt-2 bg-gray-50 p-3 rounded-md ml-7">
                <p><span className="font-medium">{t("PRODUCT")}:</span> {analysis.productName}</p>
                {analysis.price && <p><span className="font-medium">{t("PRICE_RANGE")}:</span> {analysis.price}</p>}
                {analysis.quantity && <p><span className="font-medium">{t("QUANTITY")}:</span> {analysis.quantity}</p>}
                {analysis.customization && <p><span className="font-medium">{t("CUSTOMIZATION")}:</span> {analysis.customization}</p>}

                {analysis.specifications && analysis.specifications.length > 0 && (
                  <div className="mt-2">
                    <p className="font-medium">{t("SPECIFICATIONS")}:</p>
                    <ul className="list-disc pl-5 mt-1">
                      {analysis.specifications.map((spec, index) => (
                        <li key={index} className="text-sm">{spec}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Display search keywords */}
                <div className="mt-3 pt-2 border-t border-gray-200">
                  {analysis.englishKeywords && (
                    <p className="text-sm">
                      <span className="font-medium">{t("PRIMARY")}:</span> {analysis.englishKeywords}
                    </p>
                  )}
                {/*   {analysis.chineseKeywords && (
                    <p className="text-sm mt-1">
                      <span className="font-medium">:</span> {analysis.chineseKeywords}
                     <span className="ml-2 text-xs text-green-600">({t("USED_FOR_SEARCH")})</span>
                    </p>
                  )}*/}
                </div>
              </div>
            </div>
          )}

          {/* Thinking Section with Typing Animation */}
          {userQueryTyped && (
            <TypingSection
              title={t("ANALYZING_REQUIREMENTS")}
              items={Array.isArray(analysis.thinking) ? analysis.thinking : []}
              delay={1000}
              speed={20}
              isVisible={userQueryTyped && Array.isArray(analysis.thinking) && analysis.thinking.length > 0}
            />
          )}

          {/* Verification Section with Typing Animation */}
          {userQueryTyped && (
            <TypingSection
              title={t("VERIFYING_PRODUCTS")}
              items={Array.isArray(analysis.verification) ? analysis.verification : []}
              delay={2000 + (Array.isArray(analysis.thinking) ? analysis.thinking.length : 0) * 500}
              speed={20}
              isVisible={userQueryTyped && Array.isArray(analysis.verification) && analysis.verification.length > 0}
            />
          )}

          {/* Validation Section with Typing Animation */}
          {userQueryTyped && (
            <TypingSection
              title={t("VALIDATING_PRODUCTS")}
              items={Array.isArray(analysis.validation) ? analysis.validation : []}
              delay={3000 +
                (Array.isArray(analysis.thinking) ? analysis.thinking.length : 0) * 500 +
                (Array.isArray(analysis.verification) ? analysis.verification.length : 0) * 500}
              speed={20}
              isVisible={userQueryTyped && Array.isArray(analysis.validation) && analysis.validation.length > 0}
            />
          )}
        </div>
        </>
      )}
    </div>
  );
}
