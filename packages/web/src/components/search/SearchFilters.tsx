"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import { SlidersHorizontal } from "lucide-react";

export function SearchFilters() {
  const t = useTranslations(I18NNamespace.COMMON);
  return (
    <div className="mt-4">
      <div className="flex mt-4 gap-3">
        <Button
          variant="outline"
          className="rounded-full text-sm py-1 px-6 h-9 bg-white border-gray-300 hover:border-gray-500 flex items-center"
        >
          <SlidersHorizontal className="mr-2 h-4 w-4" /> {t("FILTERS")}
        </Button>
      </div>
    </div>
  );
}
