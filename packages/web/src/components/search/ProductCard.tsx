"use client";

import { Card } from "@/components/ui/card";
import { Heart, Check } from "lucide-react";
import Link from "next/link";
import { useComparison, type Product } from "@/lib/comparison-context";
import { useEffect, useState } from "react";

import { formatPrice } from "@/utils/currency"; // Import currency utilities
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import { useSearchParams } from "next/dist/client/components/navigation";

interface ProductCardProps {
  product: Product;
}

export function ProductCard({ product }: ProductCardProps) {
  const { addToComparison, removeFromComparison, isProductSelected } = useComparison();
  const [isSelected, setIsSelected] = useState(false);
  const searchParams = useSearchParams();
  const tCommon = useTranslations(I18NNamespace.COMMON);

  // Keep local state in sync with context
  useEffect(() => {
    if (product?.id) {
      setIsSelected(isProductSelected(product.id));
    }
  }, [isProductSelected, product?.id]);

  const handleCompareToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isSelected) {
      removeFromComparison(product.id);
    } else {
      addToComparison(product);
    }

    setIsSelected(!isSelected);
  };

  // Safety check - if product is undefined, don't render anything
  if (!product || !product.id) {
    console.warn("[ProductCard] Received invalid product data:", product);
    return null;
  }


  return (
    <Card
      className="overflow-hidden border border-gray-200 hover:shadow-md transition-shadow relative"
    >
      {/* Link to actual product ID and include current search params and source */}
      <Link
        href={`/product/${product.id}?${searchParams.toString()}&source=${product.source || 'unknown'}&referrerSource=${product.source || 'unknown'}`}
        data-source={product.source}
      >
        <div className="relative pt-[100%]">
          {/* Product image */}
          <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
            {product.image ? (
              <img
                src={product.image}
                alt={product.name}
                className="w-full h-full object-contain"
              />
            ) : (
              <div className="w-1/2 h-1/2 bg-gray-300 rounded-md" />
            )}
          </div>

          {/* Favorite button */}
          <button
            className="absolute top-2 right-2 p-1.5 bg-white rounded-full shadow-sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              // Handle favorites
            }}
          >
            <Heart className="h-5 w-5 text-gray-400" />
          </button>

          {/* Compare checkbox */}
          <button
            className={`absolute bottom-2 right-2 p-1 rounded-md shadow-sm flex items-center justify-center ${
              isSelected ? 'bg-black text-white' : 'bg-white text-gray-700'
            }`}
            onClick={handleCompareToggle}
          >
            {isSelected ? (
              <>
                <Check className="w-4 h-4 mr-1" />
                <span className="text-xs font-medium">{tCommon("COMPARE")}</span>
              </>
            ) : (
              <span className="text-xs font-medium px-1">{tCommon("COMPARE")}</span>
            )}
          </button>
        </div>

        <div className="p-3">
          {product.match && (
            <div className="text-xs text-accio-primary mb-1">{product.match}</div>
          )}
          <h3 className={`text-sm font-medium text-gray-800 line-clamp-3 mb-2 ${product.isTranslating ? 'animate-pulse bg-gray-100 rounded px-1' : ''}`}>
            {product.name}
          </h3>
          <p className="text-accio-primary font-semibold mb-1">
            {product.originalPriceCNY
              ? formatPrice(product.originalPriceCNY) // Format CNY price to EUR
              : product.price.startsWith('$')
                ? formatPrice(parseFloat(product.price.replace('$', '')) / 0.14) // Convert from USD to CNY then format
                : product.price // Keep as is if not in expected format
            }
          </p>
          <div className="text-xs text-gray-600">
            {tCommon("MIN_ORDER")}: {product.minOrder}
          </div>
          {product.delivery && (
            <div className="text-xs text-gray-600">
              {tCommon("EST_DELIVERY")} {product.delivery}
            </div>
          )}
          {product.sold && (
            <div className="text-xs text-gray-600">
              {product.sold}
            </div>
          )}
          <div className="flex items-center text-xs text-gray-500 mt-1">
            <span>
              {tCommon("VIA")}
            </span>
            {product.logistics && <span className="ml-1">{product.logistics}</span>}
          </div>
          {/* 显示供应商名称（如果有） */}
          {product.supplier && product.supplier !== "Unknown" && (
            <div className="text-xs text-gray-500 mt-0.5">
              {product.supplier}
            </div>
          )}
        </div>
      </Link>
    </Card>
  );
}
