"use client";

import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import { Loader2 } from "lucide-react";

interface ImageSearchLoadingPanelProps {
  isVisible: boolean;
}

export function ImageSearchLoadingPanel({ isVisible }: ImageSearchLoadingPanelProps) {
  const t = useTranslations(I18NNamespace.COMPONENTS_SEARCH);
  if (!isVisible) return null;

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 className="text-xl font-semibold mb-4">{t("IMAGE_ANALYSIS")}</h2>

      <div className="flex flex-col space-y-4">
        <div className="flex items-start">
          <div className="flex-shrink-0 mt-1 mr-2">
            <Loader2 className="h-4 w-4 animate-spin text-accio-primary" />
          </div>
          <div className="text-sm text-gray-600">
            {t("HAS_UPLOADED")}
          </div>
        </div>

        <div className="flex items-start">
          <div className="flex-shrink-0 mt-1 mr-2">
            <Loader2 className="h-4 w-4 animate-spin text-accio-primary" />
          </div>
          <div className="text-sm text-gray-600">
            {t("TO_FIND_PRODUCT")}
          </div>
        </div>

        <div className="flex items-start">
          <div className="flex-shrink-0 mt-1 mr-2">
            <Loader2 className="h-4 w-4 animate-spin text-accio-primary" />
          </div>
          <div className="text-sm text-gray-600">
            {t("SEARCHING_FOR_SIMILAR")}
          </div>
        </div>
      </div>
    </div>
  );
}
