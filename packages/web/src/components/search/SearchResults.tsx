"use client";

import { ProductCard } from "@/components/search/ProductCard";
import type { Product } from "@/lib/comparison-context";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Crown } from "lucide-react";
import React, { useState, useEffect, useCallback } from "react"; // Import React hooks
import { formatPrice } from "@/utils/currency"; // Import currency utilities
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import { useAuth } from "@/lib/auth-context";
import { handleSubscription } from "@/lib/subscription";
import { useProductListTranslation } from "@/hooks/useProductTranslation";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface SearchResultsProps {
  products?: Product[];
  jsonData?: any;
  onPageChange?: (page: number) => void;
  onProductListTranslated?: (translatedProducts: Product[]) => void;
}

export function translateProducts (translatedRawProducts: any[]) {
  return translatedRawProducts.map((item: any) => {
    // 确保 source 字段始终是小写的 "taobao", "1688" 或 "alibaba"
    let sourceValue = item.source;
    if (sourceValue) {
      // 如果 source 已经被翻译，尝试将其转换回原始值
      const lowerSource = sourceValue.toLowerCase();
      if (lowerSource.includes("taobao") || lowerSource.includes("淘宝")) {
        sourceValue = "taobao";
      } else if (lowerSource.includes("1688")) {
        sourceValue = "1688";
      } else if (lowerSource.includes("alibaba")) {
        sourceValue = "alibaba";
      }
    }

    return {
      id: item.num_iid || String(Math.random()),
      name: item.title || 'Unknown product',
      price: item.price ? `$${item.price}` : '0.00',
      originalPriceCNY: parseFloat(item.price || '0'),
      minOrder: "1",
      supplier: item.shop_name || item.nick || "Unknown",
      source: sourceValue,
      image: item.pic_url || '',
      match: "100%",
      detailUrl: item.detail_url || '#',
      isTranslating: false // 翻译已完成
    };
  });
}

export function SearchResults({ products, jsonData, onPageChange, onProductListTranslated }: SearchResultsProps) {
  const { user, isAuthenticated } = useAuth();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [totalResults, setTotalResults] = useState(0);
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);

  // Transform jsonData to products in a useEffect to avoid render-time state updates
  const [mappedProducts, setMappedProducts] = useState<Product[]>([]);
  const [rawProducts, setRawProducts] = useState<any[]>([]);

  const t = useTranslations(I18NNamespace.COMPONENTS_SEARCH);

  // Process jsonData in an effect to avoid render-time state updates
  useEffect(() => {
    if (!jsonData) {
      return;
    }

    let rawProductsArray: any[] = [];

    if (Array.isArray(jsonData.items?.item)) { // Image search structure (nested)
      rawProductsArray = jsonData.items.item;

      // Update total results count for pagination
      if (jsonData.items?.total_results) {
        setTotalResults(parseInt(jsonData.items.total_results, 10));
      }
    } else if (Array.isArray(jsonData)) { // Text search structure (direct array)
      rawProductsArray = jsonData;
      setTotalResults(jsonData.length);
    } else {
      return;
    }

    if (rawProductsArray.length === 0) {
      return;
    }
    // 设置原始产品数据
    setRawProducts(rawProductsArray);
  }, [jsonData]);


  // 直接调用Hook，但在使用结果时进行条件判断
  const { translatedProducts: translatedRawProductsFromHook, isTranslating: isTranslatingFromHook } =
    useProductListTranslation(rawProducts, jsonData.translated ?? false);

  // 使用条件判断来处理空数组情况
  const translatedRawProducts = rawProducts.length === 0 ? [] : translatedRawProductsFromHook;
  const isTranslating = rawProducts.length === 0 ? false : isTranslatingFromHook;

  // 创建一个临时产品列表，用于在翻译完成前显示
  const untranslatedProducts = rawProducts.length > 0 ? rawProducts.map((item: any) => {
    return {
      id: item.num_iid || String(Math.random()),
      name: t("TRANSLATING_PRODUCT"), // 使用占位符文本
      price: item.price ? `$${item.price}` : '0.00',
      originalPriceCNY: parseFloat(item.price || '0'),
      minOrder: "1",
      supplier: item.shop_name || item.nick || "Unknown",
      source: item.source?.toLowerCase?.() || 'unknown',
      image: item.pic_url || '',
      match: "100%",
      detailUrl: item.detail_url || '#',
      isTranslating: true // 添加标记，表示该产品正在翻译中
    };
  }) : [];

  // 使用useEffect监听翻译状态变化
  useEffect(() => {
    if (jsonData.translated && translatedRawProducts?.length) {
      setMappedProducts(translatedRawProducts);
      return;
    }
    if (isTranslating) return;
    // 如果翻译完成且有翻译后的产品，则映射产品
    if (translatedRawProducts?.length) {
      // 翻译完成后，强制重新映射产品
      const newMappedProducts = translateProducts(translatedRawProducts)
      setMappedProducts(newMappedProducts);
      onProductListTranslated?.(newMappedProducts);
    }
    // 如果翻译完成但没有翻译后的产品，尝试使用原始产品
    else if ((!translatedRawProducts || translatedRawProducts.length === 0) && rawProducts.length > 0) {
      // 使用原始产品数据创建映射
      const fallbackProducts = translateProducts(rawProducts);
      setMappedProducts(fallbackProducts);
    }

  }, [isTranslating, translatedRawProducts, rawProducts]);

  // 使用传入的产品、映射后的产品或未翻译的产品
  // 如果正在翻译且有原始产品，则使用未翻译的产品
  // 否则使用映射后的产品（如果有）
  const allProducts = products || (isTranslating && rawProducts.length > 0 ? untranslatedProducts : mappedProducts);

  // Reset page number when data changes
  useEffect(() => {
    setCurrentPage(1);
  }, [jsonData]);

  // Calculate pagination
  // If we're using API pagination, we'll use the total from the API
  // Otherwise, we'll calculate it from the local data
  const totalPages = onPageChange
    ? Math.ceil(totalResults / itemsPerPage)
    : Math.ceil(allProducts.length / itemsPerPage);

  // If we're using local pagination, slice the data
  // If using API pagination, the data is already sliced by the API
  const displayProducts = onPageChange
    ? allProducts
    : allProducts.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

  // Function to check if user can access pagination
  const canAccessPagination = useCallback(() => {
    // If user is authenticated and has Pro plan, allow pagination
    return isAuthenticated && user?.plan === 'Pro';
  }, [isAuthenticated, user]);

  // Handle upgrade dialog
  const openUpgradeDialog = useCallback(() => {
    setShowUpgradeDialog(true);
  }, []);

  const closeUpgradeDialog = useCallback(() => {
    setShowUpgradeDialog(false);
  }, []);

  const handleUpgradeClick = useCallback(() => {
    closeUpgradeDialog();
    handleSubscription();
  }, []);

  // Use useCallback to prevent recreation of these functions on every render
  const handlePrevPage = useCallback(() => {
    // Check if user can access pagination
    if (!canAccessPagination()) {
      openUpgradeDialog();
      return;
    }

    if (currentPage > 1) {
      const newPage = currentPage - 1;
      setCurrentPage(newPage);

      // If we have an onPageChange handler, call it
      if (onPageChange) {
        onPageChange(newPage);
      }
    }
  }, [currentPage, onPageChange, canAccessPagination, openUpgradeDialog]);

  const handleNextPage = useCallback(() => {
    // Check if user can access pagination
    if (!canAccessPagination()) {
      openUpgradeDialog();
      return;
    }

    if (currentPage < totalPages) {
      const newPage = currentPage + 1;
      setCurrentPage(newPage);

      // If we have an onPageChange handler, call it
      if (onPageChange) {
        onPageChange(newPage);
      }
    }
  }, [currentPage, totalPages, onPageChange, canAccessPagination, openUpgradeDialog]);

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center text-accio-primary">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
            <path d="M12 22a10 10 0 1 0 0-20 10 10 0 0 0 0 20z" />
            <path d="m5 13 4 4" />
            <path d="M12 7v4l2 2" />
          </svg>
          <span className="font-medium">{t("ALL_PRODUCTS")}</span>
        </div>

        {totalPages > 1 && (
          <div className="pagination flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePrevPage}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm text-gray-600">
              {currentPage}/{totalPages}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {displayProducts.length > 0 ? (
          // 显示产品卡片（无论是否翻译完成）
          displayProducts.map((product: Product, index: number) => {
            return <ProductCard key={`${product.id}-${index}`} product={product} />;
          })
        ) : rawProducts.length > 0 ? (
          // 如果有原始产品但没有显示产品，显示加载中
          <div className="col-span-full text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mb-4"></div>
            <p className="text-gray-600">{t("TRANSLATING")}</p>
          </div>
        ) : (
          // 没有产品时显示提示信息
          <div className="col-span-full text-center py-12 text-gray-500">
            {t("NO_PRODUCTS")}
          </div>
        )}
      </div>

      {/* Upgrade Dialog */}
      <Dialog open={showUpgradeDialog} onOpenChange={setShowUpgradeDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Crown className="h-5 w-5 mr-2 text-yellow-500" />
              {t("UPGRADE_TO_PRO")}
            </DialogTitle>
            <DialogDescription>
              {t("UPGRADE_DESCRIPTION")}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-3">
              <div className="flex items-start">
                <div className="bg-yellow-100 p-1 rounded-full mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-sm">{t("UNLIMITED_PAGINATION")}</span>
              </div>
              <div className="flex items-start">
                <div className="bg-yellow-100 p-1 rounded-full mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-sm">{t("DEDICATED_ADVISOR")}</span>
              </div>
              <div className="flex items-start">
                <div className="bg-yellow-100 p-1 rounded-full mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-sm">{t("PRIORITY_ACCESS")}</span>
              </div>
            </div>
            <div className="mt-4 flex items-baseline">
              <span className="text-2xl font-bold">€99</span>
              <span className="text-sm text-gray-500 ml-1">{t("PER_MONTH")}</span>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={closeUpgradeDialog}>
              {t("LATER")}
            </Button>
            <Button onClick={handleUpgradeClick} className="bg-black hover:bg-gray-800">
              {t("UPGRADE_NOW")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
