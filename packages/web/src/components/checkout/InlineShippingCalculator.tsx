"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent } from "@/components/ui/card";
import { ShippingOption, calculateShippingCost, convertYuanToUSD } from "@/lib/shipping-calculator";
import { Calculator } from "lucide-react";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

interface InlineShippingCalculatorProps {
  selectedShippingOption: ShippingOption | undefined;
  onCostCalculated?: (costYuan: number | null, costUSD: number | null) => void;
}



export function InlineShippingCalculator({ selectedShippingOption, onCostCalculated }: InlineShippingCalculatorProps) {
  const [weightKg, setWeightKg] = useState<string>("");
  const [weightG, setWeightG] = useState<string>("");
  const [estimatedCostYuan, setEstimatedCostYuan] = useState<number | null>(null);
  const [estimatedCostUSD, setEstimatedCostUSD] = useState<number | null>(null);
  const [billableWeight, setBillableWeight] = useState<number | null>(null);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  // Update billable weight when weight inputs change
  useEffect(() => {
    // Calculate total weight in kg
    let totalWeightKg = 0;
    let hasValidWeight = false;

    // Add kg weight if provided
    if (weightKg) {
      const kg = parseFloat(weightKg);
      if (!isNaN(kg)) {
        totalWeightKg += kg;
        hasValidWeight = true;
      }
    }

    // Add g weight if provided (converted to kg)
    if (weightG) {
      const g = parseFloat(weightG);
      if (!isNaN(g)) {
        totalWeightKg += g / 1000;
        hasValidWeight = true;
      }
    }

    // Update billable weight if we have valid input
    if (hasValidWeight) {
      setBillableWeight(totalWeightKg);
    } else {
      setBillableWeight(null);
    }
  }, [weightKg, weightG]);

  // Calculate shipping cost whenever billable weight or shipping option changes
  useEffect(() => {
    if (!selectedShippingOption || billableWeight === null) {
      setEstimatedCostYuan(null);
      setEstimatedCostUSD(null);

      // Call the callback with null values
      if (onCostCalculated) {
        onCostCalculated(null, null);
      }
      return;
    }

    // Calculate shipping cost based on the selected shipping option and billable weight
    const costYuan = calculateShippingCost(billableWeight, selectedShippingOption);
    setEstimatedCostYuan(costYuan);

    // Convert to USD
    let costUSD = null;
    if (costYuan !== null) {
      costUSD = convertYuanToUSD(costYuan);
      setEstimatedCostUSD(costUSD);
    } else {
      setEstimatedCostUSD(null);
    }

    // Call the callback with the calculated costs
    if (onCostCalculated) {
      onCostCalculated(costYuan, costUSD);
    }
  }, [billableWeight, selectedShippingOption, onCostCalculated]);

  return (
    <Card className="mt-4">
      <CardContent className="pt-6">
        <div className="grid gap-4">
          <div className="flex items-center gap-2">
            <Calculator className="h-5 w-5 text-blue-600" />
            <h3 className="font-medium text-lg">{tCommon("SHIPPING_COST_CALCULATOR")}</h3>
          </div>

          {/* Weight */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right col-span-1">
              {tCommon("WEIGHT")}
            </Label>
            <div className="col-span-3 flex items-center">
              <div className="flex items-center mr-4">
                <Input
                  id="inline-weight-kg"
                  type="text"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  maxLength={4}
                  placeholder="0"
                  value={weightKg}
                  onChange={(e) => {
                    // Only allow numbers
                    const value = e.target.value.replace(/[^0-9]/g, '');
                    setWeightKg(value);
                  }}
                  className="mr-2 w-16"
                />
                <span className="whitespace-nowrap">{tCommon("KG")}</span>
              </div>
              <div className="flex items-center">
                <Input
                  id="inline-weight-g"
                  type="text"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  maxLength={4}
                  placeholder="0"
                  value={weightG}
                  onChange={(e) => {
                    // Only allow numbers
                    const value = e.target.value.replace(/[^0-9]/g, '');
                    setWeightG(value);
                  }}
                  className="mr-2 w-16"
                />
                <span className="whitespace-nowrap">{tCommon("G")}</span>
              </div>
            </div>
          </div>

          {/* Weight in kg display */}
          {billableWeight !== null && (
            <div className="grid grid-cols-4 gap-4 mt-2">
              <div className="col-span-1"></div>
              <div className="col-span-3 text-sm">
                <p>{tCommon("WEIGHT")}: <span className="font-medium text-blue-600">{billableWeight.toFixed(2)} {tCommon("KG")}</span></p>
              </div>
            </div>
          )}

          {/* Selected Shipping Method */}
          <div className="grid grid-cols-4 gap-4 mt-2">
            <Label className="text-right col-span-1">
              Method
            </Label>
            <div className="col-span-3">
              <p className="font-medium">{selectedShippingOption?.channel_en || 'Please select a shipping method'}</p>
              <p className="text-sm text-gray-500">{selectedShippingOption?.advantages_en}</p>
            </div>
          </div>

          {/* Estimated Cost Display */}
          <div className={`grid grid-cols-4 items-center gap-4 mt-4 p-3 border rounded-md
            ${estimatedCostUSD !== null ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'}`}>
            <Label className={`text-right col-span-1 font-medium ${estimatedCostUSD !== null ? 'text-green-800' : 'text-gray-600'}`}>
              {tCommon("ESTIMATED_SHIPPING_COST")}:
            </Label>
            <div className="col-span-3">
              {estimatedCostUSD !== null && estimatedCostYuan !== null ? (
                <>
                  <p className="text-green-700 font-semibold text-lg">${estimatedCostUSD.toFixed(2)} USD</p>
                </>
              ) : (
                <p className="text-gray-500">
                  {!selectedShippingOption
                    ? tCommon("SELECT_SHIPPING_METHOD")
                    : billableWeight === null
                      ? tCommon("ENTER_WEIGHT_TO_CALCULATE_COST")
                      : tCommon("CALCULATING")}
                </p>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
