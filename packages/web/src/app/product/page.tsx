import { Navbar } from "@/components/shared/Navbar";
import { AppSidebar as Sidebar } from "@/components/shared/Sidebar";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default function ProductPage() {
  // For demo purposes, we'll use hardcoded data
  const product = {
    id: "demo-product",
    name: "Garden Irrigation System",
    description: "Professional garden irrigation system with automatic timer and water-saving technology.",
    price: "$0.24-0.40",
    minOrder: "5 pieces",
    supplier: "Miccobuy.com",
    features: [
      { id: "feature-1", text: "Water-saving technology" },
      { id: "feature-2", text: "Automatic timer" },
      { id: "feature-3", text: "Weather-resistant materials" },
      { id: "feature-4", text: "Easy installation" },
      { id: "feature-5", text: "Adjustable flow rate" }
    ]
  };

  return (
    <div className="min-h-screen bg-[#FAFAFA]">
      <Navbar />
      <Sidebar />

      <div className="pt-20 pl-16 product-content">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Link href="/" className="text-accio-primary flex items-center mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
              <polyline points="15 18 9 12 15 6" />
            </svg>
            Back to search results
          </Link>

          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-6">
              {/* Product Image */}
              <div className="bg-gray-100 rounded-lg aspect-square flex items-center justify-center">
                <div className="w-1/2 h-1/2 bg-gray-200 rounded-md flex items-center justify-center text-gray-400">
                  Product Image
                </div>
              </div>

              {/* Product Info */}
              <div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">{product.name}</h1>
                <p className="text-lg text-accio-primary font-semibold mb-4">{product.price}</p>
                <p className="text-gray-600 mb-6">{product.description}</p>

                <div className="mb-6">
                  <h2 className="text-lg font-semibold mb-2">Product Features</h2>
                  <ul className="list-disc pl-6 space-y-1">
                    {product.features.map((feature) => (
                      <li key={feature.id} className="text-gray-600">{feature.text}</li>
                    ))}
                  </ul>
                </div>

                <div className="mb-6">
                  <h2 className="text-lg font-semibold mb-2">Order Details</h2>
                  <p className="text-gray-600">Min. order: {product.minOrder}</p>
                  <p className="text-gray-600">Supplier: {product.supplier}</p>
                </div>

                <div className="flex space-x-4">
                  <Button className="accio-btn px-8">Contact Supplier</Button>
                  <Button variant="outline" className="border-accio-primary text-accio-primary px-8">
                    Add to Favorites
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
