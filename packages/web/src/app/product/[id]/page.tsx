// This is a Server Component

// Navbar is included in the page layout
// BackButton is used in the client component
import { ProductDetailPageClient } from "@/components/products/ProductDetailPageClient"; // Import the client wrapper
import { ProductDetailSkeleton } from "@/components/products/ProductDetailSkeleton"; // Import skeleton
import { notFound } from 'next/navigation';
import { Suspense } from 'react';
import { trackProductView } from '@/lib/activity-tracker';
import adminConfig from '../../../../adminConfig.mjs';

// Helper function to format attributes
const formatAttributes = (props: { name: string; value: string }[]): Record<string, string> => {
  const attributes: Record<string, string> = {};
  props.forEach(prop => {
    // Skip brand and color as they are handled elsewhere

    if (prop.name !== "品牌" && prop.name !== "颜色分类") {
      attributes[prop.name] = prop.value;
    }
  });
  return attributes;
};

const LocalBaseUrl = 'http://0.0.0.0:3000';

// 开发环境中记录API调用的辅助函数
const logApiCall = (stage: string, data: any) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[DEV-1688-API] ${stage}:`, data);
  }
};

async function DataLoader ({ source, apiUrl, productId }: {
  source: string | string[]
  apiUrl: string
  productId: string
}) {
  let response;
  try {
    // Get the cache configuration based on the source
    let cacheConfig;
    if (source === '1688') {
      cacheConfig = adminConfig.api1688.cache.itemGet;
    } else if (source === 'alibaba') {
      cacheConfig = adminConfig.alibabaApi.cache.itemGet;
    } else {
      cacheConfig = adminConfig.taobaoApi.cache.itemGet;
    }

    // 详细记录API调用信息
    if (source === '1688') {
      logApiCall('1688 API Request', {
        url: apiUrl,
        productId: productId,
        timestamp: new Date().toISOString(),
        cacheConfig: cacheConfig,
        apiConfig: {
          baseUrl: adminConfig.api1688.baseUrl,
          key: adminConfig.api1688.key,
          endpoints: adminConfig.api1688.endpoints,
          enabled: adminConfig.api1688.enabled
        }
      });
    } else if (source === 'alibaba') {
      logApiCall('Alibaba API Request', {
        url: apiUrl,
        productId: productId,
        timestamp: new Date().toISOString(),
        cacheConfig: cacheConfig,
        apiConfig: {
          baseUrl: adminConfig.alibabaApi.baseUrl,
          key: adminConfig.alibabaApi.key,
          endpoints: adminConfig.alibabaApi.endpoints,
          enabled: adminConfig.alibabaApi.enabled
        }
      });
    }

    console.log("[ProductDetailPage]: using request url: " + apiUrl + " cache: " + cacheConfig);

    // Try with the constructed URL
    response = await fetch(apiUrl, {
      next: { revalidate: cacheConfig }, // Revalidate based on cache config
    });

  } catch (error) {
    console.log("[ProductDetailPage]: using fallback request, ", error);
    // Try a fallback approach with direct string concatenation
    try {
      const fallbackUrl = `http://localhost:3000/api/product/${productId}?source=${Array.isArray(source) ? source[0] : source}`;

      // Get the cache configuration based on the source
      let cacheConfig;
      if (source === '1688') {
        cacheConfig = adminConfig.api1688.cache.itemGet;
      } else if (source === 'alibaba') {
        cacheConfig = adminConfig.alibabaApi.cache.itemGet;
      } else {
        cacheConfig = adminConfig.taobaoApi.cache.itemGet;
      }

      response = await fetch(fallbackUrl, {
        next: { revalidate: cacheConfig },
      });

    } catch (fallbackError) {
      console.error(`[ProductDetailPage]: fallback request has error(${fallbackError}), return 404`);
      return notFound();
    }
  }

  // 获取响应内容
  let responseText;
  try {
    responseText = await response.text();
  } catch (error) {
    console.error('Failed to read response text:', error);
    return notFound();
  }
  // 保留错误处理，但添加更详细的日志
  if (!response.ok) {
    console.log('===== RESPONSE ERROR DETAILS =====');
    console.log('Response status:', response.status, response.statusText);
    console.log('Request URL:', apiUrl);
    console.log('Source:', source);
    console.log('Product ID:', productId);
    console.log('Response body:', responseText);

    // 尝试解析JSON
    try {
      const errorJson = JSON.parse(responseText);
      console.log('Response JSON:', errorJson);
    } catch (jsonError) {
      console.log('Response is not valid JSON');
    }

    console.log('===== END ERROR DETAILS =====');
    console.error("[ProductDetailPage]: request has error, return 404");

    // 继续处理而不是立即返回404，尝试从响应中获取更多信息
    // 如果你想立即返回404，取消下面这行的注释
    // return notFound();
  }



  // Parse the response text
  let productData;
  try {
    productData = JSON.parse(responseText);

    // 详细记录1688 API响应信息
    if (source === '1688') {
      logApiCall('1688 API Response', {
        timestamp: new Date().toISOString(),
        status: response.status,
        hasItem: !!productData.item,
        hasError: !!productData.error,
        success: productData.success,
        code: productData.code,
        message: productData.message,
        topLevelKeys: Object.keys(productData),
        responseSize: responseText.length
      });

      // 记录完整的响应数据（仅在开发环境）
      if (process.env.NODE_ENV === 'development') {
        console.log('[DEV-1688-API] Full Response Data:', productData);
      }
    }
  } catch (error) {
    console.error("[ProductDetailPage]: request json data parsing failed, return 404, error: " + error);
    console.error("[ProductDetailPage]: Raw response that failed to parse:", responseText);
    return notFound();
  }

  // Log the parsed data structure

  // Check if the API returned an error
  if (productData.error) {
    console.error("[ProductDetailPage]: request has error, error: " + productData.error);
    console.log('===== RESPONSE ERROR DETAILS =====');
    console.log('Response status:', response.status, response.statusText);
    console.log('Request URL:', apiUrl);
    console.log('Source:', source);
    console.log('Product ID:', productId);
    console.log('Response body:', responseText);

    // Check if this is a 5000 error after retries
    if (productData.error.includes("Error code 5000") || productData.error_code === "5000") {
      // Instead of returning 404, return the error component
      return (
        <ProductDetailPageClient
          product={null}
          sellerInfo={null}
          attributes={{}}
          error={{
            code: "5000",
            message: productData.error,
            productId,
            source: Array.isArray(source) ? source[0] : source
          }}
        />
      );
    }

    return notFound();
  }

  // Check if we have the expected item structure
  if (!productData.item) {

    // Try to access other properties that might contain the item
    if (productData.data && productData.data.item) {
      productData.item = productData.data.item;
    } else if (productData.result && productData.result.item) {
      productData.item = productData.result.item;
    } else {
      console.error("[ProductDetailPage]: product has no item data, return 404");
      return notFound();
    }
  }

  // Extract product details
  const product = productData.item;

  // Check if the product has the necessary properties
  if (!product.num_iid || !product.title) {
    console.error("[ProductDetailPage]: product data has no 'num_iid' nor 'title', return 404");
    return notFound();
  }

  // Extract seller info safely
  const sellerInfo = product.seller_info || {
    nick: product.nick || 'Unknown Seller',
    user_num_id: product.seller_id || 'unknown-id'
  };

  // Process product data
  const attributes = product.props ? formatAttributes(product.props) : {};

  // Track product view
  try {
    await trackProductView(
      productId,
      Array.isArray(source) ? source[0] : source
    );
  } catch (error) {
    console.error('Failed to track product view:', error);
    // Don't fail the page load if tracking fails
  }

  // Render the layout and pass data to the Client Component
  return (
    <ProductDetailPageClient
      product={product}
      sellerInfo={sellerInfo}
      attributes={attributes}
    />
  );
}

export default function ProductDetailPage({ params, searchParams }:
  {
    params: {  id: string },
    searchParams: { [key: string]: string | string[] | undefined }
  }
) {
  try {
    // Ensure params is awaited before accessing its properties
    const productId = params?.id;

    // Get source from search params, prioritizing referrerSource over source
    let source = searchParams?.referrerSource || searchParams?.source;

    // Log the source parameter for debugging
    console.log("[ProductDetailPage]: Source from searchParams:", source);
    console.log("[ProductDetailPage]: referrerSource:", searchParams?.referrerSource);
    console.log("[ProductDetailPage]: All searchParams:", searchParams);

    // If no source is specified or the specified source is disabled, use the first enabled API
    if (!source ||
      (source === 'taobao' && !adminConfig.taobaoApi.enabled.itemGet) ||
      (source === '1688' && !adminConfig.api1688.enabled.itemGet) ||
      (source === 'alibaba' && !adminConfig.alibabaApi.enabled.itemGet)) {

      // Check if we have a source in the referrer or other search params
      const referrerSource = searchParams?.referrerSource;
      if (referrerSource &&
          ((referrerSource === 'alibaba' && adminConfig.alibabaApi.enabled.itemGet) ||
           (referrerSource === '1688' && adminConfig.api1688.enabled.itemGet) ||
           (referrerSource === 'taobao' && adminConfig.taobaoApi.enabled.itemGet))) {
        source = referrerSource;
        console.log("[ProductDetailPage]: Using source from referrer:", source);
      }
      // If no valid referrer source, check which APIs are enabled and use the first available one
      else {
        // Default to Alibaba if it's enabled
        if (adminConfig.alibabaApi.enabled.itemGet) {
          source = 'alibaba';
        } else if (adminConfig.api1688.enabled.itemGet) {
          source = '1688';
        } else if (adminConfig.taobaoApi.enabled.itemGet) {
          source = 'taobao';
        } else {
          throw new Error('No product API is enabled in configuration');
        }
        console.log("[ProductDetailPage]: Using default source:", source);
      }
    }

    if (!productId) {
      console.error("[ProductDetailPage]: productId is not found, return 404");
      return notFound();
    }

    // Fetch product data from our API
    // For server components, we need to use absolute URLs for fetch
    // We'll use an absolute URL to ensure it works correctly
    const baseUrl = process.env.NODE_ENV === 'development' ? LocalBaseUrl : (process.env.NEXT_PUBLIC_API_URL || LocalBaseUrl);

    // Construct the URL carefully
    let apiUrl;
    try {
      // First try with URL constructor
      const url = new URL(`/api/product/${productId}`, baseUrl);
      // Add source parameter
      url.searchParams.append('source', Array.isArray(source) ? source[0] : source);
      apiUrl = url.toString();
    } catch (error) {
      // Fallback to string concatenation if URL constructor fails
      const sourceParam = Array.isArray(source) ? source[0] : source;
      apiUrl = `${baseUrl}/api/product/${productId}?source=${sourceParam}`;
    }

    return (
      <Suspense fallback={<ProductDetailSkeleton />}>
        <DataLoader source={source} apiUrl={apiUrl} productId={productId} />
      </Suspense>
    );
  } catch (error) {
    console.error("[ProductDetailPage]: the whole process has error, return 404, error: " + error);
    return notFound();
  }
}
