'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { PageLayout } from '@/components/shared/PageLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function DebugOrdersPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedOrders, setSelectedOrders] = useState<Set<string>>(new Set());
  const [combineResult, setCombineResult] = useState<any>(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login?callbackUrl=/debug/orders');
    }
  }, [status, router]);

  // Fetch all orders for debugging
  useEffect(() => {
    if (status === 'authenticated') {
      setLoading(true);
      fetch('/api/orders/debug')
        .then(res => {
          if (!res.ok) throw new Error(`Failed to fetch orders: ${res.status}`);
          return res.json();
        })
        .then(data => {
          setOrders(data.orders);
          setLoading(false);
        })
        .catch(err => {
          console.error('Error fetching orders:', err);
          setError(err.message);
          setLoading(false);
        });
    }
  }, [status]);

  // Toggle order selection
  const toggleOrderSelection = (orderId: string) => {
    setSelectedOrders(prev => {
      const newSelection = new Set(prev);
      if (newSelection.has(orderId)) {
        newSelection.delete(orderId);
      } else {
        newSelection.add(orderId);
      }
      return newSelection;
    });
  };

  // Attempt to combine selected orders
  const combineOrders = async () => {
    if (selectedOrders.size < 2) {
      alert('Please select at least 2 orders to combine');
      return;
    }

    try {
      setCombineResult({ status: 'loading', message: 'Attempting to combine orders...' });

      const response = await fetch('/api/orders/combine', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderIds: Array.from(selectedOrders) }),
      });

      const data = await response.json();

      if (!response.ok) {
        setCombineResult({
          status: 'error',
          message: data.message || data.error || `Failed to combine orders (${response.status})`,
          details: data
        });
      } else {
        setCombineResult({
          status: 'success',
          message: 'Orders combined successfully!',
          details: data
        });

        // Refresh orders
        fetch('/api/orders/debug')
          .then(res => res.json())
          .then(data => {
            setOrders(data.orders);
            setSelectedOrders(new Set());
          });
      }
    } catch (error) {
      console.error('Error combining orders:', error);
      setCombineResult({
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
      });
    }
  };

  if (status === 'loading' || loading) {
    return (
      <PageLayout>
        <div className="container mx-auto px-4 py-8 pt-20">
          <h1 className="text-2xl font-bold mb-4">Debug Orders</h1>
          <p>Loading...</p>
        </div>
      </PageLayout>
    );
  }

  if (error) {
    return (
      <PageLayout>
        <div className="container mx-auto px-4 py-8 pt-20">
          <h1 className="text-2xl font-bold mb-4">Debug Orders</h1>
          <p className="text-red-500">Error: {error}</p>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-8 pt-20">
        <h1 className="text-2xl font-bold mb-4">Debug Orders</h1>

        {/* Current User Info */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Current User</CardTitle>
          </CardHeader>
          <CardContent>
            <p><strong>User ID:</strong> {session?.user?.id}</p>
            <p><strong>Email:</strong> {session?.user?.email}</p>
          </CardContent>
        </Card>

        {/* Combine Orders Section */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Combine Orders</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-2">Selected {selectedOrders.size} orders for combining</p>
            <Button
              onClick={combineOrders}
              disabled={selectedOrders.size < 2}
              className="mb-4"
            >
              Combine Selected Orders
            </Button>

            {combineResult && (
              <div className={`p-4 rounded-md ${
                combineResult.status === 'loading' ? 'bg-blue-50 text-blue-700' :
                combineResult.status === 'success' ? 'bg-green-50 text-green-700' :
                'bg-red-50 text-red-700'
              }`}>
                <p className="font-medium">{combineResult.message}</p>
                {combineResult.details && (
                  <pre className="mt-2 text-xs overflow-auto max-h-40">
                    {JSON.stringify(combineResult.details, null, 2)}
                  </pre>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Orders List */}
        <h2 className="text-xl font-semibold mb-4">All Orders ({orders.length})</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {orders.map(order => (
            <Card
              key={order._id}
              className={`${
                selectedOrders.has(order._id)
                  ? 'border-2 border-blue-500'
                  : ''
              } ${
                order.userId === session?.user?.id
                  ? ''
                  : 'bg-gray-50'
              }`}
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-base">
                    Order: {order._id.substring(0, 8)}...
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => toggleOrderSelection(order._id)}
                  >
                    {selectedOrders.has(order._id) ? 'Deselect' : 'Select'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-sm space-y-1">
                  <p>
                    <span className={`font-medium ${
                      order.userId === session?.user?.id
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}>
                      User: {order.userId === session?.user?.id ? 'Current User' : 'Other User'}
                    </span>
                  </p>
                  <p>User ID: {order.userId}</p>
                  <p>Status: {order.status}</p>
                  <p>Combined: {order.combined ? 'Yes' : 'No'}</p>
                  <p>Created: {new Date(order.createdAt).toLocaleString()}</p>
                  <p>Items: {order.items.length}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </PageLayout>
  );
}
