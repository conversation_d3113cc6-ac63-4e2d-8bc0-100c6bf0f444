"use client"; // Client Component

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { PageLayout } from "@/components/shared/PageLayout";
import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';
import { BlogPost } from '@/types/blog';
import Link from 'next/link';
import { Search } from 'lucide-react';
import { getAllBlogPosts } from '@/utils/blog-utils';

export default function BlogPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const postsPerPage = 6;
  const t = useTranslations(I18NNamespace.PAGES_BLOG);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  useEffect(() => {
    // Fetch blog posts from the file system
    const fetchBlogPosts = async () => {
      try {
        // Get the current language from the URL
        const pathname = window.location.pathname;
        const isLocalizedRoute = /^\/[a-z]{2}\/blog/.test(pathname);
        let lang = 'en'; // Default language

        if (isLocalizedRoute) {
          // Extract locale from the current path
          lang = pathname.split('/')[1];
        }

        // Get all blog posts for the current language
        const posts = await getAllBlogPosts(lang);
        setBlogPosts(posts);
      } catch (error) {
        console.error('Error fetching blog posts:', error);
        setBlogPosts([]);
      }
    };

    fetchBlogPosts();
  }, []);

  // Filter posts based on search term
  const filteredPosts = blogPosts.filter(post =>
    post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
    post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Pagination logic
  const indexOfLastPost = currentPage * postsPerPage;
  const indexOfFirstPost = indexOfLastPost - postsPerPage;
  const currentPosts = filteredPosts.slice(indexOfFirstPost, indexOfLastPost);
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-[#f8f9fa]">
      <PageLayout>
        <div className="pt-20 container mx-auto px-4 pb-8">
          {/* Header - Centered */}
          <div className="max-w-4xl mx-auto text-center mb-12">
            <h1 className="text-3xl md:text-4xl font-bold mb-6">{t("BLOG")}</h1>

            {/* Search Bar - Centered with max width */}
            <div className="mb-8 relative max-w-md mx-auto">
              <Input
                type="text"
                placeholder={t("SEARCH_BLOG")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            </div>
          </div>

          {/* Blog Posts Grid */}
          {filteredPosts.length === 0 ? (
            <div className="max-w-4xl mx-auto">
              <p className="text-center text-gray-500 py-10">
                {t("NO_POSTS_FOUND")}
              </p>
            </div>
          ) : (
            <div className="max-w-6xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {currentPosts.map((post) => (
                  <Link href={`/blog/${post.slug}`} key={post._id} className="group">
                    <Card className="h-full overflow-hidden hover:shadow-md transition-shadow duration-300 border-0 shadow-sm">
                      <div className="relative h-52 overflow-hidden">
                        <img
                          src={post.imageUrl}
                          alt={post.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                      <CardContent className="p-5">
                        <div className="text-sm text-gray-500 mb-3">{formatDate(post.publishedAt)}</div>
                        <h2 className="text-xl font-semibold mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300 text-gray-900">{post.title}</h2>
                        <p className="text-[#374151] line-clamp-3 mb-4 leading-[1.7]">{post.excerpt}</p>
                        <div className="flex flex-wrap gap-2">
                          {post.tags.map((tag, index) => (
                            <span key={index} className="text-xs bg-gray-100 text-[#374151] px-3 py-1 rounded-full">
                              #{tag}
                            </span>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-12 space-x-3 max-w-4xl mx-auto">
              {currentPage > 1 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  className="px-4"
                >
                  ← {t("PREVIOUS")}
                </Button>
              )}

              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(page)}
                  className={`${currentPage === page ? "bg-black text-white" : ""} w-10 h-10`}
                >
                  {page}
                </Button>
              ))}

              {currentPage < totalPages && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  className="px-4"
                >
                  {t("NEXT")} →
                </Button>
              )}
            </div>
          )}
        </div>
      </PageLayout>
    </div>
  );
}
