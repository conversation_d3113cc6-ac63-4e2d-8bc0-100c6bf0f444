/* Blog content styling */
.blog-content {
  color: #374151;
  line-height: 1.8;
}

@media (max-width: 768px) {
  .blog-content {
    font-size: 16px;
  }

  .blog-content h1 {
    font-size: 1.75rem;
  }

  .blog-content h2 {
    font-size: 1.5rem;
  }

  .blog-content h3 {
    font-size: 1.25rem;
  }
}

.blog-content h1 {
  font-size: 2.25rem;
  font-weight: 700;
  margin-top: 2.5rem;
  margin-bottom: 1.5rem;
  color: #111827;
}

.blog-content h2 {
  font-size: 1.875rem;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #111827;
}

.blog-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: #111827;
}

.blog-content p {
  margin-bottom: 1.25em;
  line-height: 1.8;
}

.blog-content a {
  color: #2563eb;
  text-decoration: none;
}

.blog-content a:hover {
  text-decoration: underline;
}

.blog-content ul, .blog-content ol {
  margin-bottom: 1.25em;
  padding-left: 1.5rem;
}

.blog-content ul {
  list-style-type: disc;
}

.blog-content ol {
  list-style-type: decimal;
}

.blog-content li {
  margin-bottom: 0.5rem;
}

.blog-content blockquote {
  border-left: 4px solid #d1d5db;
  padding-left: 1rem;
  font-style: italic;
  margin: 1.25em 0;
}

.blog-content img {
  border-radius: 0.5rem;
  margin: 2rem 0;
  max-width: 100%;
}

.blog-content pre {
  background-color: #1f2937;
  border-radius: 0.375rem;
  padding: 1rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.blog-content code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #374151;
}

.blog-content pre code {
  background-color: transparent;
  padding: 0;
  color: #e5e7eb;
}

.blog-content strong {
  font-weight: 600;
  color: #111827;
}

.blog-content hr {
  margin: 2rem 0;
  border: 0;
  border-top: 1px solid #e5e7eb;
}

.blog-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.25em 0;
}

.blog-content th {
  background-color: #f9fafb;
  font-weight: 600;
  text-align: left;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
}

.blog-content td {
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
}

.blog-content tr:nth-child(even) {
  background-color: #f9fafb;
}
