"use client"; // Client Component

import React, { useState, useEffect } from 'react';
import { PageLayout } from "@/components/shared/PageLayout";
import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';
import { BlogPost } from '@/types/blog';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Calendar, User, Tag } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';
import '../blog.css';
import { getBlogPostBySlug, getAllBlogPosts } from '@/utils/blog-utils';

interface BlogDetailPageProps {
  params: {
    slug: string;
  };
}

export default function BlogDetailPage({ params }: BlogDetailPageProps) {
  const [post, setPost] = useState<BlogPost | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const t = useTranslations(I18NNamespace.PAGES_BLOG);

  useEffect(() => {
    // Fetch the blog post and related posts from the file system
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Get the current language from the URL
        const pathname = window.location.pathname;
        const isLocalizedRoute = /^\/[a-z]{2}\/blog\//.test(pathname);
        let lang = 'en'; // Default language

        if (isLocalizedRoute) {
          // Extract locale from the current path
          lang = pathname.split('/')[1];
        }

        // Get the blog post by slug and language
        const foundPost = await getBlogPostBySlug(params.slug, lang);

        if (foundPost) {
          setPost(foundPost);
          setError(null);

          // Fetch all blog posts to get related posts
          const allPosts = await getAllBlogPosts(lang);

          // Filter out the current post and get up to 3 related posts
          const related = allPosts
            .filter(p => p.slug !== params.slug)
            .slice(0, 3);

          setRelatedPosts(related);
        } else {
          setError(t("POST_NOT_FOUND"));
        }
      } catch (err) {
        setError(t("ERROR_LOADING_POST"));
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [params.slug, t]);

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const handleBack = () => {
    // Check if we're in a localized route
    const pathname = window.location.pathname;
    const isLocalizedRoute = /^\/[a-z]{2}\/blog\//.test(pathname);

    if (isLocalizedRoute) {
      // Extract locale from the current path
      const locale = pathname.split('/')[1];
      router.push(`/${locale}/blog`);
    } else {
      router.push('/blog');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#f8f9fa]">
        <PageLayout>
          <div className="pt-16 md:pt-20 container mx-auto px-2 md:px-4 pb-6 md:pb-8">
            <div className="animate-pulse">
              {/* Back button skeleton */}
              <div className="h-6 md:h-8 bg-gray-200 rounded w-20 md:w-24 mb-6 md:mb-8 max-w-4xl mx-auto px-4 md:px-0"></div>

              {/* Hero image skeleton */}
              <div className="h-[250px] md:h-[400px] bg-gray-200 rounded mb-6 md:mb-10 max-w-5xl mx-auto"></div>

              {/* Title and metadata skeleton - centered */}
              <div className="mb-6 md:mb-10 max-w-3xl mx-auto flex flex-col items-center px-4 md:px-0">
                <div className="h-8 md:h-10 bg-gray-200 rounded w-full md:w-3/4 mb-4 md:mb-6"></div>
                <div className="flex gap-4 md:gap-6 justify-center w-full">
                  <div className="h-4 bg-gray-200 rounded w-24 md:w-32"></div>
                  <div className="h-4 bg-gray-200 rounded w-24 md:w-32"></div>
                </div>
              </div>

              {/* Tags skeleton - centered */}
              <div className="flex justify-center gap-2 mb-6 md:mb-10 max-w-3xl mx-auto px-4 md:px-0">
                <div className="h-5 md:h-6 bg-gray-200 rounded-full w-12 md:w-16"></div>
                <div className="h-5 md:h-6 bg-gray-200 rounded-full w-16 md:w-20"></div>
                <div className="h-5 md:h-6 bg-gray-200 rounded-full w-20 md:w-24"></div>
              </div>

              {/* Content skeleton */}
              <div className="space-y-4 md:space-y-5 max-w-3xl mx-auto bg-white p-4 md:p-8 rounded-lg shadow-sm">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-4/5"></div>
              </div>
            </div>
          </div>
        </PageLayout>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen bg-[#f8f9fa]">
        <PageLayout>
          <div className="pt-16 md:pt-20 container mx-auto px-2 md:px-4 pb-6 md:pb-8">
            <div className="max-w-3xl mx-auto text-center px-4 md:px-0">
              <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 md:mb-6 text-gray-900">{t("ERROR")}</h1>
              <p className="text-[#374151] mb-6 md:mb-10 text-base md:text-lg leading-[1.7]">{error || t("POST_NOT_FOUND")}</p>
              <Button
                onClick={handleBack}
                variant="outline"
                className="flex items-center gap-1 md:gap-2 mx-auto text-sm md:text-base"
              >
                <ArrowLeft size={14} className="md:w-4 md:h-4" />
                {t("BACK_TO_BLOG")}
              </Button>
            </div>
          </div>
        </PageLayout>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#f8f9fa]">
      <PageLayout>
        <div className="pt-16 md:pt-20 container mx-auto px-2 md:px-4 pb-6 md:pb-8">
          {/* Back Button */}
          <div className="mb-6 md:mb-8 max-w-4xl mx-auto px-4 md:px-0">
            <Button
              onClick={handleBack}
              variant="ghost"
              className="flex items-center gap-1 md:gap-2 text-[#374151] hover:text-gray-900 transition-colors text-sm md:text-base"
            >
              <ArrowLeft size={14} className="md:w-4 md:h-4" />
              {t("BACK_TO_BLOG")}
            </Button>
          </div>

          {/* Hero Image */}
          <div className="relative h-[250px] md:h-[400px] lg:h-[500px] rounded-lg overflow-hidden mb-6 md:mb-10 max-w-5xl mx-auto">
            <img
              src={post.imageUrl}
              alt={post.title}
              className="w-full h-full object-cover"
            />
          </div>

          {/* Post Header - Centered */}
          <div className="mb-6 md:mb-10 max-w-3xl mx-auto text-center px-4 md:px-0">
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 md:mb-6 text-gray-900">{post.title}</h1>
            <div className="flex flex-wrap justify-center gap-4 md:gap-6 text-[#374151] text-sm md:text-base">
              <div className="flex items-center gap-1">
                <Calendar size={16} />
                <span>{formatDate(post.publishedAt)}</span>
              </div>
              <div className="flex items-center gap-1">
                <User size={16} />
                <span>{post.author}</span>
              </div>
            </div>
          </div>

          {/* Tags - Centered */}
          <div className="flex flex-wrap justify-center gap-2 mb-6 md:mb-10 max-w-3xl mx-auto px-4 md:px-0">
            {post.tags.map((tag, index) => (
              <span
                key={index}
                className="flex items-center gap-1 text-xs md:text-sm bg-gray-100 text-[#374151] px-2 md:px-3 py-1 rounded-full"
              >
                <Tag size={12} className="md:w-4 md:h-4" />
                {tag}
              </span>
            ))}
          </div>

          {/* Post Content - Improved spacing and width */}
          <div className="max-w-3xl mx-auto bg-white p-4 px-4 md:p-8 rounded-lg shadow-sm blog-content">
            <ReactMarkdown
              components={{
                h1: ({node, ...props}) => <h1 className="text-3xl font-bold mt-10 mb-6 text-gray-900" {...props} />,
                h2: ({node, ...props}) => <h2 className="text-2xl font-bold mt-8 mb-4 text-gray-900" {...props} />,
                h3: ({node, ...props}) => <h3 className="text-xl font-bold mt-6 mb-3 text-gray-900" {...props} />,
                p: ({node, ...props}) => <p className="text-[#374151] mb-[1.25em] leading-[1.8]" {...props} />,
                ul: ({node, ...props}) => <ul className="list-disc pl-6 mb-[1.25em]" {...props} />,
                ol: ({node, ...props}) => <ol className="list-decimal pl-6 mb-[1.25em]" {...props} />,
                li: ({node, ...props}) => <li className="text-[#374151] mb-2 leading-[1.8]" {...props} />,
                a: ({node, href, ...props}) => <a href={href} className="text-blue-600 no-underline hover:underline" {...props} />,
                blockquote: ({node, ...props}) => <blockquote className="border-l-4 border-gray-300 pl-4 italic text-[#374151] my-[1.25em]" {...props} />,
                img: ({node, src, alt, ...props}) => <img src={src} alt={alt} className="rounded-lg my-8 max-w-full" {...props} />,
                code: ({node, inline, className, children, ...props}) => {
                  const match = /language-(\w+)/.exec(className || '');
                  return !inline && match ? (
                    <SyntaxHighlighter
                      style={tomorrow}
                      language={match[1]}
                      PreTag="div"
                      className="rounded-md my-6"
                      {...props}
                    >
                      {String(children).replace(/\n$/, '')}
                    </SyntaxHighlighter>
                  ) : (
                    <code className="bg-gray-100 text-[#374151] px-1 py-0.5 rounded text-sm" {...props}>
                      {children}
                    </code>
                  );
                }
              }}
            >
              {post.content}
            </ReactMarkdown>
          </div>

          {/* Related Posts */}
          {relatedPosts.length > 0 && (
            <div className="mt-10 md:mt-16 max-w-5xl mx-auto px-4 md:px-0">
              <h2 className="text-xl md:text-2xl font-bold mb-6 md:mb-8 text-center">{t("RELATED_POSTS")}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
                {relatedPosts.map((relatedPost) => (
                  <Link
                    href={`/blog/${relatedPost.slug}`}
                    key={relatedPost._id || relatedPost.slug}
                    className="group"
                  >
                    <div className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
                      <div className="h-40 md:h-52 overflow-hidden">
                        <img
                          src={relatedPost.imageUrl}
                          alt={relatedPost.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                      <div className="p-4 md:p-5">
                        <h3 className="text-base md:text-lg font-semibold mb-2 md:mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300 text-gray-900">
                          {relatedPost.title}
                        </h3>
                        <p className="text-[#374151] text-xs md:text-sm line-clamp-2 leading-[1.7]">{relatedPost.excerpt}</p>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </PageLayout>
    </div>
  );
}
