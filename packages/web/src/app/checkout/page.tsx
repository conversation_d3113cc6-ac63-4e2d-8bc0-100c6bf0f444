"use client";

import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { useCart } from '@/lib/cart-context';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from "@/components/ui/checkbox"; // Import Checkbox
import { Box, Camera, Video, Ruler, Zap, ArrowLeft, Info } from 'lucide-react'; // Import icons
import { getShippingOptions, getPremiumLineOption, getSeaFreightOptions, ShippingOption } from '@/lib/shipping-calculator'; // Import shipping utils
import adminConfig from '../../../adminConfig.mjs';
import { useRouter } from 'next/navigation'; // Import useRouter for back button
import { useAuth } from '@/lib/auth-context'; // Import auth context
import { ObjectId } from 'mongodb';
import type { Address } from '@/models/address';

interface AddressWithId extends Address {
  id: string;
  _id: ObjectId;
  userId: ObjectId;
  name: string;
  street: string;
  city: string;
  zip: string;
  country: string;
  phone: string;
  email: string;
  isDefault: boolean; // Made non-optional to match base Address interface
  createdAt: Date;
  updatedAt: Date;
}

type PaymentStatus = 'pending' | 'completed' | 'failed';
// Update Dialog imports for details modal
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger, DialogClose } from "@/components/ui/dialog";
import { ShippingCostCalculator } from "@/components/shared/ShippingCostCalculator"; // Import the shared calculator
// Navbar is included in the page layout
import { PageLayout } from "@/components/shared/PageLayout"; // Import PageLayout
import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';

// Helper function for currency conversion
const convertYuanToEUR = (yuanAmount: number): number => {
  return yuanAmount * adminConfig.currency.exchangeRates.CNY_EUR;
};

export default function CheckoutPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const { cartItems, getCartSummary } = useCart();
  const t = useTranslations(I18NNamespace.PAGES_CHECKOUT);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  // Create a currencyConfig object using adminConfig values for compatibility
  const currencyConfig = {
    symbol: adminConfig.currency.display.format.EUR.symbol,
    rates: {
      CNY_EUR: adminConfig.currency.exchangeRates.CNY_EUR
    }
  };

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated && !window.location.pathname.includes('/login')) {
      localStorage.setItem('redirectPath', '/checkout');
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Filter for selected items only for this checkout instance
  const itemsToCheckout = cartItems.filter(item => item.selected);
  const summary = getCartSummary(); // Summary is already based on selected items

  // Placeholder for address, shipping, payment states/handlers
  // Address management state
  const { user } = useAuth();
  const [userAddresses, setUserAddresses] = useState<AddressWithId[]>([]);
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null);
  const [isAddingAddress, setIsAddingAddress] = useState(false);
  const [isLoadingAddresses, setIsLoadingAddresses] = useState(false);
  const [newAddress, setNewAddress] = useState<Partial<Address> & {
    name: string;
    street: string;
    city: string;
    zip: string;
    country: string;
    phone: string;
    email: string;
  }>({
    name: '',
    street: '',
    city: '',
    zip: '',
    country: '',
    phone: '',
    email: ''
  });
  const [isSavingAddress, setIsSavingAddress] = useState(false);
  const [saveAddressError, setSaveAddressError] = useState<string | null>(null);
  const [shippingOptions, setShippingOptions] = useState<ShippingOption[]>([]);
  const [selectedShippingChannel, setSelectedShippingChannel] = useState<string>('Economy Line'); // Default to first shipping option
  // Removed packageDetails state as inputs are removed
  const [calculatedShippingCostYuan, setCalculatedShippingCostYuan] = useState<number | null>(null); // Yuan value
  const [calculatedShippingCostUSD, setCalculatedShippingCostUSD] = useState<number | null>(null); // USD value
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [isCalculatorOpen, setIsCalculatorOpen] = useState(false); // State for calculator modal
  const [selectedServices, setSelectedServices] = useState<Record<string, boolean>>({}); // State for value-added services
  const [serviceDetailsOpen, setServiceDetailsOpen] = useState(false); // State for details dialog
  const [currentServiceDetails, setCurrentServiceDetails] = useState<{ label: string, description: string } | null>(null); // State for dialog content
  const [minOrderError, setMinOrderError] = useState(false); // State for minimum order amount error
  const [addressError, setAddressError] = useState<string | null>(null); // State for address selection error
  const [isProcessingPayment, setIsProcessingPayment] = useState(false); // State for payment processing

  // Load addresses from API
  useEffect(() => {
    if (user?.id) {
      setIsLoadingAddresses(true);
      fetch(`/api/address?userId=${user.id}`)
        .then(res => {
          if (!res.ok) throw new Error(tCommon("FAILED_FETCH_ADDR"));
          return res.json();
        })
        .then((addresses: Address[]) => {
          const formattedAddresses = addresses
            .map((addr) => {
              if (!addr || !addr._id) {
                return null;
              }

              const createdAt = typeof addr.createdAt === 'string' ? new Date(addr.createdAt) : addr.createdAt;
              const updatedAt = typeof addr.updatedAt === 'string' ? new Date(addr.updatedAt) : addr.updatedAt;

              if (!(createdAt instanceof Date) || isNaN(createdAt.getTime()) || !(updatedAt instanceof Date) || isNaN(updatedAt.getTime())) {
                return null;
              }

              return {
                ...addr,
                id: addr._id.toString(),
                _id: addr._id,
                isDefault: addr.isDefault || false,
                createdAt,
                updatedAt
              } as AddressWithId;
            })
            .filter((addr): addr is AddressWithId => addr !== null); // Filter out nulls and assert type

          setUserAddresses(formattedAddresses);

          if (formattedAddresses.length > 0) {
            const defaultAddress = formattedAddresses.find(a => a.isDefault) || formattedAddresses[0];
            setSelectedAddressId(defaultAddress.id);
          }
        })
        .catch(error => {
          console.error('Error fetching addresses:', error);
        })
        .finally(() => {
          setIsLoadingAddresses(false);
        });
    }
  }, [user]);

  // Auto-select the last address when addresses change
  useEffect(() => {
    if (userAddresses.length > 0 && !selectedAddressId) {
      setSelectedAddressId(userAddresses[userAddresses.length - 1].id);
    }
  }, [userAddresses, selectedAddressId]);

  // Fetch shipping options on mount - get Premium Line for Air Freight and all Sea Freight options
  useEffect(() => {
    const premiumLine = getPremiumLineOption();
    const seaFreightOptions = getSeaFreightOptions();

    // Combine Premium Line with Sea Freight options
    const combinedOptions: ShippingOption[] = [];

    if (premiumLine) {
      combinedOptions.push(premiumLine);
      setSelectedShippingChannel(premiumLine.channel_en);
    }

    if (seaFreightOptions.length > 0) {
      combinedOptions.push(...seaFreightOptions);

      // Only set selected channel to sea freight if no premium line was found
      if (!premiumLine && seaFreightOptions.length > 0) {
        setSelectedShippingChannel(seaFreightOptions[0].channel_en);
      }
    }

    // If no options were found, fall back to all options
    if (combinedOptions.length === 0) {
      const allOptions = getShippingOptions();
      setShippingOptions(allOptions);
      if (allOptions.length > 0) {
        setSelectedShippingChannel(allOptions[0].channel_en);
      }
    } else {
      setShippingOptions(combinedOptions);
    }
  }, []);

  // TODO: Update shipping cost calculation - needs weight/dimensions, perhaps from cart items or a new state?
  // For now, calculation based on weight input is removed. Display needs adjustment.
  // Update find logic to use channel_en
  useEffect(() => {
    const selectedOption = shippingOptions.find(opt => opt.channel_en === selectedShippingChannel); // Use channel_en

    // Placeholder: Calculation needs actual weight. Resetting cost for now.
    if (selectedOption) {
      // We need weight to calculate cost. For now, just reset.
      // In a real implementation, get weight from state updated by calculator modal or cart items.
      // const weightNum = getWeightFromSomewhere();
      // if (!isNaN(weightNum) && weightNum > 0) {
      //   const cost = calculateShippingCost(weightNum, selectedOption);
      //   setCalculatedShippingCostYuan(cost);
      // } else {
      //   setCalculatedShippingCostYuan(null);
      // }
      setCalculatedShippingCostYuan(null); // Reset cost as weight is missing
    } else {
      setCalculatedShippingCostYuan(null);
    }
  }, [selectedShippingChannel, shippingOptions]);

  // Check if all required fields are filled
  const isAddressFormValid = () => {
    return (
      newAddress.name.trim() !== '' &&
      newAddress.street.trim() !== '' &&
      newAddress.city.trim() !== '' &&
      newAddress.zip.trim() !== '' &&
      newAddress.country.trim() !== '' &&
      newAddress.phone.trim() !== '' &&
      newAddress.email.trim() !== ''
    );
  };

  const handleSaveAddress = async () => {
    setSaveAddressError(null);
    if (!isAddressFormValid()) {
      setSaveAddressError(t("FILL_REQUIRED_FIELDS"));
      return;
    }

    if (!user?.id) {
      return;
    }
    setIsSavingAddress(true);
    setSaveAddressError(null);

    try {
      const method = newAddress._id ? 'PUT' : 'POST';
      const url = '/api/address';
      let payload: any = { // Define payload structure
        name: newAddress.name,
        street: newAddress.street,
        city: newAddress.city,
        zip: newAddress.zip,
        country: newAddress.country,
        phone: newAddress.phone,
        email: newAddress.email,
        // isDefault is handled below based on method
      };

      if (method === 'PUT' && newAddress._id) {
        payload.id = newAddress._id.toString(); // Add string 'id' for PUT identification in API route
        // DO NOT send userId in the payload for updates, as it should not be changed.
        // The backend updateAddress function uses $set, which only modifies fields present in the payload.
        // By omitting userId here, we prevent it from being overwritten.
      } else { // POST request
        payload.userId = user.id; // Add userId for new address
        payload.isDefault = userAddresses.length === 0; // Set isDefault only for the first new address
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `${t("FAILED_SAVE_ADDR")} (${response.status})`);
      }

        // Update Frontend State
      if (method === 'PUT' && newAddress._id) {
        // Update existing address in state directly
        setUserAddresses(prevAddresses => {
          const index = prevAddresses.findIndex(addr => addr._id.toString() === newAddress._id?.toString());
          if (index === -1) {
             return prevAddresses;
          }
          const updatedAddress: AddressWithId = {
            ...prevAddresses[index], // Keep existing _id, userId, createdAt etc.
            // Update fields from the form
            name: newAddress.name,
            street: newAddress.street,
            city: newAddress.city,
            zip: newAddress.zip,
            country: newAddress.country,
            phone: newAddress.phone,
            email: newAddress.email,
            updatedAt: new Date() // Update timestamp
          };
          const newAddresses = [...prevAddresses];
          newAddresses[index] = updatedAddress;
          return newAddresses;
        });

      } else { // POST request - Refresh the whole list
        const refreshResponse = await fetch(`/api/address?userId=${user.id}`);
        if (!refreshResponse.ok) throw new Error(t("FAIL_REFRESH_ADDR"));

        const updatedAddresses: Address[] = await refreshResponse.json();

        const formattedAddresses: AddressWithId[] = updatedAddresses
          .filter(addr => !!(addr && addr._id))
          .map(addr => ({
            ...addr,
            id: addr._id!.toString(),
            _id: addr._id!,
            isDefault: addr.isDefault || false
          }));

        setUserAddresses(formattedAddresses);
      }

      setIsAddingAddress(false);
      setNewAddress({
        name: '',
        street: '',
        city: '',
        zip: '',
        country: '',
        phone: '',
        email: ''
      });
    } catch (error) {
      console.error('Error in handleSaveAddress:', error);
      setSaveAddressError(error instanceof Error ? error.message : tCommon("UNKNOWN_ERR"));
    } finally {
      setIsSavingAddress(false);
    }
  };

  const handleAddressInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setNewAddress(prev => ({ ...prev, [id]: value }));
  };
  

  // Define Value Added Services with descriptions and icons (Updated labels/descriptions)
  const valueAddedServices = [
    { id: 'cushioning', label: t("CUSHIONING"), priceUSD: 0.69, description: t("CUSHIONING_DESC"), icon: Box }, // Changed label/desc
    { id: 'removePackaging', label: t("REMOVE_PACKAGING"), priceUSD: 0.83, description: t("REMOVE_PACKAGING_DESC"), icon: Box }, // Changed label/desc, kept price/icon for now
    { id: 'photos', label: t("TAKE_PHOTOS"), priceUSD: 0.28, description: t("TAKE_PHOTOS_DESC"), icon: Camera },
    { id: 'video', label: t("TAKE_VIDEO"), priceUSD: 0.83, description: t("TAKE_VIDEO_DESC"), icon: Video },
    { id: 'measureSize', label: t("MEASURE_SIZE"), priceUSD: 0.55, description: t("MEASURE_SIZE_DESC"), icon: Ruler },
    { id: 'priorityPurchase', label: t("PRIOR_PURCHASE"), priceUSD: 0.55, description: t("PRIOR_PURCHASE_DESC"), icon: Zap },
  ];

  // Add type for checked parameter
  const handleServiceChange = (serviceId: string, checked: boolean | 'indeterminate') => {
    setSelectedServices(prev => ({ ...prev, [serviceId]: !!checked })); // Ensure boolean value
  };

  const openServiceDetails = (service: { label: string, description: string }) => {
    setCurrentServiceDetails(service);
    setServiceDetailsOpen(true);
  };

  const handlePlaceOrder = async () => {
    // Reset errors
    setMinOrderError(false);
    setAddressError(null);

    // Calculate total order amount in EUR (excluding shipping costs)
    const totalAmount = (
      (summary.total * currencyConfig.rates.CNY_EUR) +
      (valueAddedServices.filter(s => selectedServices[s.id]).reduce((sum, s) => sum + s.priceUSD * 0.93, 0))
      // Shipping costs are excluded from the total as they are just for simulation
    );

    // Check if total amount is less than minimum required (0.5€)
    if (totalAmount <= 0.5) {
      setMinOrderError(true);
      return;
    }

    const selectedAddress = userAddresses.find(addr => addr.id === selectedAddressId);
    if (!selectedAddress) {
      setAddressError(t("SELECT_SHIPPING_ADDR"));
      return;
    }

    if (!selectedShippingChannel) {
      alert(t("SELECT_SHIPPING_METHOD"));
      return;
    }

    // Set loading state to true before starting payment process
    setIsProcessingPayment(true);

    try {
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items: itemsToCheckout.map(item => ({
            productId: item.productId, // Add productId
            sku: item.sku,             // Add sku
            title: item.title,
            price: item.price * currencyConfig.rates.CNY_EUR, // Convert to EUR
            quantity: item.quantity,
            imageUrl: item.imageUrl
          })),
          shipping: {
            method: selectedShippingChannel,
            cost: calculatedShippingCostYuan || 0,
            addressId: selectedAddress.id
          },
          customerEmail: selectedAddress.email
        })
      });

      if (!response.ok) {
        throw new Error(t("FAILED_CREATE_SESSION"));
      }

      const { id: sessionId } = await response.json();
      const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
      await stripe?.redirectToCheckout({ sessionId });

      // Note: We don't need to set isProcessingPayment to false here
      // because we're redirecting to Stripe

    } catch (error) {
      console.error('Checkout error:', error);
      setIsProcessingPayment(false); // Reset loading state on error
      alert(t("FAILED_INITIATE_PAYMENT"));
    }
  };

  if (itemsToCheckout.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-semibold mb-6">{tCommon("CHECKOUT")}</h1>
        <p>{t("EMPTY_CART")}</p>
        {/* Optionally link back to cart or products */}
      </div>
    );
  }

  return (
    // Ensure PageLayout is part of the main structure
    <div className="min-h-screen bg-[#FAFAFA]">
      <PageLayout>
        <div className="pt-20 container mx-auto px-4 pb-8"> {/* Added pt-20 */}
      <h1 className="text-3xl font-bold mb-8">{tCommon("CHECKOUT")}</h1>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">

        {/* Order Summary for Mobile - Will be shown at the top on mobile */}
        <div className="lg:hidden mb-6">
          <Card>
            <CardHeader>
              <CardTitle>{t("OEDER_SUM")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {itemsToCheckout.map((item) => (
                <div key={`${item.productId}-${item.sku}`} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <Image
                      src={item.imageUrl.startsWith('//') ? `https:${item.imageUrl}` : item.imageUrl || '/images/placeholder.png'}
                      alt={item.title}
                      width={40}
                      height={40}
                      className="rounded border"
                    />
                    <div>
                      <a
                        href={`/product/${item.productId}`}
                        className="font-medium truncate w-48 block hover:text-blue-600 hover:underline"
                      >
                        {item.title}
                      </a>
                      <p className="text-xs text-gray-500">Qty: {item.quantity}</p>
                    </div>
                  </div>
                  <span className="font-medium">€{(item.price * item.quantity * currencyConfig.rates.CNY_EUR).toFixed(2)}</span>
                </div>
              ))}
              <Separator />
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>{t("SUBTOTAL")}</span>
                  <span>€{(summary.subtotal * currencyConfig.rates.CNY_EUR).toFixed(2)}</span>
                </div>
                {/* Value Added Services Cost */}
                {Object.entries(selectedServices).filter(([, checked]) => checked).length > 0 && (
                  <div className="flex justify-between">
                    <span>{t("VALUE_ADDED_SERV")}</span>
                    <span>
                      €{valueAddedServices
                        .filter(s => selectedServices[s.id])
                        .reduce((sum, s) => sum + s.priceUSD * 0.93, 0)
                        .toFixed(2)}
                    </span>
                  </div>
                )}
                {/* Shipping Cost (Simulation Only) */}
                {calculatedShippingCostUSD !== null && (
                  <div className="flex justify-between text-gray-500">
                    <span className="flex items-center">
                      {t("SHIPPING")} <span className="text-xs ml-1 italic">(Simulation)</span>
                    </span>
                    <span className="text-gray-500 italic">
                      €{(calculatedShippingCostUSD * 0.93).toFixed(2)}
                    </span>
                  </div>
                )}
                {summary.discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>{t("DISCOUNT")}</span>
                    <span>-€{(summary.discount * currencyConfig.rates.CNY_EUR).toFixed(2)}</span>
                  </div>
                )}
              </div>
              <Separator />
              <div className="flex justify-between font-bold text-lg">
                <span>{t("TOTAL")}</span>
                {/* Calculate final total - Excluding shipping costs */}
                <span>
                  €{(
                    (summary.total * currencyConfig.rates.CNY_EUR) +
                    (valueAddedServices.filter(s => selectedServices[s.id]).reduce((sum, s) => sum + s.priceUSD * 0.93, 0))
                    // Shipping costs excluded as they are just for simulation
                  ).toFixed(2)}
                </span>
              </div>
              <p className="text-xs text-gray-400 text-right mt-1">
                {t("EXCLUDING")}
              </p>
              {calculatedShippingCostUSD !== null && (
                <p className="text-xs text-gray-500 italic text-right mt-1">
                  {t("SHIPPING_DISPLAY_ONLY")}
                </p>
              )}
            </CardContent>
            <CardFooter className="flex flex-col gap-2">
              {minOrderError && (
                <p className="text-red-500 text-sm font-medium text-center w-full">
                  At least 0.5€
                </p>
              )}
              <Button
                className="w-full bg-orange-500 hover:bg-orange-600"
                onClick={handlePlaceOrder}
                disabled={isProcessingPayment}
              >
                {isProcessingPayment ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {t("PROCESSING")}
                  </>
                ) : t("PAY_NOW")}
              </Button>
              {addressError && (
                <p className="text-red-500 text-sm font-medium text-center w-full mt-2">
                  {addressError}
                </p>
              )}
            </CardFooter>
          </Card>
        </div>

        {/* Left Column: Shipping & Payment */}
        <div className="lg:col-span-2 space-y-6">
          {/* Shipping Address */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg font-semibold">{t("SHIPPING_ADDR")}</CardTitle>
              {isAuthenticated && (
                <Button
                  size="sm"
                  onClick={() => setIsAddingAddress(true)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {t("ADD_NEW")}
                </Button>
              )}
            </CardHeader>
            <CardContent className="space-y-4">
              {isAuthenticated ? (
                isLoadingAddresses ? (
                  <div className="flex justify-center items-center py-8">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                    <span className="ml-3 text-blue-500">{t("LOADING_ADDR")}</span>
                  </div>
                ) : userAddresses.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {userAddresses.map((address) => (
                    <div key={address.id} className="relative group">
                      <Card
                        className={`cursor-pointer ${selectedAddressId === address.id ? 'border-blue-500 bg-blue-50' : ''}`}
                        onClick={() => setSelectedAddressId(address.id)}
                      >
                        <CardContent className="p-4">
                          <p className="font-bold text-sm text-[0.875rem]">{address.name}</p>
                          <p className="text-sm">{address.street}</p>
                          <p className="text-sm">{address.city}, {address.country}</p>
                          <p className="text-sm text-gray-600 mt-2">
                            {address.phone} • {address.email}
                          </p>
                        </CardContent>
                      </Card>
                        <Button
                        variant="ghost"
                        size="sm"
                        className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-black text-white hover:bg-black hover:text-white"
                        onClick={(e) => {
                          e.stopPropagation();
                          setNewAddress({
                            _id: address._id,
                            name: address.name,
                            street: address.street,
                            city: address.city,
                            zip: address.zip,
                            country: address.country,
                            phone: address.phone,
                            email: address.email
                          });
                          setIsAddingAddress(true);
                        }}
                      >
                        {tCommon("EDIT")}
                      </Button>
                    </div>
                  ))}
                  </div>
                ) : (
                  <p className="text-center py-4 text-gray-500">{t("NO_ADDR_ADD")}</p>
                )
              ) : (
                <div className="text-center py-4">
                  <p className="mb-4">{t("LOGIN_MANAGE_ADDR")}</p>
                  <Button
                    onClick={() => router.push('/login')}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {tCommon("LOGIN")}
                  </Button>
                </div>
              )}

              {isAddingAddress && (
                <div className="mt-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">{tCommon("FULL_NAME")} <span className="text-red-500">*</span></Label>
                      <Input
                        id="name"
                        value={newAddress.name}
                        onChange={handleAddressInputChange}
                        placeholder="John Doe"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="street">{tCommon("ADDRESS")} <span className="text-red-500">*</span></Label>
                      <Input
                        id="street"
                        value={newAddress.street}
                        onChange={handleAddressInputChange}
                        placeholder="123 Main St"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="city">{tCommon("CITY")} <span className="text-red-500">*</span></Label>
                      <Input
                        id="city"
                        value={newAddress.city}
                        onChange={handleAddressInputChange}
                        placeholder="Anytown"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="zip">{tCommon("ZIP")} <span className="text-red-500">*</span></Label>
                      <Input
                        id="zip"
                        value={newAddress.zip}
                        onChange={handleAddressInputChange}
                        placeholder="12345"
                        required
                      />
                    </div>
                    <div className="col-span-2">
                      <Label htmlFor="country">{tCommon("COUNTRY")} <span className="text-red-500">*</span></Label>
                      <Input
                        id="country"
                        value={newAddress.country}
                        onChange={handleAddressInputChange}
                        placeholder="United States"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">{tCommon("TELEPHONE")} <span className="text-red-500">*</span></Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={newAddress.phone}
                        onChange={handleAddressInputChange}
                        placeholder="****** 456 7890"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">{tCommon("EMAIL")} <span className="text-red-500">*</span></Label>
                      <Input
                        id="email"
                        type="email"
                        value={newAddress.email}
                        onChange={handleAddressInputChange}
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>
                  <div className="flex justify-end gap-2 mt-4">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsAddingAddress(false);
                        setSaveAddressError(null);
                      }}
                      disabled={isSavingAddress}
                    >
                      {tCommon("CANCEL")}
                    </Button>
                    <Button
                      className="bg-blue-600 hover:bg-blue-700"
                      onClick={handleSaveAddress}
                      disabled={isSavingAddress || !isAddressFormValid()} // Disable if saving or form invalid
                    >
                      {isSavingAddress ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {tCommon("SAVING")}
                        </>
                      ) : tCommon("SAVE_ADDR")}
                    </Button>
                  </div>
                  {saveAddressError && (
                    <p className="text-red-500 text-sm mt-2">{saveAddressError}</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>


          <Card>
            <CardHeader>
              <CardTitle>{t("VALUE_ADDED_SERV")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Service Checkboxes with Icons and Info Buttons */}
              {/* Wrap the entire list in the Dialog component for the details modal */}
              <Dialog open={serviceDetailsOpen} onOpenChange={setServiceDetailsOpen}>
                <div className="space-y-3">
                  {valueAddedServices.map((service) => {
                    const ServiceIcon = service.icon; // Get the icon component
                    return (
                      <div key={service.id} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                          id={service.id}
                          checked={selectedServices[service.id] || false}
                          onCheckedChange={(checked) => handleServiceChange(service.id, !!checked)}
                        />
                        <ServiceIcon className="h-4 w-4 text-gray-500 flex-shrink-0" /> {/* Service Icon */}
                        <Label htmlFor={service.id} className="font-normal cursor-pointer">
                          {service.label}
                          </Label>
                          {/* Info button to open details dialog */}
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-5 w-5 text-gray-400 hover:text-blue-600"
                              onClick={() => openServiceDetails(service)} // Keep onClick to set details
                            >
                              <Info className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                        </div>
                        <span className="text-sm text-gray-600">€{(service.priceUSD * 0.93).toFixed(2)}</span>
                      </div>
                    );
                  })}
                </div>
                {/* Dialog Content is defined outside the loop, at the end of the page */}
              </Dialog>
            </CardContent>
          </Card>


           {/* Shipping Details (Moved Up) */}
           <Card>
            <CardHeader>
              <CardTitle>{t("SELECT_METHOD_CALCULATOR")}</CardTitle>
              <p className="text-sm font-medium text-orange-500 mt-1">
                {t("SHIPPING_SIMULATION_NOTICE")}
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-sm">
                <p className="text-xs text-gray-500 mb-3">
                  {t("SELECT_TIP")}
                </p>
              </div>

              {/* Shipping Method Selection and Calculator */}
              <ShippingCostCalculator
                initialShippingOption={selectedShippingChannel}
                onCostCalculated={(costYuan, costUSD, selectedOption) => {
                  setCalculatedShippingCostYuan(costYuan);
                  setCalculatedShippingCostUSD(costUSD);
                  if (selectedOption) {
                    setSelectedShippingChannel(selectedOption.channel_en);
                  }
                }}
                showMethodSelection={true}
                useCurrency="EUR"
              />
            </CardContent>
          </Card>


          {/* Estimated Taxes & Duties Section (Placeholder) */}
          <Card>
            <CardHeader>
              <CardTitle>{t("TAX_DUTIES")}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                {t("TAX_DUTIES_1")}
              </p>
              <p className="text-sm text-gray-600 mt-2">
                {t("TAX_DUTIES_2")}
              </p>
              {/* Placeholder for specific item tax info if available */}
            </CardContent>
          </Card>
        </div>

        {/* Right Column: Order Summary - Make it sticky (Hidden on mobile) */}
        <div className="hidden lg:block lg:col-span-1 lg:sticky lg:top-24 self-start"> {/* Added hidden on mobile */}
          <Card>
            <CardHeader>
              <CardTitle>{t("OEDER_SUM")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {itemsToCheckout.map((item) => (
                <div key={`${item.productId}-${item.sku}`} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <Image
                      src={item.imageUrl.startsWith('//') ? `https:${item.imageUrl}` : item.imageUrl || '/images/placeholder.png'}
                      alt={item.title}
                      width={40}
                      height={40}
                      className="rounded border"
                    />
                    <div>
                      <a
                        href={`/product/${item.productId}`}
                        className="font-medium truncate w-48 block hover:text-blue-600 hover:underline"
                      >
                        {item.title}
                      </a>
                      <p className="text-xs text-gray-500">Qty: {item.quantity}</p>
                    </div>
                  </div>
                  <span className="font-medium">€{(item.price * item.quantity * currencyConfig.rates.CNY_EUR).toFixed(2)}</span>
                </div>
              ))}
              <Separator />
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>{t("SUBTOTAL")}</span>
                  <span>€{(summary.subtotal * currencyConfig.rates.CNY_EUR).toFixed(2)}</span>
                </div>
                 {/* Value Added Services Cost */}
                 {Object.entries(selectedServices).filter(([, checked]) => checked).length > 0 && (
                    <div className="flex justify-between">
                        <span>{t("VALUE_ADDED_SERV")}</span>
                        <span>
                            €{valueAddedServices
                                .filter(s => selectedServices[s.id])
                                .reduce((sum, s) => sum + s.priceUSD * 0.93, 0)
                                .toFixed(2)}
                        </span>
                    </div>
                 )}
                 {/* Shipping Cost (Simulation Only) */}
                 {calculatedShippingCostUSD !== null && (
                    <div className="flex justify-between text-gray-500">
                      <span className="flex items-center">
                        {t("SHIPPING")} <span className="text-xs ml-1 italic">(Simulation)</span>
                      </span>
                      <span className="text-gray-500 italic">
                        €{(calculatedShippingCostUSD * 0.93).toFixed(2)}
                      </span>
                    </div>
                 )}
                 {summary.discount > 0 && (
                    <div className="flex justify-between text-green-600">
                        <span>{t("DISCOUNT")}</span>
                        <span>-€{(summary.discount * currencyConfig.rates.CNY_EUR).toFixed(2)}</span>
                    </div>
                 )}
              </div>
              <Separator />
              <div className="flex justify-between font-bold text-lg">
                <span>{t("TOTAL")}</span>
                {/* Calculate final total - Excluding shipping costs */}
                <span>
                  €{(
                    (summary.total * currencyConfig.rates.CNY_EUR) +
                    (valueAddedServices.filter(s => selectedServices[s.id]).reduce((sum, s) => sum + s.priceUSD * 0.93, 0))
                    // Shipping costs excluded as they are just for simulation
                  ).toFixed(2)}
                </span>
              </div>
              <p className="text-xs text-gray-400 text-right mt-1">
                {t("EXCLUDING")}
              </p>
              {calculatedShippingCostUSD !== null && (
                <p className="text-xs text-gray-500 italic text-right mt-1">
                  {t("SHIPPING_DISPLAY_ONLY")}
                </p>
              )}
            </CardContent>
            <CardFooter className="flex flex-col gap-2">
              {minOrderError && (
                <p className="text-red-500 text-sm font-medium text-center w-full">
                  At least 0.5€
                </p>
              )}
              <Button
                className="w-full bg-orange-500 hover:bg-orange-600"
                onClick={handlePlaceOrder}
                disabled={isProcessingPayment}
              >
                {isProcessingPayment ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {t("PROCESSING")}
                  </>
                ) : t("PAY_NOW")}
              </Button>
              {addressError && (
                <p className="text-red-500 text-sm font-medium text-center w-full mt-2">
                  {addressError}
                </p>
              )}
            </CardFooter>
          </Card>
        </div>
      </div>

      {/* Service Details Dialog (ensure it's outside the main grid or positioned correctly) */}
      <Dialog open={serviceDetailsOpen} onOpenChange={setServiceDetailsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{currentServiceDetails?.label}</DialogTitle>
            <DialogDescription>
              {currentServiceDetails?.description || "No details available."}
            </DialogDescription>
          </DialogHeader>
          <DialogClose asChild>
            <Button type="button" variant="outline" className="mt-4">
              {tCommon("CLOSE")}
            </Button>
          </DialogClose>
        </DialogContent>
      </Dialog>
        </div> {/* Close container */}
      </PageLayout>
    </div>
  );
}
