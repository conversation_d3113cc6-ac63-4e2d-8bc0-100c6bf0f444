"use client";

import { useState, useEffect } from 'react';
import { useAdminAuth } from '@/hooks/useAdminAuth';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Search, Filter, ChevronLeft, ChevronRight } from 'lucide-react';

interface ApiLog {
  _id: string;
  timestamp: string;
  apiType: 'openai' | '1688' | 'taobao' | 'alibaba';
  endpoint: string;
  method: string;
  status: 'success' | 'error' | 'pending';
  statusCode?: number;
  responseTime?: number;
  userId?: string;
  userEmail?: string;
  errorMessage?: string;
}

interface ApiLogsResponse {
  logs: ApiLog[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export default function ApiLogsPage() {
  const { authenticated, loading: authLoading, requireAuth } = useAdminAuth();
  const [logs, setLogs] = useState<ApiLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);

  // Filters
  const [apiTypeFilter, setApiTypeFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [userFilter, setUserFilter] = useState<string>('');

  // Check admin access
  useEffect(() => {
    if (authLoading) return;

    if (!authenticated) {
      requireAuth();
      return;
    }
  }, [authenticated, authLoading, requireAuth]);

  // Fetch API logs
  const fetchLogs = async (page: number = 1) => {
    try {
      setRefreshing(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20'
      });

      if (apiTypeFilter) params.append('apiType', apiTypeFilter);
      if (statusFilter) params.append('status', statusFilter);
      if (userFilter) params.append('userId', userFilter);

      const response = await fetch(`/api/admin/api-logs?${params}`);
      if (response.ok) {
        const data: ApiLogsResponse = await response.json();
        setLogs(data.logs);
        setCurrentPage(data.page);
        setTotalPages(data.totalPages);
        setTotal(data.total);
      } else {
        console.error('Failed to fetch API logs');
      }
    } catch (error) {
      console.error('Error fetching API logs:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (authenticated) {
      fetchLogs(1);
    }
  }, [authenticated, apiTypeFilter, statusFilter, userFilter]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchLogs(page);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800">Success</Badge>;
      case 'error':
        return <Badge className="bg-red-100 text-red-800">Error</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getApiTypeBadge = (apiType: string) => {
    const colors = {
      openai: 'bg-blue-100 text-blue-800',
      '1688': 'bg-orange-100 text-orange-800',
      taobao: 'bg-red-100 text-red-800',
      alibaba: 'bg-purple-100 text-purple-800'
    };
    return <Badge className={colors[apiType as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>{apiType}</Badge>;
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-[#FAFAFA] flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading API logs...</p>
        </div>
      </div>
    );
  }

  if (!authenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Admin Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => window.location.href = '/admin'}
                variant="outline"
                size="sm"
              >
                ← Back to Dashboard
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">API Logs</h1>
            </div>
            <Button
              onClick={() => fetchLogs(currentPage)}
              disabled={refreshing}
              variant="outline"
              size="sm"
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <p className="text-gray-600">Monitor API calls and responses ({total} total)</p>
        </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Filter className="mr-2 h-5 w-5" />
                Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">API Type</label>
                  <Select value={apiTypeFilter} onValueChange={setApiTypeFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All APIs" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All APIs</SelectItem>
                      <SelectItem value="openai">OpenAI</SelectItem>
                      <SelectItem value="1688">1688</SelectItem>
                      <SelectItem value="taobao">Taobao</SelectItem>
                      <SelectItem value="alibaba">Alibaba</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Status</label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Statuses</SelectItem>
                      <SelectItem value="success">Success</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">User ID</label>
                  <Input
                    placeholder="Filter by user ID"
                    value={userFilter}
                    onChange={(e) => setUserFilter(e.target.value)}
                  />
                </div>
                <div className="flex items-end">
                  <Button
                    onClick={() => {
                      setApiTypeFilter('');
                      setStatusFilter('');
                      setUserFilter('');
                    }}
                    variant="outline"
                    className="w-full"
                  >
                    Clear Filters
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Logs Table */}
          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Timestamp
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        API Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Endpoint
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Response Time
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {logs.map((log) => (
                      <tr key={log._id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(log.timestamp).toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getApiTypeBadge(log.apiType)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                            {log.method}
                          </span>
                          <span className="ml-2">{log.endpoint}</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(log.status)}
                          {log.statusCode && (
                            <span className="ml-2 text-xs text-gray-500">({log.statusCode})</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {log.responseTime ? `${log.responseTime}ms` : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {log.userEmail ? (
                            <div>
                              <div className="font-medium">{log.userEmail}</div>
                              <div className="text-xs text-gray-500">{log.userId}</div>
                            </div>
                          ) : (
                            <span className="text-gray-400">Anonymous</span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-700">
                Showing page {currentPage} of {totalPages} ({total} total logs)
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
      </div>
    </div>
  );
}
