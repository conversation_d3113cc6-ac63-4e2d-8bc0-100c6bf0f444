"use client";

import { useState, useEffect } from 'react';
import { Navbar } from "@/components/shared/Navbar";
import { AppSidebar } from "@/components/shared/Sidebar";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PlusCircle, Trash2, Save, RefreshCw } from 'lucide-react';
import prohibitedProductsData from '@/data/prohibited-products.json';

// Type definitions based on the JSON structure
interface ProhibitedCategory {
  name: string;
  description: string;
  keywords: string[];
}

interface ProhibitedException {
  pattern: string;
  description: string;
  action: string;
}

interface ProhibitedProductsData {
  version: string;
  lastUpdated: string;
  categories: ProhibitedCategory[];
  rules: {
    matchType: string;
    caseSensitive: boolean;
    wholeWordOnly: boolean;
    minimumConfidence: number;
    actionOnMatch: string;
    notificationEmail: string;
  };
  messages: {
    rejected: string;
    warning: string;
    review: string;
  };
  exceptions: ProhibitedException[];
}

export default function ProhibitedProductsPage() {
  const [data, setData] = useState<ProhibitedProductsData>(prohibitedProductsData as ProhibitedProductsData);
  const [activeCategory, setActiveCategory] = useState<number>(0);
  const [newKeyword, setNewKeyword] = useState('');
  const [testProduct, setTestProduct] = useState('');
  const [testResult, setTestResult] = useState<any>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Function to add a new keyword to a category
  const addKeyword = (categoryIndex: number) => {
    if (!newKeyword.trim()) return;

    const updatedData = { ...data };
    updatedData.categories[categoryIndex].keywords.push(newKeyword.trim());
    setData(updatedData);
    setNewKeyword('');
  };

  // Function to remove a keyword from a category
  const removeKeyword = (categoryIndex: number, keywordIndex: number) => {
    const updatedData = { ...data };
    updatedData.categories[categoryIndex].keywords.splice(keywordIndex, 1);
    setData(updatedData);
  };

  // Function to test a product name against the prohibited list
  const testProductName = () => {
    if (!testProduct.trim()) return;

    // Simple implementation of the validation logic
    let result = {
      isProhibited: false,
      matchedKeyword: '',
      matchedCategory: '',
      message: '',
      confidence: 0
    };

    // Check each category and keyword
    for (const category of data.categories) {
      for (const keyword of category.keywords) {
        const normalizedKeyword = data.rules.caseSensitive ? keyword : keyword.toLowerCase();
        const normalizedProduct = data.rules.caseSensitive ? testProduct : testProduct.toLowerCase();

        if (normalizedProduct.includes(normalizedKeyword)) {
          result = {
            isProhibited: true,
            matchedKeyword: keyword,
            matchedCategory: category.name,
            message: data.messages.rejected.replace('{keyword}', keyword),
            confidence: 1.0
          };
          break;
        }
      }
      if (result.isProhibited) break;
    }

    setTestResult(result);
  };

  // Function to save the updated prohibited products data
  const saveData = () => {
    setIsSaving(true);

    // In a real application, this would be an API call to save the data
    // For this example, we'll just simulate a save operation
    setTimeout(() => {
      const updatedData = {
        ...data,
        lastUpdated: new Date().toISOString().split('T')[0]
      };
      setData(updatedData);
      setIsSaving(false);
      alert('Prohibited products list saved successfully!');
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-[#FAFAFA]">
      <Navbar />
      <AppSidebar />
      <div className="pt-20 pl-16 admin-content">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">Prohibited Products Management</h1>
            <Button
              onClick={saveData}
              disabled={isSaving}
              className="flex items-center"
            >
              {isSaving ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left column - Categories and Keywords */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Categories and Keywords</CardTitle>
                </CardHeader>
                <CardContent>
                  {/* <Tabs
                    defaultValue={data.categories[0].name}
                    onValueChange={(value) => {
                      const index = data.categories.findIndex(cat => cat.name === value);
                      setActiveCategory(index >= 0 ? index : 0);
                    }}
                  >
                    <TabsList className="mb-4 flex flex-wrap">
                      {data.categories.map((category) => (
                        <TabsTrigger
                          key={category.name}
                          value={category.name}
                          className="mr-2 mb-2"
                        >
                          {category.name}
                        </TabsTrigger>
                      ))}
                    </TabsList>

                    {data.categories.map((category, categoryIndex) => (
                      <TabsContent key={category.name} value={category.name}>
                        <div className="mb-4">
                          <p className="text-sm text-gray-500 mb-2">{category.description}</p>

                          <div className="flex mb-4">
                            <Input
                              value={newKeyword}
                              onChange={(e) => setNewKeyword(e.target.value)}
                              placeholder="Add new keyword"
                              className="mr-2"
                            />
                            <Button
                              onClick={() => addKeyword(categoryIndex)}
                              disabled={!newKeyword.trim()}
                            >
                              <PlusCircle className="h-4 w-4 mr-2" />
                              Add
                            </Button>
                          </div>

                          <div className="flex flex-wrap gap-2">
                            {category.keywords.map((keyword, keywordIndex) => (
                              <div
                                key={`${category.name}-${keywordIndex}`}
                                className="flex items-center bg-gray-100 rounded-full px-3 py-1"
                              >
                                <span className="text-sm mr-2">{keyword}</span>
                                <button
                                  onClick={() => removeKeyword(categoryIndex, keywordIndex)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      </TabsContent>
                    ))}
                  </Tabs> */}
                </CardContent>
              </Card>
            </div>

            {/* Right column - Test Tool and Settings */}
            <div>
              {/* Test Tool */}
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle>Test Product Name</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Input
                        value={testProduct}
                        onChange={(e) => setTestProduct(e.target.value)}
                        placeholder="Enter product name to test"
                      />
                    </div>
                    <Button
                      onClick={testProductName}
                      disabled={!testProduct.trim()}
                      className="w-full"
                    >
                      Test
                    </Button>

                    {testResult && (
                      <div className={`mt-4 p-4 rounded-md ${testResult.isProhibited ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'}`}>
                        <p className="font-medium mb-2">
                          {testResult.isProhibited ? 'Prohibited' : 'Allowed'}
                        </p>
                        {testResult.isProhibited && (
                          <>
                            <p className="text-sm mb-1">
                              <span className="font-medium">Matched keyword:</span> {testResult.matchedKeyword}
                            </p>
                            <p className="text-sm mb-1">
                              <span className="font-medium">Category:</span> {testResult.matchedCategory}
                            </p>
                            <p className="text-sm">
                              <span className="font-medium">Message:</span> {testResult.message}
                            </p>
                          </>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Settings */}
              <Card>
                <CardHeader>
                  <CardTitle>Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Notification Email</label>
                      <Input
                        value={data.rules.notificationEmail}
                        onChange={(e) => {
                          const updatedData = { ...data };
                          updatedData.rules.notificationEmail = e.target.value;
                          setData(updatedData);
                        }}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Rejection Message</label>
                      <Textarea
                        value={data.messages.rejected}
                        onChange={(e) => {
                          const updatedData = { ...data };
                          updatedData.messages.rejected = e.target.value;
                          setData(updatedData);
                        }}
                        rows={3}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Use {'{keyword}'} as a placeholder for the matched keyword.
                      </p>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="caseSensitive"
                        checked={data.rules.caseSensitive}
                        onChange={(e) => {
                          const updatedData = { ...data };
                          updatedData.rules.caseSensitive = e.target.checked;
                          setData(updatedData);
                        }}
                        className="mr-2"
                      />
                      <label htmlFor="caseSensitive" className="text-sm">
                        Case Sensitive Matching
                      </label>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
