"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Navbar } from "@/components/shared/Navbar";
import { AppSidebar } from "@/components/shared/Sidebar";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Activity, ChevronLeft, ChevronRight } from 'lucide-react';

interface UserActivity {
  _id: string;
  timestamp: string;
  userId?: string;
  userEmail?: string;
  activityType: 'page_visit' | 'search' | 'product_view' | 'checkout' | 'order_created';
  url: string;
  referrer?: string;
  ipAddress?: string;
  metadata?: any;
}

interface UserActivitiesResponse {
  logs: UserActivity[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export default function UserActivitiesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activities, setActivities] = useState<UserActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);

  // Filters
  const [activityTypeFilter, setActivityTypeFilter] = useState<string>('');
  const [userFilter, setUserFilter] = useState<string>('');

  // Check admin access
  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/');
      return;
    }
  }, [session, status, router]);

  // Fetch user activities
  const fetchActivities = async (page: number = 1) => {
    try {
      setRefreshing(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20'
      });

      if (activityTypeFilter) params.append('activityType', activityTypeFilter);
      if (userFilter) params.append('userId', userFilter);

      const response = await fetch(`/api/admin/user-activities?${params}`);
      if (response.ok) {
        const data: UserActivitiesResponse = await response.json();
        setActivities(data.logs);
        setCurrentPage(data.page);
        setTotalPages(data.totalPages);
        setTotal(data.total);
      } else {
        console.error('Failed to fetch user activities');
      }
    } catch (error) {
      console.error('Error fetching user activities:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (authenticated) {
      fetchActivities(1);
    }
  }, [authenticated, activityTypeFilter, userFilter]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchActivities(page);
  };

  const getActivityBadge = (activityType: string) => {
    const colors = {
      page_visit: 'bg-blue-100 text-blue-800',
      search: 'bg-green-100 text-green-800',
      product_view: 'bg-purple-100 text-purple-800',
      checkout: 'bg-orange-100 text-orange-800',
      order_created: 'bg-red-100 text-red-800'
    };
    return <Badge className={colors[activityType as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>
      {activityType.replace('_', ' ')}
    </Badge>;
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-[#FAFAFA] flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading user activities...</p>
        </div>
      </div>
    );
  }

  if (!authenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-[#FAFAFA]">
      <Navbar />
      <AppSidebar />
      <div className="pt-20 pl-16 admin-content">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold">User Activities</h1>
              <p className="text-gray-600 mt-1">Monitor user behavior and page visits ({total} total)</p>
            </div>
            <Button
              onClick={() => fetchActivities(currentPage)}
              disabled={refreshing}
              className="flex items-center"
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="mr-2 h-5 w-5" />
                Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Activity Type</label>
                  <Select value={activityTypeFilter} onValueChange={setActivityTypeFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Activities" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Activities</SelectItem>
                      <SelectItem value="page_visit">Page Visit</SelectItem>
                      <SelectItem value="search">Search</SelectItem>
                      <SelectItem value="product_view">Product View</SelectItem>
                      <SelectItem value="checkout">Checkout</SelectItem>
                      <SelectItem value="order_created">Order Created</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">User ID</label>
                  <Input
                    placeholder="Filter by user ID"
                    value={userFilter}
                    onChange={(e) => setUserFilter(e.target.value)}
                  />
                </div>
                <div className="flex items-end">
                  <Button
                    onClick={() => {
                      setActivityTypeFilter('');
                      setUserFilter('');
                    }}
                    variant="outline"
                    className="w-full"
                  >
                    Clear Filters
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Activities Table */}
          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Timestamp
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Activity Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        URL
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        IP Address
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Details
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {activities.map((activity) => (
                      <tr key={activity._id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(activity.timestamp).toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getActivityBadge(activity.activityType)}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                          {activity.url}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {activity.userEmail ? (
                            <div>
                              <div className="font-medium">{activity.userEmail}</div>
                              <div className="text-xs text-gray-500">{activity.userId}</div>
                            </div>
                          ) : (
                            <span className="text-gray-400">Anonymous</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {activity.ipAddress || '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {activity.metadata && (
                            <div className="max-w-xs">
                              {activity.metadata.query && (
                                <div className="text-xs">Query: {activity.metadata.query.substring(0, 50)}...</div>
                              )}
                              {activity.metadata.productId && (
                                <div className="text-xs">Product: {activity.metadata.productId}</div>
                              )}
                              {activity.metadata.resultCount !== undefined && (
                                <div className="text-xs">Results: {activity.metadata.resultCount}</div>
                              )}
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-700">
                Showing page {currentPage} of {totalPages} ({total} total activities)
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
