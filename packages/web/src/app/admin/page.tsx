"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, LogOut } from 'lucide-react';

interface DashboardStats {
  apiRequests: { total: number; today: number; success: number; errors: number };
  userActivities: { total: number; today: number; uniqueUsers: number };
  stripeEvents: { total: number; today: number; successfulPayments: number; failedPayments: number };
}

export default function AdminDashboard() {
  const router = useRouter();
  const { user, loading: authLoading, authenticated, logout, requireAuth } = useAdminAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Check admin access
  useEffect(() => {
    console.log('Admin page auth state:', { authLoading, authenticated, user });
    if (authLoading) return;

    if (!authenticated) {
      console.log('Not authenticated, requiring auth...');
      requireAuth();
      return;
    }
    console.log('User is authenticated, showing admin page');
  }, [authenticated, authLoading, requireAuth, user]);

  // Fetch dashboard stats
  const fetchStats = async () => {
    try {
      setRefreshing(true);
      const response = await fetch('/api/admin/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      } else {
        console.error('Failed to fetch stats');
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (authenticated) {
      fetchStats();
    }
  }, [authenticated]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    if (!authenticated) return;

    const interval = setInterval(fetchStats, 30000);
    return () => clearInterval(interval);
  }, [authenticated]);

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-[#FAFAFA] flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  if (!authenticated) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Admin Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">Admin Dashboard</h1>
              {user && (
                <span className="ml-4 text-sm text-gray-500">
                  Welcome, {user.username}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <Button
                onClick={fetchStats}
                disabled={refreshing}
                variant="outline"
                size="sm"
              >
                <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                onClick={logout}
                variant="outline"
                size="sm"
              >
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900">Admin Dashboard</h2>
          <p className="text-gray-600 mt-1">Welcome to the admin panel</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>API Logs</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                View API call logs and monitoring data.
              </p>
              <Button
                onClick={() => router.push('/admin/api-logs')}
                className="w-full"
              >
                View API Logs
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>User Activities</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Monitor user activities and behavior.
              </p>
              <Button
                onClick={() => router.push('/admin/user-activities')}
                className="w-full"
              >
                View Activities
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Stripe Events</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Track payment events and transactions.
              </p>
              <Button
                onClick={() => router.push('/admin/stripe-events')}
                className="w-full"
              >
                View Payments
              </Button>
            </CardContent>
          </Card>
        </div>

        {stats && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{stats.apiRequests.total}</div>
                  <p className="text-sm text-gray-600">API Requests</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{stats.userActivities.total}</div>
                  <p className="text-sm text-gray-600">User Activities</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{stats.stripeEvents.total}</div>
                  <p className="text-sm text-gray-600">Stripe Events</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
