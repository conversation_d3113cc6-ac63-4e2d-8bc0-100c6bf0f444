"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Navbar } from "@/components/shared/Navbar";
import { AppSidebar } from "@/components/shared/Sidebar";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, Activity, Database, CreditCard, Users, TrendingUp, AlertCircle } from 'lucide-react';

interface DashboardStats {
  apiRequests: { total: number; today: number; success: number; errors: number };
  userActivities: { total: number; today: number; uniqueUsers: number };
  stripeEvents: { total: number; today: number; successfulPayments: number; failedPayments: number };
}

export default function AdminDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Check admin access
  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/');
      return;
    }
  }, [session, status, router]);

  // Fetch dashboard stats
  const fetchStats = async () => {
    try {
      setRefreshing(true);
      const response = await fetch('/api/admin/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      } else {
        console.error('Failed to fetch stats');
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (session) {
      fetchStats();
    }
  }, [session]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    if (!session) return;

    const interval = setInterval(fetchStats, 30000);
    return () => clearInterval(interval);
  }, [session]);

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-[#FAFAFA] flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-[#FAFAFA]">
      <Navbar />
      <AppSidebar />
      <div className="pt-20 pl-16 admin-content">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold">Admin Dashboard</h1>
              <p className="text-gray-600 mt-1">Real-time statistics and monitoring</p>
            </div>
            <Button
              onClick={fetchStats}
              disabled={refreshing}
              className="flex items-center"
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {/* API Requests Stats */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">API Requests</CardTitle>
                  <Database className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.apiRequests.total}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.apiRequests.today} today
                  </p>
                  <div className="flex justify-between text-xs mt-2">
                    <span className="text-green-600">✓ {stats.apiRequests.success}</span>
                    <span className="text-red-600">✗ {stats.apiRequests.errors}</span>
                  </div>
                </CardContent>
              </Card>

              {/* User Activities Stats */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">User Activities</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.userActivities.total}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.userActivities.today} today
                  </p>
                  <div className="text-xs mt-2">
                    <span className="text-blue-600">👥 {stats.userActivities.uniqueUsers} unique users</span>
                  </div>
                </CardContent>
              </Card>

              {/* Stripe Events Stats */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Payments</CardTitle>
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.stripeEvents.total}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.stripeEvents.today} today
                  </p>
                  <div className="flex justify-between text-xs mt-2">
                    <span className="text-green-600">✓ {stats.stripeEvents.successfulPayments}</span>
                    <span className="text-red-600">✗ {stats.stripeEvents.failedPayments}</span>
                  </div>
                </CardContent>
              </Card>

              {/* Success Rate */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {stats.apiRequests.total > 0
                      ? Math.round((stats.apiRequests.success / stats.apiRequests.total) * 100)
                      : 0}%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    API success rate
                  </p>
                  <div className="text-xs mt-2">
                    <span className="text-blue-600">
                      Payment: {stats.stripeEvents.total > 0
                        ? Math.round((stats.stripeEvents.successfulPayments / stats.stripeEvents.total) * 100)
                        : 0}%
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Database className="mr-2 h-5 w-5" />
                  API Logs
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  View detailed logs of OpenAI, 1688, and Taobao API calls with timestamps and response data.
                </p>
                <Button
                  onClick={() => router.push('/admin/api-logs')}
                  className="w-full"
                >
                  View API Logs
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="mr-2 h-5 w-5" />
                  User Activities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Monitor user activities including page visits, searches, and product views.
                </p>
                <Button
                  onClick={() => router.push('/admin/user-activities')}
                  className="w-full"
                >
                  View Activities
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="mr-2 h-5 w-5" />
                  Stripe Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Track Stripe payment events, successful transactions, and failed payments.
                </p>
                <Button
                  onClick={() => router.push('/admin/stripe-events')}
                  className="w-full"
                >
                  View Payments
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* System Status */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertCircle className="mr-2 h-5 w-5" />
                System Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">✓ Database</div>
                  <p className="text-sm text-gray-600">Connected</p>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">✓ APIs</div>
                  <p className="text-sm text-gray-600">Operational</p>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold text-green-600">✓ Logging</div>
                  <p className="text-sm text-gray-600">Active</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
