"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { signIn } from "next-auth/react";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { LoadingButton } from "@/components/ui/loading-button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

function Page() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams.get("email");
  const firstName = searchParams.get("firstName");
  const lastName = searchParams.get("lastName");

  const [verificationCode, setVerificationCode] = useState<string[]>(Array(6).fill(""));
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [resendDisabled, setResendDisabled] = useState<boolean>(false);
  const [countdown, setCountdown] = useState<number>(0);

  const t = useTranslations(I18NNamespace.PAGES_VERIFY_EMAIL);

  useEffect(() => {
    if (!email) {
      router.push("/signup");
    }
  }, [email, router]);

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else if (countdown === 0 && resendDisabled) {
      setResendDisabled(false);
    }
  }, [countdown, resendDisabled]);

  const handleInputChange = (index: number, value: string) => {
    // Handle pasting of verification code
    if (value.length > 1) {
      // Extract only digits from pasted content
      const digits = value.replace(/\D/g, '');

      // If we have enough digits for the entire code
      if (digits.length >= 6) {
        const newCode = Array(6).fill('');
        // Fill in the verification code array with the first 6 digits
        for (let i = 0; i < 6; i++) {
          newCode[i] = digits[i];
        }
        setVerificationCode(newCode);

        // Focus the last input after pasting
        const lastInput = document.getElementById(`code-${5}`);
        if (lastInput) lastInput.focus();
        return;
      }
      // If we have some digits but less than 6
      else if (digits.length > 0) {
        const newCode = [...verificationCode];
        // Fill in as many inputs as we have digits, starting from the current position
        for (let i = 0; i < digits.length && index + i < 6; i++) {
          newCode[index + i] = digits[i];
        }
        setVerificationCode(newCode);

        // Focus the appropriate input after pasting
        const nextIndex = Math.min(index + digits.length, 5);
        const nextInput = document.getElementById(`code-${nextIndex}`);
        if (nextInput) nextInput.focus();
        return;
      }

      // If no digits were found, just use the first character
      value = value.slice(0, 1);
    }

    // Only allow digits
    if (value && !/^\d+$/.test(value)) return;

    const newCode = [...verificationCode];
    newCode[index] = value;
    setVerificationCode(newCode);

    // Auto-focus next input
    if (value && index < 5) {
      const nextInput = document.getElementById(`code-${index + 1}`);
      if (nextInput) nextInput.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    // Handle backspace to go to previous input
    if (e.key === "Backspace" && !verificationCode[index] && index > 0) {
      const prevInput = document.getElementById(`code-${index - 1}`);
      if (prevInput) prevInput.focus();
    }
  };

  const handleResendCode = async () => {
    if (resendDisabled || !email) return;

    setIsLoading(true);
    setError(null);
    setResendDisabled(true);
    setCountdown(60); // 60 seconds cooldown

    try {
      const response = await fetch("/api/auth/send-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || t("FAILED_TO_RESEND"));
      }

      // Show success message
      setError(null);
    } catch (err: any) {
      console.error("Resend code error:", err);
      setError(err.message || t("FAILED_TO_RESEND"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    const code = verificationCode.join("");
    if (code.length !== 6 || !email) return;

    setIsLoading(true);
    setError(null);

    try {
      // Verify the code
      const verifyResponse = await fetch("/api/auth/verify-code", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, code }),
      });

      if (!verifyResponse.ok) {
        const data = await verifyResponse.json();
        throw new Error(data.error || t("INVALID_VERI_CODE"));
      }

      // If verification successful, sign in the user
      const result = await signIn("credentials", {
        redirect: false,
        email,
        firstName,
        lastName,
        callbackUrl: "/",
      });

      if (result?.error) {
        throw new Error(result.error || t("FAILED_TO_SIGNIN"));
      }

      // Redirect to home page
      router.push(result?.url || "/");
    } catch (err: any) {
      console.error("Verification error:", err);
      setError(err.message || t("FAILED_TO_VARIFY"));
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <Link href="/" className="inline-block mb-4">
            <Image
              src="/images/logoMiccobuy.png"
              alt="Miccobuy Logo"
              width={120}
              height={40}
              className="mx-auto"
            />
          </Link>
          <CardTitle className="text-2xl font-bold">{t("CHECK_EMAIL")}</CardTitle>
          <CardDescription>
            {t("ENTER_VERY_CODE")} {email}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col space-y-2">
            <Label htmlFor="code-0" className="sr-only">
              {t("VERY_CODE")}
            </Label>
            <div className="flex justify-between space-x-2">
              {Array.from({ length: 6 }).map((_, index) => (
                <Input
                  key={index}
                  id={`code-${index}`}
                  type="text"
                  inputMode="numeric"
                  maxLength={1}
                  value={verificationCode[index]}
                  onChange={(e) => handleInputChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  onPaste={(e) => {
                    // Prevent default to handle paste in our custom handler
                    e.preventDefault();
                    // Get pasted content
                    const pastedData = e.clipboardData.getData('text');
                    // Use our custom handler
                    handleInputChange(index, pastedData);
                  }}
                  className="h-12 w-12 text-center text-lg border-gray-300"
                  disabled={isLoading}
                  autoFocus={index === 0}
                />
              ))}
            </div>
          </div>
          {error && <p className="text-sm text-red-500 text-center">{error}</p>}
          <div className="text-center text-sm">
            <p>
              {t("CANT_FIND_EMAIL")}{" "}
              <button
                type="button"
                onClick={handleResendCode}
                disabled={resendDisabled || isLoading}
                className="font-medium text-blue-600 hover:text-blue-500 disabled:text-gray-400"
              >
                {resendDisabled
                  ? `${t("RESEND_CODE")} (${countdown}s)`
                  : t("RESEND_CODE")}
              </button>
            </p>
            <p className="mt-2 text-gray-500">
              {t("CHECK_SPAM")}
            </p>
          </div>
        </CardContent>
        <CardFooter>
          <LoadingButton
            className="w-full"
            onClick={handleVerifyCode}
            isLoading={isLoading}
            loadingText={t("VERIFYING")}
            disabled={verificationCode.join("").length !== 6}
          >
            {t("VERIFY_EMAIL")}
          </LoadingButton>
        </CardFooter>
      </Card>
    </div>
  );
}

export default function VerifyEmailPage () {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <Page />
    </Suspense>
  )
}
