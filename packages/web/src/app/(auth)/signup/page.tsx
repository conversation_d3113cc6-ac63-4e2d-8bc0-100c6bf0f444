"use client";

import { useState } from "react";
import { signIn } from "next-auth/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { LoadingButton } from "@/components/ui/loading-button";
import Image from "next/image"; // Import Image component
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Github, Loader2 } from "lucide-react";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

export default function SignUpPage() {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const t = useTranslations(I18NNamespace.PAGES_SIGNUP);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  // These handlers are often the same for sign up and login with OAuth providers
  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    setError(null);
    try {
      await signIn("google", { callbackUrl: "/" });
    } catch (err) {
      console.error("Google Sign In Error:", err);
      setError(t("G_FAILED"));
      setIsLoading(false);
    }
  };

  const handleGitHubSignIn = async () => {
     alert("GitHub Sign In not implemented yet.");
  };

  // Handle email signup with verification
  const handleEmailSignUp = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      if (!email) {
        throw new Error(t("EMAIL_REQUIRED"));
      }

      if (!firstName || !lastName) {
        throw new Error(t("NAME_REQUIRED"));
      }

      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, firstName, lastName }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Signup failed');
      }

      const data = await response.json();
      console.log("Sign up successful. Redirecting to verification page...");

      // Redirect to verification page with email and name params
      router.push(`/verify-email?email=${encodeURIComponent(email)}&firstName=${encodeURIComponent(firstName)}&lastName=${encodeURIComponent(lastName)}`);

      // Note: We no longer auto sign-in here, as the user needs to verify their email first

    } catch (err: any) {
      console.error("Sign Up Exception:", err);
      setError(err.message || t("UNEXPECTED_ERR"));
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <Link href="/" className="inline-block mb-4"> {/* Wrap logo in Link */}
            <Image
              src="/images/logoMiccobuy.png"
              alt="Miccobuy Logo"
              width={120} // Adjust width as needed
              height={40} // Adjust height based on aspect ratio
              className="mx-auto"
            />
          </Link>
          <CardTitle className="text-2xl font-bold">{tCommon("SIGNUP")}</CardTitle>
          {/* <CardDescription>Create your account</CardDescription> */}
        </CardHeader>
        <CardContent className="space-y-4">
          <LoadingButton
            variant="outline"
            className="w-full flex items-center justify-center gap-2 border-gray-300 hover:bg-gray-100"
            onClick={handleGoogleSignIn}
            isLoading={isLoading}
            loadingText={t("CONNECTING")}
          >
            <Image src="/images/google.svg" alt="Google" width={16} height={16} />
            {t("CONTINUE_GOOGLE")}
          </LoadingButton>
           {/* GitHub Button Removed */}
           {/* <Button
            variant="outline"
            className="w-full flex items-center justify-center gap-2 bg-gray-800 text-white hover:bg-gray-700"
            onClick={handleGitHubSignIn}
            disabled={isLoading}
          >
            <Github className="h-4 w-4" />
            Continue with GitHub
          </Button> */}

          <div className="relative my-4">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-2 text-gray-500">{tCommon("OR")}</span>
            </div>
          </div>

          <form onSubmit={handleEmailSignUp} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">{tCommon("FIRST_NAME")}</Label>
                <Input
                  id="firstName"
                  name="firstName"
                  type="text"
                  autoComplete="given-name"
                  required
                  placeholder={tCommon("FIRST_NAME")}
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  disabled={isLoading}
                  className="border-gray-300"
                />
              </div>
              <div>
                <Label htmlFor="lastName">{tCommon("LAST_NAME")}</Label>
                <Input
                  id="lastName"
                  name="lastName"
                  type="text"
                  autoComplete="family-name"
                  required
                  placeholder={tCommon("LAST_NAME")}
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  disabled={isLoading}
                  className="border-gray-300"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="email">{tCommon("EMAIL")}</Label>
              <Input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                placeholder={tCommon("EMAIL")}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
                className="border-gray-300"
              />
            </div>
            {error && (
              <p className="text-xs text-red-600 text-center">{error}</p>
            )}
            <LoadingButton
              type="submit"
              className="w-full"
              isLoading={isLoading}
              loadingText={t("CREATING_ACCNT")}
            >
              {tCommon("CONTINUE")}
            </LoadingButton>
          </form>
        </CardContent>
        <CardFooter className="text-center text-sm text-gray-600">
          {t("ALREADY_HAVE")}&nbsp;
          <Link href="/login" className="font-medium text-blue-600 hover:text-blue-500">
            {tCommon("LOGIN")}
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
