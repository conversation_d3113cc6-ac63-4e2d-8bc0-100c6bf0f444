"use client";

import { Suspense, useState } from "react";
import { signIn } from "next-auth/react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { LoadingButton } from "@/components/ui/loading-button";
import Image from "next/image"; // Import Image component
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Github, Loader2 } from "lucide-react";

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { useAuth } from '@/lib/auth-context';
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

function Page() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/';

  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [needsVerification, setNeedsVerification] = useState<boolean>(false);

  const t = useTranslations(I18NNamespace.PAGES_LOGIN);
  const tCommon = useTranslations(I18NNamespace.COMMON);
  const tSignup = useTranslations(I18NNamespace.PAGES_SIGNUP);

  useEffect(() => {
    // 处理重定向路径
    const redirectPath = localStorage.getItem('redirectPath');
    if (redirectPath && !callbackUrl.includes('auth') && isAuthenticated) {
      localStorage.removeItem('redirectPath');
      router.push(redirectPath);
    }

    // 检查错误参数
    const errorParam = searchParams.get('error');
    if (errorParam === 'Callback') {
      setError(t("FAILED_TO_SIGNIN_G"));
    }
  }, [callbackUrl, router, isAuthenticated, searchParams, t]);

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // The callbackUrl redirects user after sign in, adjust as needed
      await signIn("google", { callbackUrl: "/" });
    } catch (err) {
      console.error("Google Sign In Error:", err);
      setError(t("FAILED_TO_SIGNIN_G"));
      setIsLoading(false);
    }
    // No need to setIsLoading(false) here if successful, as page redirects
  };

  // Placeholder for GitHub sign in if needed later
  const handleGitHubSignIn = async () => {
     alert("GitHub Sign In not implemented yet.");
     // setIsLoading(true);
     // setError(null);
     // await signIn("github", { callbackUrl: "/" });
     // setIsLoading(false);
  };

  const handleResendVerification = async () => {
    if (!email) return;

    setIsLoading(true);
    setError(null);

    try {
      // First, check if the user exists
      const userCheckResponse = await fetch("/api/auth/check-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (!userCheckResponse.ok) {
        const errorData = await userCheckResponse.json();
        throw new Error(errorData.error || t("FAILED_TO_CHECK_STATUS"));
      }

      const userData = await userCheckResponse.json();

      if (!userData.exists) {
        throw new Error(t("NO_ACCNT_FOUND"));
      }

      // If the user is already verified, no need to send a verification code
      if (userData.isVerified) {
        setError(t("ALREADY_VERIFIED"));
        setIsLoading(false);
        return;
      }

      // Redirect to verification page (the check-verification endpoint already sent a new code)
      router.push(`/verify-email?email=${encodeURIComponent(email)}`);
    } catch (err: any) {
      console.error("Resend verification error:", err);
      setError(err.message || t("FAILED_TO_RESEND"));
      setIsLoading(false);
    }
  };

  const handleEmailSignIn = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsLoading(true);
    setError(null);
    setNeedsVerification(false);

    try {
      if (!email) {
        throw new Error(tSignup("EMAIL_REQUIRED"));
      }

      // First, check if the email is verified
      const verificationResponse = await fetch("/api/auth/check-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (!verificationResponse.ok) {
        const errorData = await verificationResponse.json();
        throw new Error(errorData.error || t("FAILED_TO_CHECK_VERY_STATUS"));
      }

      const verificationData = await verificationResponse.json();

      // If the user exists, redirect to verification page
      if (verificationData.exists) {
        // Always redirect to verification page, regardless of verification status
        router.push(`/verify-email?email=${encodeURIComponent(email)}`);
        setIsLoading(false);
        return;
      }

      // If the user doesn't exist, show an error
      setError(t("NO_ACCNT_FOUND"));
      setIsLoading(false);
    } catch (err: any) {
      console.error("Email Sign In Exception:", err);
      setError(err.message || t("UNEXPECTED"));
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <Link href="/" className="inline-block mb-4"> {/* Wrap logo in Link */}
            <Image
              src="/images/logoMiccobuy.png"
              alt="Miccobuy Logo"
              width={120} // Adjust width as needed
              height={40} // Adjust height based on aspect ratio
              className="mx-auto"
            />
          </Link>
          <CardTitle className="text-2xl font-bold">{tCommon("LOGIN")}</CardTitle>
          {/* <CardDescription>Access your account</CardDescription> */}
        </CardHeader>
        <CardContent className="space-y-4">
          <LoadingButton
            variant="outline"
            className="w-full flex items-center justify-center gap-2 border-gray-300 hover:bg-gray-100"
            onClick={handleGoogleSignIn}
            isLoading={isLoading}
            loadingText={tSignup("CONNECTING")}
          >
            <Image src="/images/google.svg" alt="Google" width={16} height={16} />
            {tSignup("CONTINUE_GOOGLE")}
          </LoadingButton>
           {/* GitHub Button Removed */}
           {/* <Button
            variant="outline"
            className="w-full flex items-center justify-center gap-2 bg-gray-800 text-white hover:bg-gray-700"
            onClick={handleGitHubSignIn} // Add GitHub handler if needed
            disabled={isLoading}
          >
            <Github className="h-4 w-4" />
            Continue with GitHub
          </Button> */}

          <div className="relative my-4">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-2 text-gray-500">{tCommon("OR")}</span>
            </div>
          </div>

          <form onSubmit={handleEmailSignIn} className="space-y-4">
            <div>
              <Label htmlFor="email" className="sr-only">{tCommon("EMAIL")}</Label>
              <Input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                placeholder={tCommon("EMAIL")}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
                className="border-gray-300"
              />
            </div>
            {/* Add password field if using password credentials */}
            {/* <div>
              <Label htmlFor="password">Password</Label>
              <Input id="password" name="password" type="password" required />
            </div> */}
             {error && (
              <p className="text-xs text-red-600 text-center">{error}</p>
            )}
            {needsVerification && (
              <LoadingButton
                type="button"
                variant="outline"
                className="w-full mb-2"
                onClick={handleResendVerification}
                isLoading={isLoading}
                loadingText={t("SENDING")}
              >
                {t("RESEND")}
              </LoadingButton>
            )}
            <LoadingButton
              type="submit"
              className="w-full"
              isLoading={isLoading}
              loadingText={t("CONTINUING")}
            >
              {tCommon("CONTINUE")}
            </LoadingButton>
          </form>
        </CardContent>
        <CardFooter className="text-center text-sm text-gray-600">
          {t("DONT_HAVE_ACCNT")}&nbsp;
          <Link href="/signup" className="font-medium text-blue-600 hover:text-blue-500">
            {tCommon("SIGNUP")}
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <Page />
    </Suspense>
  );
}

