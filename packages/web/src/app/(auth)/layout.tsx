import type { ReactNode } from "react";
import "../globals.css"; // Import global styles

// This layout applies only to routes within the (auth) group, like /login and /signup.
// It intentionally omits the Navbar, Sidebar, and MainContentWrapper padding
// to allow for a full-screen auth page design.

export default function AuthLayout({ children }: { children: ReactNode }) {
  return (
    // We might not need ClientBody here if the root layout handles it,
    // but including it ensures body styles are applied if needed directly.
    // The root layout's AuthSessionProvider already covers these pages.
    <div className="min-h-screen bg-gray-50"> {/* Basic background */}
      {children}
    </div>
  );
}
