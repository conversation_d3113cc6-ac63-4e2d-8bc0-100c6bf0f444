"use client"; // Reverted to Client Component

import React, { useEffect, useState, useMemo, Suspense } from 'react'; // Import hooks
import { useSearchParams } from 'next/navigation'; // Import useSearchParams
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
// Import types/enum from the new shared file
import { type Order as OrderType, OrderStatus, type OrderItem } from '@/types/order-shared';
// Navbar is included in the page layout
import { PageLayout } from "@/components/shared/PageLayout";
import { useCart } from '@/lib/cart-context'; // Import useCart for clearing
import { useAuth } from '@/lib/auth-context'; // Import useAuth to check login status
import { useRouter } from 'next/navigation'; // Import useRouter for redirect
import { OrderDetailsPopup } from '@/components/orders/OrderDetailsPopup'; // Import OrderDetailsPopup
import { loadStripe } from '@stripe/stripe-js'; // Import loadStripe for payment
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"; // Import AlertDialog components
import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';


// Define OrderItem type locally if needed, or rely on OrderType['items'][number]
// interface OrderItem { ... }

const Page = () => {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { removeSelectedItems } = useCart(); // Get cart clearing function

  const [orders, setOrders] = useState<OrderType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [activeStatusFilter, setActiveStatusFilter] = useState<OrderStatus | 'all'>('all'); // 'all' or specific status
  const [updatingOrderId, setUpdatingOrderId] = useState<string | null>(null); // State to track which order is being updated
  const [updateError, setUpdateError] = useState<string | null>(null); // State for update errors
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null); // State for the selected order to view details
  const [isDetailsOpen, setIsDetailsOpen] = useState(false); // State for the details popup
  const [selectedOrders, setSelectedOrders] = useState<Set<string>>(new Set()); // State for selected orders (for batch operations)
  const [showCombineAlert, setShowCombineAlert] = useState(false); // State for combine boxes alert (not enough orders)
  const [showCombineConfirm, setShowCombineConfirm] = useState(false); // State for combine boxes confirmation
  const [processingPaymentOrderId, setProcessingPaymentOrderId] = useState<string | null>(null); // State for payment processing
  const t = useTranslations(I18NNamespace.PAGES_MY_ORDERS);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  // Mapping from OrderStatus enum to display text (adjust as needed)
  const statusDisplayMap: Record<OrderStatus, string> = {
    [OrderStatus.PENDING]: t("PENDING"), // Pending Payment
    [OrderStatus.PROCESSING]: t("PROCESSING"), // Processing / Awaiting Warehouse
    [OrderStatus.SHIPPED]: t("SHIPPED"), // Shipped / Awaiting Delivery
    [OrderStatus.DELIVERED]: t("DELIVERED"), // Delivered / Completed
    [OrderStatus.CANCELLED]: t("CANCELLED"), // Cancelled (will be hidden in UI)
    [OrderStatus.PENDING_TRANSFER]: t("PENDING_TRANSFER"), // Pending Transfer
  };

  // Redirect check
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login?callbackUrl=/my-orders');
    }
  }, [authLoading, isAuthenticated, router]);

  // Effect for cart clearing on successful checkout redirect
  useEffect(() => {
    const sessionId = searchParams.get('session_id');
    if (sessionId) {
      console.log("Checkout success detected (session_id found), clearing selected cart items.");
      removeSelectedItems();
      // Optional: Remove session_id from URL without reload
      // window.history.replaceState(null, '', '/my-orders');
    }
    // Run only once on mount or when session_id appears
  }, [searchParams, removeSelectedItems]);

  // Effect for fetching orders
  useEffect(() => {
    // Only fetch if authenticated
    if (isAuthenticated) {
      setIsLoading(true);
      setFetchError(null);
      fetch('/api/orders') // Fetch from the new GET endpoint
        .then(res => {
          if (!res.ok) {
            throw new Error(`${t("FAILED_TO_FETCH_ORDERS")} (${res.status})`);
          }
          return res.json();
        })
        .then((data: OrderType[]) => {
          // Ensure dates are Date objects if needed, though JSON.parse handles ISO strings
          const formattedOrders = data.map(order => ({
            ...order,
            createdAt: new Date(order.createdAt),
            updatedAt: new Date(order.updatedAt),
            warehouseEntryDate: order.warehouseEntryDate ? new Date(order.warehouseEntryDate) : undefined,
            combinedAt: order.combinedAt ? new Date(order.combinedAt) : undefined,
            // Ensure combined property is properly set
            combined: !!order.combined,
            // Ensure combinedGroupId is preserved
            combinedGroupId: order.combinedGroupId || undefined
          }));

          setOrders(formattedOrders);
        })
        .catch(error => {
          console.error("Error fetching orders:", error);
          setFetchError(error instanceof Error ? error.message : t("FAILED_TO_FETCH_ORDERS"));
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else if (!authLoading) {
      // If not authenticated and not loading auth state, stop loading orders
      setIsLoading(false);
    }
    // Dependency: Fetch when authentication status changes (after initial load)
  }, [isAuthenticated, authLoading]);

  // Filter orders based on the active status tab
  const filteredOrders = useMemo(() => {
    // Always exclude cancelled orders
    const visibleOrders = orders.filter(order => order.status !== OrderStatus.CANCELLED);

    if (activeStatusFilter === 'all') {
      return visibleOrders;
    }
    return visibleOrders.filter(order => order.status === activeStatusFilter);
  }, [orders, activeStatusFilter]);

  // Group orders by combinedGroupId for visualization
  const groupedOrders = useMemo(() => {
    // Create a map of combinedGroupId -> array of orders
    const groupMap = new Map<string, OrderType[]>();

    // First pass: group orders by combinedGroupId
    filteredOrders.forEach(order => {
      if (order.combined && order.combinedGroupId) {
        if (!groupMap.has(order.combinedGroupId)) {
          groupMap.set(order.combinedGroupId, []);
        }
        groupMap.get(order.combinedGroupId)?.push(order);
      }
    });

    // Sort each group by createdAt date
    groupMap.forEach(orders => {
      orders.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
    });

    return groupMap;
  }, [filteredOrders]);

  // --- Update Order Status Handler ---
  const handleUpdateOrderStatus = async (orderId: string | undefined, newStatus: OrderStatus) => {
    if (!orderId) {
      setUpdateError(t("INVALID_ORDER_ID"));
      return;
    }

    setUpdatingOrderId(orderId); // Indicate loading state for this specific order
    setUpdateError(null);

    try {
      console.log(`Updating order ${orderId} status to ${newStatus}`);
      const response = await fetch(`/api/orders/${orderId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });
      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `${t("FAILED_TO_UPDATE")} (${response.status})`);
      }

      // Update the order in the local state
      setOrders(prevOrders => prevOrders.map(order =>
        order._id === orderId ? { ...order, status: newStatus } : order
      ));
      console.log(`Order ${orderId} status updated to ${newStatus}.`);

    } catch (error) {
      console.error("Error updating order status:", error);
      setUpdateError(error instanceof Error ? error.message : t("FAILED_TO_UPDATE"));
      // Optionally show error to user (e.g., using a toast notification)
    } finally {
      setUpdatingOrderId(null); // Reset loading state
    }
  };

  // --- Open Order Details Handler ---
  const handleOpenOrderDetails = (orderId: string | undefined) => {
    if (orderId) {
      setSelectedOrderId(orderId);
      setIsDetailsOpen(true);
    }
  };

  // --- Close Order Details Handler ---
  const handleCloseOrderDetails = () => {
    setIsDetailsOpen(false);
    setSelectedOrderId(null);
  };

  // --- Toggle Order Selection Handler ---
  const toggleOrderSelection = (orderId: string | undefined) => {
    if (!orderId) return;

    // Find the order to check its status
    const order = orders.find(o => o._id === orderId);

    // Only allow selection of orders in PENDING_TRANSFER status
    if (order && order.status !== OrderStatus.PENDING_TRANSFER) {
      alert(t("ONLY_IN_PENDING_TRANSFER"));
      console.log(`Cannot select order ${orderId} with status ${order.status}, expected ${OrderStatus.PENDING_TRANSFER}`);
      return;
    }

    setSelectedOrders(prev => {
      const newSelection = new Set(prev);
      if (newSelection.has(orderId)) {
        newSelection.delete(orderId);
      } else {
        newSelection.add(orderId);
      }
      return newSelection;
    });
  };

  // --- Start Transfer Handler ---
  const handleStartTransfer = async (orderId: string | undefined) => {
    if (!orderId) return;
    await handleUpdateOrderStatus(orderId, OrderStatus.SHIPPED);
  };

  // --- Combine Boxes Handler ---
  const handleCombineBoxes = () => {
    // Debug log
    console.log('handleCombineBoxes called, selectedOrders:', selectedOrders);
    console.log('selectedOrders.size:', selectedOrders.size);

    // Check if at least 2 orders are selected
    if (selectedOrders.size < 2) {
      console.log('Less than 2 orders selected, showing alert');
      setShowCombineAlert(true);
      return;
    }

    // Show confirmation dialog with fee warning
    setShowCombineConfirm(true);
  };

  // --- Execute Combine Boxes ---
  const executeCombineBoxes = async () => {
    try {
      // Show loading state
      setUpdatingOrderId('combining'); // Use a special value to indicate combining operation

      // Call the API to combine orders
      const orderIdsArray = Array.from(selectedOrders);
      console.log('Executing combine boxes for orders:', orderIdsArray);

      // Double-check that all selected orders are in PENDING_TRANSFER status
      const selectedOrderObjects = orders.filter(order =>
        selectedOrders.has(order._id?.toString() || '')
      );

      console.log('Selected orders for combining:', selectedOrderObjects.map(o => ({
        id: o._id,
        status: o.status,
        expectedStatus: OrderStatus.PENDING_TRANSFER,
        isCorrectStatus: o.status === OrderStatus.PENDING_TRANSFER
      })));

      const allPendingTransfer = selectedOrderObjects.every(
        order => order.status === OrderStatus.PENDING_TRANSFER
      );

      if (!allPendingTransfer) {
        throw new Error(t("ONLY_IN_PENDING_TRANSFER_CAN_MERGE"));
      }

      // Check if any of the selected orders are already combined
      const anyCombined = selectedOrderObjects.some(order => order.combined);
      if (anyCombined) {
        throw new Error(t("CANNOT_COMBINE_AGAIN"));
      }

      const response = await fetch('/api/orders/combine', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderIds: orderIdsArray }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || errorData.error || `${t("FAILED_TO_COMBINE")} (Status: ${response.status})`);
      }

      const responseData = await response.json(); // Process the response to get combinedGroupId
      const combinedGroupId = responseData.combinedGroupId;

      // Update local state to reflect the changes
      setOrders(prevOrders => prevOrders.map(order => {
        if (selectedOrders.has(order._id?.toString() || '')) {
          return {
            ...order,
            combined: true,
            combinedAt: new Date(),
            combinedGroupId: combinedGroupId
          };
        }
        return order;
      }));

      // Clear selection after combining
      setSelectedOrders(new Set());

      // Close confirmation dialog
      setShowCombineConfirm(false);

      // Show success message
      alert(t("COMBINED_SUCCESSFULLY", { size: selectedOrders.size }));
    } catch (error) {
      console.error('Error combining orders:', error);
      alert(`${t("FAILED_TO_COMBINE")}: ${error instanceof Error ? error.message : ''}`);
    } finally {
      setUpdatingOrderId(null); // Reset loading state
    }
  };


  // --- Handle Payment for Pending Orders ---
  const handlePayment = async (orderId: string | undefined) => {
    if (!orderId) return;

    try {
      setProcessingPaymentOrderId(orderId);

      const response = await fetch('/api/orders/repay', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || errorData.error || `${t("FAILED_TO_PROCESS_PAYMENT")} (${response.status})`);
      }

      const data = await response.json();
      const sessionId = data.id;

      // Load Stripe and redirect to checkout
      const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
      await stripe?.redirectToCheckout({ sessionId });

    } catch (error) {
      console.error('Payment error:', error);
      alert(`${t("FAILED_TO_PROCESS_PAYMENT")}: ${error instanceof Error ? error.message : ''}`);
    } finally {
      setProcessingPaymentOrderId(null);
    }
  };

  // Loading state for initial auth check or order fetch
  if (authLoading || (isLoading && isAuthenticated)) {
     return (
       <div className="min-h-screen bg-[#FAFAFA]">
         <PageLayout>
           <div className="pt-20 container mx-auto px-4 pb-8 text-center">
             Loading...
           </div>
         </PageLayout>
       </div>
     );
  }

  // If not authenticated after loading, the redirect should have happened,
  // but render nothing or a message just in case.
  if (!isAuthenticated) {
     return null; // Or a login prompt
  }


  return (
    <div className="min-h-screen bg-[#FAFAFA]">
      <PageLayout>
        <div className="pt-20 container mx-auto px-4 pb-8">
          <h1 className="text-3xl font-bold mb-8">{tCommon("MY_ORDERS")}</h1>

          {/* Batch Actions for PENDING_TRANSFER orders */}
          {activeStatusFilter === OrderStatus.PENDING_TRANSFER && (
            <div className="flex justify-end mb-4">
              <Button
                variant="outline"
                className="bg-orange-50 text-orange-600 border-orange-200 hover:bg-orange-100"
                onClick={handleCombineBoxes}
              >
                {t("PACKING")}
              </Button>
            </div>
          )}

          {/* Status Filter Tabs */}
          <div className="flex space-x-1 sm:space-x-4 border-b mb-4 overflow-x-auto pb-2">
             {/* Order status tabs in specific order */}
             <Button
                variant="link"
                className={`pb-2 text-sm sm:text-base whitespace-nowrap ${activeStatusFilter === OrderStatus.PENDING ? 'text-orange-500 border-b-2 border-orange-500 font-semibold' : 'text-gray-600 hover:text-orange-500'}`}
                onClick={() => setActiveStatusFilter(OrderStatus.PENDING)}
             >
                {statusDisplayMap[OrderStatus.PENDING]}
             </Button>
             <Button
                variant="link"
                className={`pb-2 text-sm sm:text-base whitespace-nowrap ${activeStatusFilter === OrderStatus.PROCESSING ? 'text-orange-500 border-b-2 border-orange-500 font-semibold' : 'text-gray-600 hover:text-orange-500'}`}
                onClick={() => setActiveStatusFilter(OrderStatus.PROCESSING)}
             >
                {statusDisplayMap[OrderStatus.PROCESSING]}
             </Button>
             <Button
                variant="link"
                className={`pb-2 text-sm sm:text-base whitespace-nowrap ${activeStatusFilter === OrderStatus.PENDING_TRANSFER ? 'text-orange-500 border-b-2 border-orange-500 font-semibold' : 'text-gray-600 hover:text-orange-500'}`}
                onClick={() => setActiveStatusFilter(OrderStatus.PENDING_TRANSFER)}
             >
                {statusDisplayMap[OrderStatus.PENDING_TRANSFER]}
             </Button>
             <Button
                variant="link"
                className={`pb-2 text-sm sm:text-base whitespace-nowrap ${activeStatusFilter === OrderStatus.SHIPPED ? 'text-orange-500 border-b-2 border-orange-500 font-semibold' : 'text-gray-600 hover:text-orange-500'}`}
                onClick={() => setActiveStatusFilter(OrderStatus.SHIPPED)}
             >
                {statusDisplayMap[OrderStatus.SHIPPED]}
             </Button>
             <Button
                variant="link"
                className={`pb-2 text-sm sm:text-base whitespace-nowrap ${activeStatusFilter === 'all' ? 'text-orange-500 border-b-2 border-orange-500 font-semibold' : 'text-gray-600 hover:text-orange-500'}`}
                onClick={() => setActiveStatusFilter('all')}
             >
                {t("ALL_ORDERS")}
             </Button>
          </div>


          {/* Search bar */}
          {/* <div className="flex space-x-2 mb-6">
            <Input placeholder={t("ENTER_PRODUCT_TITLE")} className="flex-grow" />
            <Button>{t("SEARCH_ORDERS")}</Button>
          </div> */}

          {/* Order List Header - Dynamic based on status filter */}
          <div className="hidden md:grid grid-cols-8 gap-4 items-center p-2 bg-gray-100 rounded-t-md text-sm font-medium text-gray-600 mb-2">
            {activeStatusFilter === OrderStatus.PENDING_TRANSFER ? (
              <>
                <div className="text-center">{t("VOLUME")}</div>
                <div className="text-center flex items-center justify-center">
                  {t("BILLABLE_WEIGHT")}
                  <span className="ml-1 relative group">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-500 cursor-help">
                      <circle cx="12" cy="12" r="10"></circle>
                      <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                      <line x1="12" y1="17" x2="12.01" y2="17"></line>
                    </svg>
                    <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 bg-black text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-10">
                      {t("BILLABLE_WEIGHT_TIP")}
                    </span>
                  </span>
                </div>
                <div className="text-center">{t("PENT_FREE_PERIOD")}</div>
              </>
            ) : (
              <>
                <div className="text-center">{t("UNIT_PRICE")}</div>
                <div className="text-center">{t("AMOUNT")}</div>
              </>
            )}
            <div className="text-center">{t("ACTUAL_PAYMENT")}</div>
            <div className="text-center">{t("TRANSACTION_STATUS")}</div>
            <div className="text-center">{t("TRANS_OPERATIONS")}</div>
          </div>

          {/* Display Error or Order List */}
          {fetchError ? (
            <p className="text-center text-red-500 py-10">{fetchError}</p>
          ) : filteredOrders.length === 0 ? (
            <p className="text-center text-gray-500 py-10">
              {t("NO_ORDER_RECORD")}
            </p>
          ) : (
            <div className="space-y-4">
              {/* Process orders to show combined orders with connecting lines */}
              {filteredOrders.map((order) => {
                // Check if this order is part of a combined group
                const isCombined = order.combined && order.combinedGroupId;

                // Get all orders in this group
                const groupOrders = isCombined ?
                  [...(groupedOrders.get(order.combinedGroupId!) || [])] : [];

                // Skip if this order is not the first in its group (to avoid duplicates)
                if (isCombined && groupOrders.length > 0 && groupOrders[0]._id !== order._id) {
                  return null;
                }

                // If this is a combined group, render all orders in the group
                if (isCombined && groupOrders.length > 0) {
                  return (
                    <div key={order.combinedGroupId} className="relative pl-8 mt-8"> {/* Added left padding and top margin */}
                      {/* Combined group label */}
                      <div className="absolute left-0 top-[-1.5rem] text-xs text-blue-500 font-medium">
                        {t("ALREADY_PACKING")} ({groupOrders.length})
                      </div>
                      {/* Vertical curly brace to group combined orders */}
                      <div className="absolute left-0 top-0 bottom-0 w-6">
                        {/* Top part of the brace */}
                        <div className="absolute left-0 top-0 h-8 w-4 border-t-2 border-l-2 border-blue-400 border-dashed rounded-tl-lg"></div>
                        {/* Middle part of the brace */}
                        <div className="absolute left-0 top-8 bottom-8 w-0 border-l-2 border-blue-400 border-dashed"></div>
                        {/* Bottom part of the brace */}
                        <div className="absolute left-0 bottom-0 h-8 w-4 border-b-2 border-l-2 border-blue-400 border-dashed rounded-bl-lg"></div>
                      </div>

                      {/* Render each order in the group */}
                      {groupOrders.map((groupOrder, groupIndex) => (
                        <div key={groupOrder._id?.toString()} className="relative">
                          {/* Order card with right margin removed */}
                          <Card className={`mb-4 ${groupIndex > 0 ? 'border-t-0 rounded-t-none' : ''} ${groupIndex < groupOrders.length - 1 ? 'rounded-b-none' : ''}`}>
                            <CardHeader className="bg-gray-50 p-2 flex flex-row items-center justify-between text-sm text-gray-500">
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id={`order-${groupOrder._id?.toString()}`}
                                  checked={selectedOrders.has(groupOrder._id?.toString() || '')}
                                  onCheckedChange={() => toggleOrderSelection(groupOrder._id?.toString())}
                                />
                                <label htmlFor={`order-${groupOrder._id?.toString()}`}>{groupOrder.createdAt.toLocaleDateString()}</label>
                                <div className="flex items-center">
                                  <span>{t("ORDER_NUM")}: {groupOrder._id?.toString()}</span>
                                  {/* Add 'Combined' label if order is combined */}
                                  {groupOrder.combined && (
                                    <span className="ml-2 px-1.5 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">{t("PACKED")}</span>
                                  )}
                                </div>
                              </div>
                              {/* Action buttons for PENDING_TRANSFER status */}
                              {groupOrder.status === OrderStatus.PENDING_TRANSFER && (
                                <div className="flex space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-blue-600 border-blue-200 hover:bg-blue-50 h-7 text-xs"
                                    onClick={() => handleStartTransfer(groupOrder._id)}
                                    disabled={updatingOrderId === groupOrder._id}
                                  >
                                    {updatingOrderId === groupOrder._id ? (
                                      <>
                                        <svg className="animate-spin -ml-1 mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        {t("PROCESSING_")}
                                      </>
                                    ) : t("START_TRANSSHIPMENT")}
                                  </Button>
                                  {groupIndex === 0 && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-orange-600 border-orange-200 hover:bg-orange-50 h-7 text-xs"
                                      onClick={(e) => {
                                        e.stopPropagation(); // Prevent event bubbling
                                        handleCombineBoxes();
                                      }}
                                    >
                                      {t("PACKING")}
                                    </Button>
                                  )}
                                </div>
                              )}
                            </CardHeader>
                            <CardContent className="p-0">
                              {/* Add type annotation for item and index */}
                              {groupOrder.items.map((item: OrderItem, index: number) => (
                                <div key={`${groupOrder._id?.toString()}-${index}`} className="grid grid-cols-1 md:grid-cols-8 gap-4 items-center p-4 border-b last:border-b-0 overflow-x-auto">
                                  {/* Item Details */}
                                  <div className="col-span-1 md:col-span-3 flex space-x-3 items-center">
                                    <img src={item.imageUrl || '/images/placeholder.png'} alt={item.title} className="w-16 h-16 object-cover rounded" />
                                    <div className="flex flex-col justify-center">
                                      <p className="text-sm line-clamp-2">{item.title}</p>
                                      <p className="text-xs text-gray-500">{item.sku}</p>
                                    </div>
                                  </div>
                                  {/* Dynamic columns based on order status */}
                                  {groupOrder.status === OrderStatus.PENDING_TRANSFER ? (
                                    <>
                                      {/* Volume */}
                                      <div className="text-sm md:text-center flex items-center justify-center">
                                        {item.dimensions || '26*25*10cm'}
                                      </div>
                                      {/* Billable Weight */}
                                      <div className="text-sm md:text-center flex items-center justify-center">
                                        {item.billableWeight ? `${item.billableWeight}kg` : '1.49kg'}
                                      </div>
                                      {/* Free Storage Period */}
                                      <div className="text-sm md:text-center flex items-center justify-center">
                                        {groupOrder.freeStoragePeriod ? `${groupOrder.freeStoragePeriod}${tCommon("DAYS")}` : `90${tCommon("DAYS")}`}
                                      </div>
                                    </>
                                  ) : (
                                    <>
                                      {/* Price */}
                                      <div className="text-sm md:text-center flex items-center justify-center">${item.price.toFixed(2)}</div>
                                      {/* Quantity */}
                                      <div className="text-sm md:text-center flex items-center justify-center">{item.quantity}</div>
                                    </>
                                  )}
                                  {/* Total Amount */}
                                  <div className="text-sm md:text-center font-semibold flex items-center justify-center">${(item.price * item.quantity).toFixed(2)}</div>
                                  {/* Status */}
                                  <div className="text-sm md:text-center flex flex-col space-y-1 items-center justify-center">
                                    <span className={`font-medium ${groupOrder.status === OrderStatus.PENDING ? 'text-red-600' : 'text-gray-700'}`}>
                                      {statusDisplayMap[groupOrder.status as keyof typeof statusDisplayMap]}
                                    </span>
                                  </div>
                                  {/* Actions */}
                                  <div className="text-sm md:text-center flex flex-row space-x-2 items-center justify-center">
                                    {groupOrder.status === OrderStatus.PENDING && (
                              <Button
                                size="sm"
                                className="text-xs h-7 bg-blue-600 hover:bg-blue-700 text-white"
                                onClick={() => handlePayment(groupOrder._id)}
                                disabled={processingPaymentOrderId === groupOrder._id}
                              >
                                {processingPaymentOrderId === groupOrder._id ? (
                                  <>
                                    <svg className="animate-spin -ml-1 mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    {t("PROCESSING_")}
                                  </>
                                ) :  t("GO_PAY")}
                              </Button>
                            )}
                                    {groupOrder.status === OrderStatus.PROCESSING && (
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        className="bg-green-50 text-green-600 border-green-200 hover:bg-green-100 text-xs h-7"
                                        onClick={() => handleUpdateOrderStatus(groupOrder._id, OrderStatus.PENDING_TRANSFER)}
                                        disabled={updatingOrderId === groupOrder._id}
                                      >
                                        {updatingOrderId === groupOrder._id ? t("PROCESSING_") : t("SIMULATE")}
                                      </Button>
                                    )}
                                    <Button
                                      variant="link"
                                      size="sm"
                                      className="text-xs"
                                      onClick={() => handleOpenOrderDetails(groupOrder._id)}
                                    >
                                      {t("ORDER_DETAIL")}
                                    </Button>
                                    {updateError && updatingOrderId === groupOrder._id && (
                                      <p className="text-red-500 text-xs">{updateError}</p>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </CardContent>
                          </Card>
                        </div>
                      ))}
                    </div>
                  );
                }

                // Regular non-combined order
                return (
                  <Card key={order._id?.toString()} className="mb-4">
                    <CardHeader className="bg-gray-50 p-2 flex flex-row items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`order-${order._id?.toString()}`}
                          checked={selectedOrders.has(order._id?.toString() || '')}
                          onCheckedChange={() => toggleOrderSelection(order._id?.toString())}
                        />
                        <label htmlFor={`order-${order._id?.toString()}`}>{order.createdAt.toLocaleDateString()}</label>
                        <div className="flex items-center">
                          <span>{t("ORDER_NUM")}: {order._id?.toString()}</span>
                          {/* Add 'Combined' label if order is combined */}
                          {order.combined && (
                            <span className="ml-2 px-1.5 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">{t("PACKED")}</span>
                          )}
                        </div>
                      </div>
                      {/* Action buttons for PENDING_TRANSFER status */}
                      {order.status === OrderStatus.PENDING_TRANSFER && (
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-blue-600 border-blue-200 hover:bg-blue-50 h-7 text-xs"
                            onClick={() => handleStartTransfer(order._id)}
                            disabled={updatingOrderId === order._id}
                          >
                            {updatingOrderId === order._id ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                {t("PROCESSING_")}
                              </>
                            ) :  t("START_TRANSSHIPMENT")}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-orange-600 border-orange-200 hover:bg-orange-50 h-7 text-xs"
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent event bubbling
                              handleCombineBoxes();
                            }}
                          >
                            {t("PACKING")}
                          </Button>
                        </div>
                      )}
                    </CardHeader>
                    <CardContent className="p-0">
                      {/* Add type annotation for item and index */}
                      {order.items.map((item: OrderItem, index: number) => (
                        <div key={`${order._id?.toString()}-${index}`} className="grid grid-cols-1 md:grid-cols-8 gap-4 items-center p-4 border-b last:border-b-0 overflow-x-auto">
                          {/* Item Details */}
                          <div className="col-span-1 md:col-span-3 flex space-x-3 items-center">
                            <img src={item.imageUrl || '/images/placeholder.png'} alt={item.title} className="w-16 h-16 object-cover rounded" />
                            <div className="flex flex-col justify-center">
                              <p className="text-sm line-clamp-2">{item.title}</p>
                              <p className="text-xs text-gray-500">{item.sku}</p>
                            </div>
                          </div>
                          {/* Dynamic columns based on order status */}
                          {order.status === OrderStatus.PENDING_TRANSFER ? (
                            <>
                              {/* Volume */}
                              <div className="text-sm md:text-center flex items-center justify-center">
                                {item.dimensions || '26*25*10cm'}
                              </div>
                              {/* Billable Weight */}
                              <div className="text-sm md:text-center flex items-center justify-center">
                                {item.billableWeight ? `${item.billableWeight}kg` : '1.49kg'}
                              </div>
                              {/* Free Storage Period */}
                              <div className="text-sm md:text-center flex items-center justify-center">
                                {order.freeStoragePeriod ? `${order.freeStoragePeriod}${tCommon("DAYS")}` : `90${tCommon("DAYS")}`}
                              </div>
                            </>
                          ) : (
                            <>
                              {/* Price */}
                              <div className="text-sm md:text-center flex items-center justify-center">${item.price.toFixed(2)}</div>
                              {/* Quantity */}
                              <div className="text-sm md:text-center flex items-center justify-center">{item.quantity}</div>
                            </>
                          )}
                          {/* Total Amount */}
                          <div className="text-sm md:text-center font-semibold flex items-center justify-center">${(item.price * item.quantity).toFixed(2)}</div>
                          {/* Status */}
                          <div className="text-sm md:text-center flex flex-col space-y-1 items-center justify-center">
                            <span className={`font-medium ${order.status === OrderStatus.PENDING ? 'text-red-600' : 'text-gray-700'}`}>
                              {statusDisplayMap[order.status as keyof typeof statusDisplayMap]}
                            </span>
                          </div>
                          {/* Actions */}
                          <div className="text-sm md:text-center flex flex-row space-x-2 items-center justify-center">
                            {order.status === OrderStatus.PENDING && (
                              <Button
                                size="sm"
                                className="text-xs h-7 bg-blue-600 hover:bg-blue-700 text-white"
                                onClick={() => handlePayment(order._id)}
                                disabled={processingPaymentOrderId === order._id}
                              >
                                {processingPaymentOrderId === order._id ? (
                                  <>
                                    <svg className="animate-spin -ml-1 mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    {t("PROCESSING_")}
                                  </>
                                ) : t("GO_PAY")}
                              </Button>
                            )}
                            {order.status === OrderStatus.PROCESSING && (
                              <Button
                                size="sm"
                                variant="outline"
                                className="bg-green-50 text-green-600 border-green-200 hover:bg-green-100 text-xs h-7"
                                onClick={() => handleUpdateOrderStatus(order._id, OrderStatus.PENDING_TRANSFER)}
                                disabled={updatingOrderId === order._id}
                              >
                                {updatingOrderId === order._id ? t("PROCESSING_") : t("SIMULATE")}
                              </Button>
                            )}
                            <Button
                              variant="link"
                              size="sm"
                              className="text-xs"
                              onClick={() => handleOpenOrderDetails(order._id)}
                            >
                              {t("ORDER_DETAIL")}
                            </Button>
                            {updateError && updatingOrderId === order._id && (
                              <p className="text-red-500 text-xs">{updateError}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}

          {/* Pagination (optional) */}
          {/* <div className="flex justify-end space-x-2 mt-6">
            <Button variant="outline">上一页</Button>
            <Button variant="outline">下一页</Button>
          </div> */}

          {/* Order Details Popup */}
          {selectedOrderId && (
            <OrderDetailsPopup
              isOpen={isDetailsOpen}
              onClose={handleCloseOrderDetails}
              orderId={selectedOrderId}
              orderStatus={orders.find(order => order._id === selectedOrderId)?.status || OrderStatus.PENDING}
              orderTotal={orders.find(order => order._id === selectedOrderId)?.total || 0}
              createdAt={orders.find(order => order._id === selectedOrderId)?.createdAt || new Date()}
              updatedAt={orders.find(order => order._id === selectedOrderId)?.updatedAt || new Date()}
              warehouseEntryDate={orders.find(order => order._id === selectedOrderId)?.warehouseEntryDate}
            />
          )}

          {/* Combine Boxes Alert Dialog - Not enough orders selected */}
          <AlertDialog open={showCombineAlert} onOpenChange={setShowCombineAlert}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>{t("SELECT_LEAST_TWO")}</AlertDialogTitle>
                <AlertDialogDescription>
                  {t("SELECT_LEAST_TWO_PACKING")}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogAction>{tCommon("CONFIRM")}</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          {/* Combine Boxes Confirmation Dialog - With fee warning */}
          <AlertDialog open={showCombineConfirm} onOpenChange={setShowCombineConfirm}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>{t("CONFIRM_PACKING")}</AlertDialogTitle>
                <AlertDialogDescription>
                  {t("NON_REFUNDABLE")}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogAction onClick={() => setShowCombineConfirm(false)}>{tCommon("CANCEL")}</AlertDialogAction>
                <AlertDialogAction onClick={executeCombineBoxes} className="bg-orange-600 hover:bg-orange-700">{tCommon("CONFIRM")}</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </PageLayout>
    </div>
  );
};

export default function MyOrdersPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <Page />
    </Suspense>
  );
};
