"use client";

import { useEffect } from "react";

export default function ClientBody({
  children,
}: {
  children: React.ReactNode;
}) {
  // Remove any extension-added classes during hydration
  useEffect(() => {
    // This runs only on the client after hydration
    document.body.className = "font-noto-sans antialiased";
  }, []);

  return (
    <body className="font-noto-sans antialiased" suppressHydrationWarning>
      {children}
    </body>
  );
}
