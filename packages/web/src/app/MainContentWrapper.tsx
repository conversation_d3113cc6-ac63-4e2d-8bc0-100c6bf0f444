"use client";

import { usePathname } from 'next/navigation'; // Import usePathname
import { useSidebar } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";

// List of routes that should not have sidebar padding applied
const AUTH_ROUTES = ['/login', '/signup', '/verify-email'];

export default function MainContentWrapper({
    children,
}: {
    children: React.ReactNode;
}) {
    const { state, isMobile } = useSidebar();
    const pathname = usePathname(); // Get current path

    // Check if the current route is an auth route
    const isAuthRoute = AUTH_ROUTES.includes(pathname);

    // Determine padding based on sidebar state, mobile view, and route
    const paddingLeftClass = isMobile || isAuthRoute // No padding on mobile OR auth routes
        ? ""
        : state === "expanded"
          ? "md:pl-[var(--sidebar-width)]" // Use variable for desktop expanded
          : ""; // No padding when sidebar is collapsed

    return (
        // Apply padding class conditionally based on route
        <main className={cn("flex-1 transition-[padding-left] duration-200 ease-linear w-full max-w-full main-content-wrapper", paddingLeftClass)}>
            {/* pt-20 might be needed here if Navbar is fixed, or keep it on individual pages */}
            {/* The pt-20 for the fixed Navbar should likely be applied here or within specific page layouts,
                but NOT in the (auth) layout if the Navbar isn't present there.
                Consider adding pt-20 conditionally based on !isAuthRoute if Navbar is always fixed.
            */}
            {children}
        </main>
    );
}
