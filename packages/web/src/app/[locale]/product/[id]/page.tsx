import { Suspense, use } from 'react';
import ProductDetailPage from '../../../product/[id]/page';

export default function LocalizedProductDetailPage({ params: paramsPromise, searchParams: searchParamsPromise }: { 
  params: Promise<{ id: string }>,
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const params = use(paramsPromise);
  const searchParams = use(searchParamsPromise);

  return (
    <ProductDetailPage params={params} searchParams={searchParams} />
  );
}
