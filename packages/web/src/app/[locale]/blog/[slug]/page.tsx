import { Suspense } from 'react';
import BlogDetailPage from '../../../blog/[slug]/page';

interface LocalizedBlogDetailPageProps {
  params: {
    locale: string;
    slug: string;
  };
}

export default async function LocalizedBlogDetailPage({ params }: LocalizedBlogDetailPageProps) {
  // Make params.slug available asynchronously to avoid Next.js warning
  const slug = await Promise.resolve(params.slug);

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <BlogDetailPage params={{ slug }} />
    </Suspense>
  );
}
