'use client';

import { NextIntlClientProvider } from 'next-intl';
import { useEffect, useState } from 'react';
import { LanguageProvider } from '@/lib/language-context';

export function IntlProvider({
  locale,
  children,
}: {
  locale: string;
  children: React.ReactNode;
}) {
  const [messages, setMessages] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load messages on the client side
    const loadMessages = async () => {
      setIsLoading(true);
      try {
        const messages = (await import(`../../messages/${locale}.json`)).default;
        setMessages(messages);
      } catch (error) {
        console.error("Failed to load translation messages:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadMessages();
  }, [locale]);

  // Show a simple loading state while translations are being loaded
  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  return (
    <NextIntlClientProvider
      locale={locale}
      messages={messages}
      timeZone="UTC"
    >
      <LanguageProvider>
        {children}
      </LanguageProvider>
    </NextIntlClientProvider>
  );
}
