'use client';

import { ReactNode, use } from 'react';
import { IntlProvider } from './providers';
import { locales } from '@/i18n';
import { ComparisonCard } from '@/components/shared/ComparisonCard';
import { CartPanel } from '@/components/shared/CartPanel';
import { WeChatPanel } from '@/components/shared/WeChatPanel';
import { useLoadBrowsTranslationEngine } from '@/hooks/useBrowserTranslation';

export default function LocaleLayout({
  children,
  params
}: {
  children: ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = use(params);
  useLoadBrowsTranslationEngine();
  // Validate that the locale is supported
  if (!locales.includes(locale)) {
    // This shouldn't happen due to middleware, but just in case
    return null;
  }

  return (
    <IntlProvider locale={locale}>
      {children}
      <ComparisonCard />
      <CartPanel /> {/* Add CartPanel here */}
      <WeChatPanel /> {/* Add WeChatPanel here */}
    </IntlProvider>
  );
}
