"use client";

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useAuth } from '@/lib/auth-context';
import { PageLayout } from "@/components/shared/PageLayout";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle } from "lucide-react";
import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';

function Page() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session, status, update: updateSession } = useSession();
  const { refreshSession } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [planUpdated, setPlanUpdated] = useState(false);
  const t = useTranslations(I18NNamespace.PAGES_SUBSCRIPTION);

  // Effect to verify subscription and update user's plan in database
  useEffect(() => {
    // Skip if already verified or no session ID
    if (planUpdated) return;

    const sessionId = searchParams.get('session_id');
    if (!sessionId) {
      setError('No session ID found');
      setLoading(false);
      return;
    }

    // Verify the subscription and update the user's plan
    const verifySubscription = async () => {
      try {
        // Call our API to update the user's plan
        const response = await fetch('/api/subscription/verify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ sessionId }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to verify subscription');
        }

        // Success - user's plan has been updated in the database
        setPlanUpdated(true);
        setLoading(false);

        // Refresh both the NextAuth session and the auth context
        await updateSession();
        await refreshSession();
        console.log('Session updated after subscription verification');
      } catch (err) {
        console.error('Subscription verification error:', err);
        setError(err instanceof Error ? err.message : 'Failed to verify subscription');
        setLoading(false);
      }
    };

    verifySubscription();
  }, [searchParams, planUpdated, updateSession]);

  return (
    <div className="min-h-screen bg-[#FAFAFA]">
      <PageLayout>
        <div className="pt-20 container mx-auto px-4 pb-8 max-w-3xl">
          <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-100">
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accio-primary mx-auto"></div>
                <p className="mt-4 text-lg">{t("VERIFING")}</p>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <div className="bg-red-100 text-red-700 p-4 rounded-md mb-6">
                  <p>{error}</p>
                </div>
                <Button onClick={() => router.push('/')}>{t("RETURN")}</Button>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="flex justify-center mb-6">
                  <CheckCircle className="h-16 w-16 text-green-500" />
                </div>
                <h1 className="text-3xl font-bold mb-4">{t("SUB_SUCCESS")}</h1>
                <div className="flex items-center justify-center mb-2">
                  <span className="inline-flex items-center justify-center rounded-md border font-medium px-2 py-1 text-sm bg-black text-white">
                    {session?.user?.plan || 'Pro'}
                  </span>
                </div>
                <p className="text-lg mb-8">
                  {t("THANK_YOU")}
                </p>
                <div className="bg-gray-50 p-6 rounded-lg mb-8">
                  <h2 className="text-xl font-semibold mb-4">{t("BENEFITS")}</h2>
                  <ul className="text-left space-y-2">
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>{t("AI_ASSISTED")}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>{t("ONLINE_APPOINTMENT")}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>{t("STRATEGIC_ADVICE")}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>{t("PRIOR_ACCESS")}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>{t("ORGANIZATION")}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>{t("OEM_ODM")}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>{t("EXCLUSIVE_DISCOUNTS")}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>{t("MONITORING")}</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>{t("CONTRACT_ASSISTANCE")}</span>
                    </li>
                  </ul>
                </div>
                <div className="space-y-4">
                  <Button
                    onClick={() => router.push('/')}
                    className="bg-accio-primary hover:bg-accio-primary/90"
                  >
                    {t("START_EXPLORING")}
                  </Button>

                  {session?.user?.plan !== 'Pro' && (
                    <div className="mt-4">
                      <p className="text-sm text-gray-500 mb-2">{t("DONT_SEE_PRO")}</p>
                      <Button
                        onClick={async () => {
                          // Refresh both sessions
                          await updateSession();
                          await refreshSession();
                          window.location.reload();
                        }}
                        variant="outline"
                      >
                        {t("REFRESH_SESSION")}
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </PageLayout>
    </div>
  );
}

export default function SubscriptionSuccessPage () {
  return <Suspense fallback="Loading"><Page /></Suspense>
}