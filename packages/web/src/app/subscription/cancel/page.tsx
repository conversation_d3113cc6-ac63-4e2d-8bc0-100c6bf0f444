"use client";

import { useRouter } from 'next/navigation';
import { PageLayout } from "@/components/shared/PageLayout";
import { But<PERSON> } from "@/components/ui/button";
import { XCircle } from "lucide-react";
import { I18NNamespace, useTranslations } from '@/hooks/useTranslations';

export default function SubscriptionCancelPage() {
  const router = useRouter();
  const t = useTranslations(I18NNamespace.PAGES_SUBSCRIPTION);

  return (
    <div className="min-h-screen bg-[#FAFAFA]">
      <PageLayout>
        <div className="pt-20 container mx-auto px-4 pb-8 max-w-3xl">
          <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-100">
            <div className="text-center py-8">
              <div className="flex justify-center mb-6">
                <XCircle className="h-16 w-16 text-gray-400" />
              </div>
              <h1 className="text-3xl font-bold mb-4">{t("SUB_CANCELED")}</h1>
              <p className="text-lg mb-8">
                {t("NO_CHARGE")}
              </p>
              <div className="space-y-4">
                <Button 
                  onClick={() => router.push('/')}
                  className="bg-gray-900 hover:bg-gray-800"
                >
                  {t("RETURN")}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </PageLayout>
    </div>
  );
}
