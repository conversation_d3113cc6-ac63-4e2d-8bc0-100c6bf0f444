import fs from 'fs';
import path from 'path';
import { BlogPost } from '@/types/blog';

// Map of language codes to their respective blog directories
const BLOG_DIRS = {
  en: 'enblog',
  fr: 'frblog',
  zh: 'cnblog',
};

// Default language if none is specified
const DEFAULT_LANGUAGE = 'en';

// Path to blog images directory (in public folder)
const BLOG_IMAGES_DIR = '/images/blog';

/**
 * Loads a blog post from the file system based on the slug and language
 * @param slug The slug of the blog post
 * @param lang The language code (en, fr, zh)
 * @returns The blog post or null if not found
 */
export async function getBlogPostBySlug(slug: string, lang: string = DEFAULT_LANGUAGE): Promise<BlogPost | null> {
  try {
    // Get the directory for the specified language or default to English
    const langDir = BLOG_DIRS[lang as keyof typeof BLOG_DIRS] || BLOG_DIRS[DEFAULT_LANGUAGE];
    const dataDir = path.join(process.cwd(), 'src', 'data');
    const blogDir = path.join(dataDir, langDir);

    // Read all files in the blog directory
    const files = fs.readdirSync(blogDir);

    // Find a file that contains the slug
    const matchingFile = files.find(file => file.includes(slug) && file.endsWith('.md'));

    if (!matchingFile) {
      return null;
    }

    // Read the file content
    const filePath = path.join(blogDir, matchingFile);
    const fileContent = fs.readFileSync(filePath, 'utf8');

    // Parse the blog post data (it's in a JavaScript object format)
    // We need to convert it to a proper JSON object
    const blogPostData = eval(`(${fileContent})`);

    // No need to process image URLs if they're already using the correct path
    // Just keep them as they are since they already point to /images/blog/

    // We don't need to modify the content either since the image paths are already correct

    return {
      ...blogPostData,
      // Ensure dates are properly converted to Date objects
      createdAt: new Date(blogPostData.createdAt),
      updatedAt: new Date(blogPostData.updatedAt),
      publishedAt: new Date(blogPostData.publishedAt),
    };
  } catch (error) {
    console.error(`Error loading blog post ${slug} in language ${lang}:`, error);
    return null;
  }
}

/**
 * Gets all blog posts for a specific language
 * @param lang The language code (en, fr, zh)
 * @returns Array of blog posts
 */
export async function getAllBlogPosts(lang: string = DEFAULT_LANGUAGE): Promise<BlogPost[]> {
  try {
    // Get the directory for the specified language or default to English
    const langDir = BLOG_DIRS[lang as keyof typeof BLOG_DIRS] || BLOG_DIRS[DEFAULT_LANGUAGE];
    const dataDir = path.join(process.cwd(), 'src', 'data');
    const blogDir = path.join(dataDir, langDir);

    // Read all files in the blog directory
    const files = fs.readdirSync(blogDir);

    // Process each file and convert to BlogPost
    const blogPosts: BlogPost[] = [];

    for (const file of files) {
      if (file.endsWith('.md')) {
        const filePath = path.join(blogDir, file);
        const fileContent = fs.readFileSync(filePath, 'utf8');

        // Parse the blog post data
        const blogPostData = eval(`(${fileContent})`);

        // No need to process image URLs if they're already using the correct path
        // Just keep them as they are since they already point to /images/blog/

        // We don't need to modify the content either since the image paths are already correct

        blogPosts.push({
          ...blogPostData,
          // Ensure dates are properly converted to Date objects
          createdAt: new Date(blogPostData.createdAt),
          updatedAt: new Date(blogPostData.updatedAt),
          publishedAt: new Date(blogPostData.publishedAt),
        });
      }
    }

    // Sort by publishedAt date (newest first)
    return blogPosts.sort((a, b) => b.publishedAt.getTime() - a.publishedAt.getTime());
  } catch (error) {
    console.error(`Error loading blog posts for language ${lang}:`, error);
    return [];
  }
}
