import { NextRequest, NextResponse } from 'next/server';
import { getAllBlogPosts } from '../utils';

export async function GET(request: NextRequest) {
  try {
    // Get the language from the query parameter
    const searchParams = request.nextUrl.searchParams;
    const lang = searchParams.get('lang') || 'en';
    
    // Get all blog posts for the specified language
    const posts = await getAllBlogPosts(lang);
    
    // Return the posts as JSON
    return NextResponse.json(posts);
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch blog posts' },
      { status: 500 }
    );
  }
}
