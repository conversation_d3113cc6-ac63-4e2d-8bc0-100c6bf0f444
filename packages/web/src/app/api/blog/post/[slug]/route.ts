import { NextRequest, NextResponse } from 'next/server';
import { getBlogPostBySlug } from '../../utils';

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Get the slug from the URL parameters and await it to avoid Next.js warning
    const slug = await Promise.resolve(params.slug);

    // Get the language from the query parameter
    const searchParams = request.nextUrl.searchParams;
    const lang = searchParams.get('lang') || 'en';

    // Get the blog post
    const post = await getBlogPostBySlug(slug, lang);

    if (!post) {
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      );
    }

    // Return the post as JSON
    return NextResponse.json(post);
  } catch (error) {
    console.error(`Error fetching blog post with slug ${params.slug}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch blog post' },
      { status: 500 }
    );
  }
}
