import { NextResponse } from 'next/server';
import { getTrendingSearches, getPopularSearchTerms } from '@/models/userSearch';

/**
 * GET handler for trending searches
 * Returns the most recent searches and popular search terms
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const type = searchParams.get('type') || 'all';

    // Get trending searches based on the requested type
    if (type === 'terms') {
      // Return only popular search terms
      const popularTerms = await getPopularSearchTerms(limit);
      return NextResponse.json({ popularTerms });
    } else if (type === 'searches') {
      // Return only recent searches with display data
      const recentSearches = await getTrendingSearches(limit);
      return NextResponse.json({ recentSearches });
    } else {
      // Return both popular terms and recent searches
      const [popularTerms, recentSearches] = await Promise.all([
        getPopularSearchTerms(limit),
        getTrendingSearches(limit)
      ]);
      return NextResponse.json({ popularTerms, recentSearches });
    }
  } catch (error: any) {
    console.error('Trending API error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to get trending searches' },
      { status: 500 }
    );
  }
}
