import { NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { connectToDatabase, Collections } from '@/lib/database';
import { ObjectId } from 'mongodb';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: Request) {
  // Get user session server-side
  const authSession = await getServerSession(authOptions);

  if (!authSession || !authSession.user?.id) {
    return NextResponse.json({ error: 'Unauthorized: User not logged in' }, { status: 401 });
  }
  const userId = authSession.user.id;

  try {
    const { sessionId } = await request.json();

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    // Retrieve the Stripe checkout session
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    // Verify that the session is completed and paid
    if (session.payment_status !== 'paid') {
      return NextResponse.json({ error: 'Payment not completed' }, { status: 400 });
    }

    // Verify that this session belongs to the current user
    // This is a security check to prevent users from verifying other users' subscriptions
    if (session.metadata?.userId && session.metadata.userId !== userId) {
      return NextResponse.json({ error: 'Session does not belong to current user' }, { status: 403 });
    }

    // Connect to the database
    const { db } = await connectToDatabase();

    // Update the user's plan to Pro
    const result = await db.collection(Collections.USERS).updateOne(
      { _id: new ObjectId(userId) },
      { 
        $set: { 
          plan: 'Pro',
          updatedAt: new Date(),
          // Store Stripe subscription info for future reference
          stripeCustomerId: session.customer as string,
          stripeSubscriptionId: session.subscription as string
        } 
      }
    );

    if (result.modifiedCount === 0) {
      return NextResponse.json({ error: 'Failed to update user plan' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Subscription verified and user plan updated to Pro' });
  } catch (err) {
    console.error('Subscription verification error:', err);
    return NextResponse.json(
      { error: 'Error verifying subscription' },
      { status: 500 }
    );
  }
}
