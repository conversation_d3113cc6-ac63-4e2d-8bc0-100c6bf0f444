import { NextResponse } from 'next/server';
import Stripe from 'stripe';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: Request) {
  // Get user session server-side
  const authSession = await getServerSession(authOptions);

  if (!authSession || !authSession.user?.id) {
    return NextResponse.json({ error: 'Unauthorized: User not logged in' }, { status: 401 });
  }
  const userId = authSession.user.id;
  const customerEmail = authSession.user.email;

  try {
    // Create a Stripe checkout session for subscription
    const stripeSession = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: 'Miccobuy Pro',
              description: `🤖 Agent de sourcing intelligent assisté par IA + expert humain dédié\n
               📆 RDV en ligne personnalisé avec un conseiller sourcing\n
               🧭 Conseil stratégique pour vos achats, logistique et développement produit\n
               💼 Accès prioritaire aux fournisseurs vérifiés & partenaires privilégiés\n
               🏭 Organisation de visites en usine & audits qualité\n
               🎨 Service OEM / ODM & personnalisation produit (logo, packaging, etc.)\n
               💰 Réductions exclusives sur une sélection de produits et services\n
               📦 Suivi de production & coordination logistique centralisée\n
               🛡️ Assistance sur contrats, paiements sécurisés et gestion des risques`,
            },
            unit_amount: 9900, // Temporarily changed from 9900 (99 EUR) to 100 (1 EUR) in cents for testing
            recurring: {
              interval: 'month',
            },
          },
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXTAUTH_URL}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXTAUTH_URL}/subscription/cancel`,
      customer_email: customerEmail,
      metadata: {
        userId: userId,
      },
      // Store the userId in the client_reference_id as well for redundancy
      client_reference_id: userId,
    });

    return NextResponse.json({ id: stripeSession.id });
  } catch (err) {
    console.error('Stripe subscription error:', err);
    return NextResponse.json(
      { error: 'Error creating subscription session' },
      { status: 500 }
    );
  }
}
