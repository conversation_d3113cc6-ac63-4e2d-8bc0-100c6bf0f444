import { NextRequest, NextResponse } from 'next/server';
import adminConfig from '../../../../../adminConfig.mjs';
import { getProductFromCache, saveProductToCache } from '@/models/productCache';

// In-memory cache for product details (faster than database for frequent requests)
const productCache = new Map<string, { data: any, timestamp: number }>();

/**
 * GET handler for product details
 * @param request The incoming request
 * @param params Route parameters including product ID
 * @returns Response with product details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Ensure params is properly handled
    const productId = params.id;
    const url = new URL(request.url);

    // Get source from query params, check which APIs are enabled in config
    let source = url.searchParams.get('source');

    // If no source is specified or the specified source is disabled, use the first enabled API
    if (!source ||
        (source === 'taobao' && !adminConfig.taobaoApi.enabled.itemGet) ||
        (source === '1688' && !adminConfig.api1688.enabled.itemGet) ||
        (source === 'alibaba' && !adminConfig.alibabaApi.enabled.itemGet)) {

      // Check which APIs are enabled and use the first available one
      if (adminConfig.api1688.enabled.itemGet) {
        source = '1688';
      } else if (adminConfig.alibabaApi.enabled.itemGet) {
        source = 'alibaba';
      } else if (adminConfig.taobaoApi.enabled.itemGet) {
        source = 'taobao';
      } else {
        return NextResponse.json(
          { error: 'No product API is enabled in configuration' },
          { status: 503 }
        );
      }
    }

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    // Create a cache key that includes the source
    const cacheKey = `${source}:${productId}`;

    // Use the appropriate API config based on source
    let apiConfig;
    if (source === '1688') {
      apiConfig = adminConfig.api1688;
    } else if (source === 'alibaba') {
      apiConfig = adminConfig.alibabaApi;
    } else {
      apiConfig = adminConfig.taobaoApi;
    }

    // Check if the selected API endpoint is enabled
    if (!apiConfig.enabled.itemGet) {
      return NextResponse.json(
        { error: `${source} itemGet API is disabled in configuration` },
        { status: 503 }
      );
    }

    const now = Date.now();
    const cacheExpirySeconds = apiConfig.cache.itemGet;

    // Check in-memory cache first (fastest)
    const inMemoryCachedProduct = productCache.get(cacheKey);
    if (inMemoryCachedProduct && (now - inMemoryCachedProduct.timestamp) < cacheExpirySeconds * 1000) {
      console.log(`[Product API] In-memory cache hit for ${cacheKey}`);
      return NextResponse.json(inMemoryCachedProduct.data);
    }

    // If not in memory, check database cache
    const dbCachedProduct = await getProductFromCache(cacheKey);
    if (dbCachedProduct) {
      console.log(`[Product API] Database cache hit for ${cacheKey}`);

      // Update in-memory cache with the database result
      productCache.set(cacheKey, dbCachedProduct);

      return NextResponse.json(dbCachedProduct.data);
    }

    console.log(`[Product API] Cache miss for ${cacheKey}, fetching from API`);

    // Validate the product ID
    if (!/^\d+$/.test(productId)) {
      return NextResponse.json(
        { error: 'Invalid product ID format. Expected a numeric ID.' },
        { status: 400 }
      );
    }

    // Check if API configuration is valid
    if (!apiConfig || !apiConfig.baseUrl || !apiConfig.endpoints.itemGet) {
      return NextResponse.json(
        { error: 'Invalid API configuration' },
        { status: 500 }
      );
    }

    // Construct the API URL with proper error handling
    let apiUrl;
    let apiUrlString;

    try {
      // First, construct the base URL
      const baseUrlString = `${apiConfig.baseUrl}${apiConfig.endpoints.itemGet}`;

      // Create URL object
      apiUrl = new URL(baseUrlString);

      // Add query parameters
      apiUrl.searchParams.append('key', apiConfig.key);
      apiUrl.searchParams.append('secret', apiConfig.secret);
      apiUrl.searchParams.append('num_iid', productId);
      apiUrl.searchParams.append('is_promotion', '1');
      apiUrl.searchParams.append('lang', apiConfig.defaultLang);

      // Get the full URL string
      apiUrlString = apiUrl.toString();
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid API configuration' },
        { status: 500 }
      );
    }


    // Fetch data from API with proper error handling and retry mechanism
    let response;
    let responseText;
    let data;
    let retryCount = 0;
    const maxRetries = 5;
    let shouldRetry = false;

    do {
      if (retryCount > 0) {
        console.log(`[Product API] ${source} Retry attempt ${retryCount} of ${maxRetries}...`);
        // Add a small delay before retrying (increasing with each retry)
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
      }

      try {
        // Try using the URL object
        response = await fetch(apiUrlString, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
        });

      } catch (error) {
        // Try a fallback approach with direct string
        try {
          const directUrlString = `${apiConfig.baseUrl}${apiConfig.endpoints.itemGet}?key=${apiConfig.key}&secret=${apiConfig.secret}&num_iid=${productId}&is_promotion=1&lang=${apiConfig.defaultLang}`;

          response = await fetch(directUrlString, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
            },
          });

        } catch (fallbackError) {
          return NextResponse.json(
            { error: `Network error when connecting to ${source} API` },
            { status: 503 }
          );
        }
      }

      console.log(`[Product API] ${source} Response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        // Don't retry on HTTP errors, only on specific API error codes
        shouldRetry = false;
        return NextResponse.json(
          { error: `Failed to fetch product details: ${response.statusText}`, details: errorText },
          { status: response.status }
        );
      }

      // Get the raw response text for debugging
      responseText = await response.text();

      // Parse the response
      try {
        data = JSON.parse(responseText);

        // Check for error code 5000
        if (data.error_code === "5000") {
          shouldRetry = true;
          retryCount++;
          console.log(`[Product API] ${source} Received error code 5000, will retry (${retryCount}/${maxRetries})`);
        } else {
          shouldRetry = false;
        }

      } catch (error) {
        return NextResponse.json(
          { error: `Invalid JSON response from ${source} API` },
          { status: 502 }
        );
      }
    } while (shouldRetry && retryCount < maxRetries);

    // If we've exhausted all retries and still have error 5000
    if (data.error_code === "5000") {
      console.log(`[Product API] ${source} Failed after ${maxRetries} retries with error code 5000`);
      return NextResponse.json(
        { error: `Failed to fetch product details from ${source} API after ${maxRetries} retries: Error code 5000` },
        { status: 502 }
      );
    }

    // Check if the API returned an error
    if (data.error) {
      return NextResponse.json(
        { error: `${source} API error: ${data.error}` },
        { status: 502 }
      );
    }

    // Check for API specific error formats
    if (data.code && data.code !== 200) {
      return NextResponse.json(
        { error: `${source} API error: ${data.message || data.code}` },
        { status: 502 }
      );
    }

    // Check if we have the expected item structure
    if (!data.item) {
      return NextResponse.json(
        { error: 'Product not found or invalid response format' },
        { status: 404 }
      );
    }

    // Check if the item has the necessary properties
    const item = data.item;
    if (!item.num_iid || !item.title) {
      return NextResponse.json(
        { error: `Invalid product data returned from ${source} API` },
        { status: 502 }
      );
    }

    // Add source information to the data
    data.source = source;

    // Cache the result with the source-specific cache key
    productCache.set(cacheKey, { data, timestamp: now });

    // Save to database cache asynchronously
    await saveProductToCache(cacheKey, data, 3600 * 24 * 14)
      .then(success => {
        if (success) {
          console.log(`[Product API] Successfully saved ${cacheKey} to database cache`);
        } else {
          console.error(`[Product API] Failed to save ${cacheKey} to database cache`);
        }
      })
      .catch(error => {
        console.error(`[Product API] Error saving to database cache:`, error);
      });

    // Return the data
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch product details' },
      { status: 500 }
    );
  }
}
