import { NextResponse } from 'next/server';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Simple in-memory cache for translations
const translationCache: Record<string, { timestamp: number, translation: string }> = {};
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export async function POST(request: Request) {
  try {
    const { texts, targetLanguage } = await request.json();

    if (!texts || !Array.isArray(texts) || texts.length === 0) {
      return NextResponse.json(
        { error: 'Texts parameter is required and must be a non-empty array' },
        { status: 400 }
      );
    }

    if (!targetLanguage || typeof targetLanguage !== 'string') {
      return NextResponse.json(
        { error: 'Target language parameter is required and must be a string' },
        { status: 400 }
      );
    }

    // Skip translation if target language is Chinese
    if (targetLanguage === 'zh' || targetLanguage === 'zh-CN') {
      return NextResponse.json({ translations: texts });
    }

    // Check cache and filter out texts that need translation
    const now = Date.now();
    const translations: string[] = new Array(texts.length);
    const textsToTranslate: { index: number; text: string }[] = [];

    // Generate a request ID for logging
    const requestId = Math.random().toString(36).substring(2, 10);
    console.log(`[Translation API ${requestId}] Processing ${texts.length} texts for language: ${targetLanguage}`);

    // Count how many texts are cached vs. need translation
    let cachedCount = 0;
    let emptyCount = 0;

    texts.forEach((text, index) => {
      if (!text || typeof text !== 'string' || text.trim().length === 0) {
        translations[index] = text; // Keep empty or non-string values as is
        emptyCount++;
        return;
      }

      const cacheKey = `${text}:${targetLanguage}`;
      const cached = translationCache[cacheKey];

      if (cached && (now - cached.timestamp < CACHE_EXPIRY)) {
        translations[index] = cached.translation;
        cachedCount++;
      } else {
        textsToTranslate.push({ index, text });
      }
    });

    console.log(`[Translation API ${requestId}] Stats: ${cachedCount} cached, ${emptyCount} empty, ${textsToTranslate.length} to translate`);

    // If all texts are cached, return immediately
    if (textsToTranslate.length === 0) {
      console.log(`[Translation API ${requestId}] All texts cached, returning immediately`);
      return NextResponse.json({ translations });
    }

    // Prepare batch for translation
    const batchTexts = textsToTranslate.map(item => item.text);
    const batchText = batchTexts.join('\n---\n');

    try {
      console.log(`[Translation API ${requestId}] Translating ${textsToTranslate.length} texts to ${targetLanguage}`);

      // Log the first few texts being translated (for debugging)
      const textsToLog = batchTexts.slice(0, 3);
      console.log(`[Translation API ${requestId}] Sample texts to translate:`,
        textsToLog.map(t => t.length > 50 ? t.substring(0, 50) + '...' : t));

      // Create the messages for OpenAI
      const systemPrompt = `You are a professional translator specializing in e-commerce product descriptions.
                     Translate the following Chinese text to ${targetLanguage}.
                     Be concise and natural while preserving key product terminology and specifications.
                     Return ONLY the translations in the same order, separated by '---' on new lines.
                     Do not add any explanations or notes.`;

      const userPrompt = `请将以下中文翻译成${getLanguageName(targetLanguage)}，要求简洁自然并保留核心关键词和产品规格，不需要返回任何其他文字：\n\n${batchText}`;

      // Log the prompts (truncated for readability)
      console.log(`[Translation API ${requestId}] System prompt: ${systemPrompt.replace(/\s+/g, ' ').substring(0, 100)}...`);
      console.log(`[Translation API ${requestId}] User prompt (first 100 chars): ${userPrompt.substring(0, 100)}...`);
      console.log(`[Translation API ${requestId}] Total prompt length: ${userPrompt.length} characters`);

      const response = await openai.chat.completions.create({
        model: "gpt-4o-mini", // Use a smaller model for efficiency
        messages: [
          {
            role: "system",
            content: systemPrompt
          },
          {
            role: "user",
            content: userPrompt
          }
        ],
        temperature: 0.3,
      });

      const content = response.choices[0]?.message?.content || '';
      const batchTranslations = content.split('---').map(t => t.trim());

      console.log(`[Translation API ${requestId}] Received ${batchTranslations.length} translations from OpenAI`);

      // Check if we got the expected number of translations
      if (batchTranslations.length < textsToTranslate.length) {
        console.warn(`[Translation API ${requestId}] Warning: Received fewer translations (${batchTranslations.length}) than requested (${textsToTranslate.length})`);
      }

      // Update translations array and cache
      let cacheUpdates = 0;
      textsToTranslate.forEach((item, i) => {
        if (i < batchTranslations.length) {
          const translation = batchTranslations[i];
          translations[item.index] = translation;

          // Update cache
          const cacheKey = `${item.text}:${targetLanguage}`;
          translationCache[cacheKey] = {
            timestamp: now,
            translation
          };
          cacheUpdates++;
        } else {
          // If translation is missing, use original text
          translations[item.index] = item.text;
        }
      });

      console.log(`[Translation API ${requestId}] Completed: ${cacheUpdates} translations cached`);

      return NextResponse.json({ translations });
    } catch (openaiError: any) {
      console.error(`[Translation API ${requestId}] Translation failed:`, openaiError.message || openaiError);

      // For any error, fall back to original texts for untranslated items
      textsToTranslate.forEach(item => {
        if (!translations[item.index]) {
          translations[item.index] = item.text;
        }
      });

      // Check for specific OpenAI errors for logging
      if (openaiError.message && openaiError.message.includes('does not exist')) {
        console.error(`[Translation API ${requestId}] The selected AI model is not available`);
      } else if (openaiError.message && openaiError.message.includes('Rate limit')) {
        console.error(`[Translation API ${requestId}] Rate limit exceeded for OpenAI API`);
      } else if (openaiError.message && openaiError.message.includes('context_length_exceeded')) {
        console.error(`[Translation API ${requestId}] Context length exceeded - batch too large`);
      } else if (openaiError.message && openaiError.message.includes('invalid_api_key')) {
        console.error(`[Translation API ${requestId}] Invalid API key`);
      }

      console.log(`[Translation API ${requestId}] Returning ${translations.length} translations with fallbacks due to error`);

      // Return whatever translations we have, with original text as fallback
      return NextResponse.json({ translations });
    }
  } catch (error: any) {
    console.error(`[Translation API ${requestId}] Translation API error:`, error.message || error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to get language name
function getLanguageName(code: string): string {
  const languageMap: Record<string, string> = {
    'en': '英语',
    'fr': '法语',
    'es': '西班牙语',
    'de': '德语',
    'it': '意大利语',
    'ja': '日语',
    'ko': '韩语',
    'ru': '俄语',
    'pt': '葡萄牙语',
    'ar': '阿拉伯语',
    'hi': '印地语',
    'bn': '孟加拉语',
    'zh': '中文',
    'zh-CN': '中文',
  };

  return languageMap[code] || '英语'; // Default to English if language code not found
}
