import { NextRequest, NextResponse } from 'next/server';
import adminConfig from '../../../../../adminConfig.mjs';
import { uploadToS3 } from '@/lib/s3-upload';
import { saveUserSearch } from '@/models/userSearch';
import { getServerSession } from 'next-auth/next';
import { extractDisplayData } from '@/utils/search-utils';

// Cache for image search results
const imageSearchCache = new Map<string, { data: any, timestamp: number }>();

/**
 * POST handler for image-based search
 * @param request The incoming request with image file
 * @returns Response with search results
 */
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const imageFile = formData.get('image') as File | null;
    const imageUrl = formData.get('imageUrl') as string | null;

    // Check if we have either an image file or an image URL
    if (!imageFile && !imageUrl) {
      return NextResponse.json(
        { error: 'Either image file or image URL is required' },
        { status: 400 }
      );
    }

    // Create a cache key based on the input
    let cacheKey = '';
    if (imageFile) {
      cacheKey = `file_${imageFile.name}_${imageFile.size}`;
    } else if (imageUrl) {
      cacheKey = `url_${imageUrl}`;
    }

    // Check cache first
    const cachedResult = imageSearchCache.get(cacheKey);
    const now = Date.now();

    if (cachedResult && (now - cachedResult.timestamp) < adminConfig.taobaoApi.cache.itemSearch * 1000) {
      return NextResponse.json(cachedResult.data);
    }

    // Get the image URL to use with the Taobao API
    let taobaoImageUrl: string;

    // If an image URL is provided directly, use it
    if (imageUrl) {
      taobaoImageUrl = imageUrl;
    }
    // If an image file is provided, upload it to S3
    else if (imageFile) {
      try {
        taobaoImageUrl = await uploadToS3(imageFile);
      } catch (error) {
        // Fallback to a sample image URL if S3 upload fails
        taobaoImageUrl = 'https://img.alicdn.com/imgextra/i3/15353738/TB2HDHAqN9YBuNjy0FfXXXIsVXa_!!15353738-0-beehive-scenes.jpg';
      }
    }
    // If neither is provided (shouldn't happen due to earlier check), use a sample URL
    else {
      taobaoImageUrl = 'https://img.alicdn.com/imgextra/i3/15353738/TB2HDHAqN9YBuNjy0FfXXXIsVXa_!!15353738-0-beehive-scenes.jpg';
    }

    // Prepare API calls based on enabled configuration
    try {
      // Prepare variables for API results
      let taobaoData = { items: { total_results: '0', page: '1', pagecount: '1', item: [] } };
      let api1688Data = { items: { total_results: '0', page: '1', pagecount: '1', item: [] } };
      let alibabaData = { items: { total_results: '0', page: '1', pagecount: '1', item: [] } };
      const apiCalls = [];

      // Check if Taobao image search is enabled
      if (adminConfig.taobaoApi.enabled.itemSearchImg) {
        // Create Taobao API URL
        const taobaoApiUrl = new URL(`${adminConfig.taobaoApi.baseUrl}${adminConfig.taobaoApi.endpoints.itemSearchImg}`);

        // Add query parameters to Taobao API URL
        taobaoApiUrl.searchParams.append('key', adminConfig.taobaoApi.key);
        taobaoApiUrl.searchParams.append('secret', adminConfig.taobaoApi.secret);
        taobaoApiUrl.searchParams.append('imgid', taobaoImageUrl);
        taobaoApiUrl.searchParams.append('lang', adminConfig.taobaoApi.defaultLang);

        // Log the API URL
        console.log(`[IMAGE SEARCH API] Calling Taobao API: ${taobaoApiUrl.toString()}`);

        // Add Taobao API call to the list
        apiCalls.push(
          fetchImageSearchResults(taobaoApiUrl, taobaoImageUrl, 'Taobao')
            .then(data => { taobaoData = data; })
        );
      } else {
        console.log('[IMAGE SEARCH API] Taobao itemSearchImg API is disabled in configuration');
      }

      // Check if 1688 image search is enabled
      if (adminConfig.api1688.enabled.itemSearchImg) {
        // Create 1688 API URL
        const api1688Url = new URL(`${adminConfig.api1688.baseUrl}${adminConfig.api1688.endpoints.itemSearchImg}`);

        // Add query parameters to 1688 API URL
        api1688Url.searchParams.append('key', adminConfig.api1688.key);
        api1688Url.searchParams.append('secret', adminConfig.api1688.secret);
        api1688Url.searchParams.append('imgid', taobaoImageUrl);
        api1688Url.searchParams.append('lang', adminConfig.api1688.defaultLang);

        // Log the API URL
        console.log(`[IMAGE SEARCH API] Calling 1688 API: ${api1688Url.toString()}`);

        // Add 1688 API call to the list
        apiCalls.push(
          fetchImageSearchResults(api1688Url, taobaoImageUrl, '1688')
            .then(data => { api1688Data = data; })
        );
      } else {
        console.log('[IMAGE SEARCH API] 1688 itemSearchImg API is disabled in configuration');
      }

      // Check if Alibaba image search is enabled
      if (adminConfig.alibabaApi.enabled.itemSearchImg) {
        // Create Alibaba API URL
        const alibabaUrl = new URL(`${adminConfig.alibabaApi.baseUrl}${adminConfig.alibabaApi.endpoints.itemSearchImg}`);

        // Add query parameters to Alibaba API URL
        alibabaUrl.searchParams.append('key', adminConfig.alibabaApi.key);
        alibabaUrl.searchParams.append('secret', adminConfig.alibabaApi.secret);
        alibabaUrl.searchParams.append('imgid', taobaoImageUrl);
        alibabaUrl.searchParams.append('lang', adminConfig.alibabaApi.defaultLang);

        // Log the API URL
        console.log(`[IMAGE SEARCH API] Calling Alibaba API: ${alibabaUrl.toString()}`);

        // Add Alibaba API call to the list
        apiCalls.push(
          fetchImageSearchResults(alibabaUrl, taobaoImageUrl, 'Alibaba')
            .then(data => { alibabaData = data; })
        );
      } else {
        console.log('[IMAGE SEARCH API] Alibaba itemSearchImg API is disabled in configuration');
      }

      console.log(`[IMAGE SEARCH API] Image URL parameter: ${taobaoImageUrl}`);

      // Wait for all enabled API calls to complete
      await Promise.all(apiCalls);

      // Combine the results
      const combinedData = {
        taobao: taobaoData,
        api1688: api1688Data,
        alibaba: alibabaData,
        // Create a combined items structure for backward compatibility
        items: {
          total_results: (
            parseInt(taobaoData.items?.total_results || '0', 10) +
            parseInt(api1688Data.items?.total_results || '0', 10) +
            parseInt(alibabaData.items?.total_results || '0', 10)
          ).toString(),
          page: '1',
          pagecount: Math.max(
            parseInt(taobaoData.items?.pagecount || '1', 10),
            parseInt(api1688Data.items?.pagecount || '1', 10),
            parseInt(alibabaData.items?.pagecount || '1', 10)
          ).toString(),
          // Combine and mark items with their source
          item: [
            ...(taobaoData.items?.item || []).map((item: any) => ({
              ...item,
              source: 'taobao'
            })),
            ...(api1688Data.items?.item || []).map((item: any) => ({
              ...item,
              source: '1688'
            })),
            ...(alibabaData.items?.item || []).map((item: any) => ({
              ...item,
              source: 'alibaba'
            }))
          ]
        }
      };

      // Cache the combined result
      imageSearchCache.set(cacheKey, { data: combinedData, timestamp: now });

      // Get user information if available
      const session = await getServerSession();
      const userId = session?.user?.id;
      const userEmail = session?.user?.email;

      // Extract display data for trending section
      const displayData = extractDisplayData(combinedData);

      // Get the original query description if provided
      const originalQuery = formData.get('description') as string || 'Image search';

      // Save the search to the database
      await saveUserSearch({
        userId: userId || null,
        userEmail: userEmail || null,
        originalQuery,
        chineseKeywords: null, // No Chinese keywords for image search
        searchType: 'image',
        aiModel: null, // No AI model for image search
        imageUrl: taobaoImageUrl, // Save the S3 uploaded image URL
        resultCount: combinedData.items?.item?.length || 0,
        displayData
      });

      console.log(`[IMAGE SEARCH API] Combined results: ${combinedData.items.item.length} items (Taobao: ${taobaoData.items?.item?.length || 0}, 1688: ${api1688Data.items?.item?.length || 0}, Alibaba: ${alibabaData.items?.item?.length || 0})`);

      // Return the combined data
      return NextResponse.json(combinedData);
    } catch (error: any) {
      console.error(`[IMAGE SEARCH API] Error in combined image search:`, error);
      throw error; // Re-throw to be caught by the outer catch block
    }

    // Helper function to fetch image search results from an API
    async function fetchImageSearchResults(apiUrl: URL, imageUrl: string, apiName: string) {
      // Implement retry logic for error code 5000
      let response;
      let data;
      let retryCount = 0;
      const maxRetries = 5;
      let shouldRetry = false;

    do {
        if (retryCount > 0) {
          console.log(`[IMAGE SEARCH API] ${apiName} Retry attempt ${retryCount} of ${maxRetries}...`);
          // Add a small delay before retrying (increasing with each retry)
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }

        // Make the API request
        response = await fetch(apiUrl.toString(), {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
        });

        // Log response status and headers for debugging
        console.log(`[IMAGE SEARCH API] ${apiName} Response status: ${response.status}`);
        console.log(`[IMAGE SEARCH API] ${apiName} Response headers:`, Object.fromEntries([...response.headers.entries()]));

        if (!response.ok) {
          const errorText = await response.text();

          // Don't retry on HTTP errors, only on specific API error codes
          shouldRetry = false;

          // Return empty result structure instead of throwing
          return {
            error: `${apiName} API error: ${response.status} ${response.statusText}`,
            items: { total_results: '0', page: '1', pagecount: '1', item: [] }
          };
        }

        // Parse the response
        data = await response.json();

        // Log the complete response data for debugging
        console.log(`[IMAGE SEARCH API] ${apiName} Complete API response:`, JSON.stringify(data, null, 2));

        // Check for error code 5000
        if (data.error_code === "5000") {
          shouldRetry = true;
          retryCount++;
          console.log(`[IMAGE SEARCH API] ${apiName} Received error code 5000, will retry (${retryCount}/${maxRetries})`);
        } else {
          shouldRetry = false;
        }
      } while (shouldRetry && retryCount < maxRetries);

      // If we've exhausted all retries and still have error 5000
      if (data.error_code === "5000") {
        console.log(`[IMAGE SEARCH API] ${apiName} Failed after ${maxRetries} retries with error code 5000`);
        console.log(`[IMAGE SEARCH API] ${apiName} Error details:`, JSON.stringify(data, null, 2));

        // Return empty result structure instead of throwing
        return {
          error: `Failed to search ${apiName} products after ${maxRetries} retries: Error code 5000`,
          items: { total_results: '0', page: '1', pagecount: '1', item: [] }
        };
      }

      // Log the response data
      console.log(`[IMAGE SEARCH API] ${apiName} Response status: ${response.status}`);

      // Check if data has the expected structure
      if (data.items && data.items.item && Array.isArray(data.items.item)) {
        console.log(`[IMAGE SEARCH API] ${apiName} Response received: ${data.items.item.length} items found`);
        console.log(`[IMAGE SEARCH API] ${apiName} First few items:`, data.items.item.slice(0, 2));
      } else {
        console.log(`[IMAGE SEARCH API] ${apiName} Response structure:`, Object.keys(data));
        console.log(`[IMAGE SEARCH API] ${apiName} No items found or unexpected response format`);
      }

      return data;
    }
  } catch (error: any) {
    console.error(`[IMAGE SEARCH API] Error in image search API:`, error);

    // Log stack trace for better debugging
    if (error.stack) {
      console.error(`[IMAGE SEARCH API] Error stack trace:`, error.stack);
    }

    try {
      // Try to save the search to the database even if there was an error
      // Only if we have taobaoImageUrl defined
      if (typeof taobaoImageUrl !== 'undefined') {
        // Get user information if available
        const session = await getServerSession();
        const userId = session?.user?.id;
        const userEmail = session?.user?.email;

        // Get the original query description if provided
        const originalQuery = formData.get('description') as string || 'Image search';

        // Save the search to the database despite the error
        await saveUserSearch({
          userId: userId || null,
          userEmail: userEmail || null,
          originalQuery,
          chineseKeywords: null,
          searchType: 'image',
          aiModel: null,
          imageUrl: taobaoImageUrl, // Save the S3 uploaded image URL
          resultCount: 0,
          displayData: []
        });

        console.log(`[IMAGE SEARCH API] Search saved to database despite API exception: Image search "${originalQuery}" with image URL: ${taobaoImageUrl}`);
      } else {
        console.log(`[IMAGE SEARCH API] Could not save search to database: taobaoImageUrl is undefined`);
      }
    } catch (dbError) {
      console.error(`[IMAGE SEARCH API] Error saving to database:`, dbError);
    }

    return NextResponse.json(
      {
        error: error.message || 'Failed to search by image',
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
