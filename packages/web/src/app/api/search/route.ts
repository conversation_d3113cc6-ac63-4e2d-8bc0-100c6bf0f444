import { NextResponse } from 'next/server';
import { saveUserSearch } from '@/models/userSearch';
import { getServerSession } from 'next-auth/next';
import { extractDisplayData } from '@/utils/search-utils';
import adminConfig from '../../../../adminConfig.mjs';

// API endpoint for text-based search
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const page = searchParams.get('page') || '1';
    const pageSize = searchParams.get('pageSize') || '30';

    if (!query) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    // Check if the query contains Chinese characters
    const containsChinese = /[\u4e00-\u9fa5]/.test(query);
    console.log(`[API] Query contains Chinese characters: ${containsChinese}`);

    // If the query doesn't contain Chinese and is likely an English query that should have been translated,
    // log a warning (this helps with debugging)
    if (!containsChinese && query.length > 10) {
      console.warn(`[API] WARNING: Long query without Chinese characters. This might indicate that the AI translation was not used correctly.`);
    }

    // Get user information if available
    const session = await getServerSession();
    const userId = session?.user?.id;
    const userEmail = session?.user?.email;

    // Get the original query if it was translated by AI
    const originalQuery = searchParams.get('originalQuery') || query;
    const aiModel = searchParams.get('model') || null;

    // Prepare API calls based on enabled configuration
    const apiCalls = [];
    let taobaoData = { items: { total_results: '0', page: '1', pagecount: '1', item: [] } };
    let api1688Data = { items: { total_results: '0', page: '1', pagecount: '1', item: [] } };
    let alibabaData = { items: { total_results: '0', page: '1', pagecount: '1', item: [] } };

    // Add Taobao API call if enabled
    if (adminConfig.taobaoApi.enabled.itemSearch) {
      apiCalls.push(
        fetchFromAPI(query, page, pageSize, adminConfig.taobaoApi, 'Taobao')
          .then(data => { taobaoData = data; })
      );
    } else {
      console.log('[API] Taobao itemSearch API is disabled in configuration');
    }

    // Add 1688 API call if enabled
    if (adminConfig.api1688.enabled.itemSearch) {
      apiCalls.push(
        fetchFromAPI(query, page, pageSize, adminConfig.api1688, '1688')
          .then(data => { api1688Data = data; })
      );
    } else {
      console.log('[API] 1688 itemSearch API is disabled in configuration');
    }

    // Add Alibaba API call if enabled
    if (adminConfig.alibabaApi.enabled.itemSearch) {
      apiCalls.push(
        fetchFromAPI(query, page, pageSize, adminConfig.alibabaApi, 'Alibaba')
          .then(data => { alibabaData = data; })
      );
    } else {
      console.log('[API] Alibaba itemSearch API is disabled in configuration');
    }

    // Wait for all enabled API calls to complete
    await Promise.all(apiCalls);

    // Combine the results
    const combinedData = {
      taobao: taobaoData,
      api1688: api1688Data,
      alibaba: alibabaData,
      // Create a combined items structure for backward compatibility
      items: {
        total_results: (
          parseInt(taobaoData.items?.total_results || '0', 10) +
          parseInt(api1688Data.items?.total_results || '0', 10) +
          parseInt(alibabaData.items?.total_results || '0', 10)
        ).toString(),
        page: page,
        pagecount: Math.max(
          parseInt(taobaoData.items?.pagecount || '1', 10),
          parseInt(api1688Data.items?.pagecount || '1', 10),
          parseInt(alibabaData.items?.pagecount || '1', 10)
        ).toString(),
        // Combine and mark items with their source
        item: [
          ...(taobaoData.items?.item || []).map((item: any) => {
            console.log("[API] Setting source 'taobao' for item:", item.num_iid);
            return {
              ...item,
              source: 'taobao'
            };
          }),
          ...(api1688Data.items?.item || []).map((item: any) => {
            console.log("[API] Setting source '1688' for item:", item.num_iid);
            return {
              ...item,
              source: '1688'
            };
          }),
          ...(alibabaData.items?.item || []).map((item: any) => {
            console.log("[API] Setting source 'alibaba' for item:", item.num_iid);
            return {
              ...item,
              source: 'alibaba'
            };
          })
        ]
      }
    };

    // Extract display data for trending section from combined results
    const displayData = extractDisplayData(combinedData);

    // Save the search to the database
    await saveUserSearch({
      userId: userId || null,
      userEmail: userEmail || null,
      originalQuery,
      chineseKeywords: query,
      searchType: 'text',
      aiModel,
      resultCount: parseInt(combinedData.items?.total_results || '0', 10),
      displayData
    });

    console.log(`[API] Search saved to database: "${originalQuery}" -> "${query}"`);
    console.log(`[API] Combined results: ${combinedData.items.item.length} items (Taobao: ${taobaoData.items?.item?.length || 0}, 1688: ${api1688Data.items?.item?.length || 0}, Alibaba: ${alibabaData.items?.item?.length || 0})`);

    // Return the combined API response
    return NextResponse.json(combinedData);
  } catch (error: any) {
    console.error('Search API error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to search products' },
      { status: 500 }
    );
  }
}

// Helper function to fetch data from an API
async function fetchFromAPI(query: string, page: string, pageSize: string, apiConfig: any, apiName: string) {
  // Construct the API URL with parameters
  const apiUrl = new URL(`${apiConfig.baseUrl}${apiConfig.endpoints.itemSearch}`);
  apiUrl.searchParams.append('key', apiConfig.key);
  apiUrl.searchParams.append('q', query);
  apiUrl.searchParams.append('start_price', '0');
  apiUrl.searchParams.append('end_price', '0');
  apiUrl.searchParams.append('page', page);
  apiUrl.searchParams.append('page_size', pageSize);
  apiUrl.searchParams.append('cat', '0');
  apiUrl.searchParams.append('lang', apiConfig.defaultLang);
  apiUrl.searchParams.append('secret', apiConfig.secret);

  console.log(`[API] Searching ${apiName} for query: "${query}" (${encodeURIComponent(query)}), page: ${page}, pageSize: ${pageSize}`);
  console.log(`[API] ${apiName} Full URL: ${apiUrl.toString()}`);

  // Implement retry logic for error code 5000
  let response;
  let data;
  let retryCount = 0;
  const maxRetries = 5;
  let shouldRetry = false;

  do {
    if (retryCount > 0) {
      console.log(`[API] ${apiName} Retry attempt ${retryCount} of ${maxRetries}...`);
      // Add a small delay before retrying (increasing with each retry)
      await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
    }

    // Make the API request
    response = await fetch(apiUrl.toString());

    console.log(`[API] ${apiName} Response status: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[API] ${apiName} API error: ${response.status} ${errorText}`);

      // Don't retry on HTTP errors, only on specific API error codes
      shouldRetry = false;

      // Return empty result structure instead of throwing
      return {
        error: `${apiName} API error: ${response.status} ${response.statusText}`,
        items: { total_results: '0', page: '1', pagecount: '1', item: [] }
      };
    }

    // Parse the response
    data = await response.json();

    // Check for error code 5000
    if (data.error_code === "5000") {
      shouldRetry = true;
      retryCount++;
      console.log(`[API] ${apiName} Received error code 5000, will retry (${retryCount}/${maxRetries})`);
    } else {
      shouldRetry = false;
    }
  } while (shouldRetry && retryCount < maxRetries);

  // If we've exhausted all retries and still have error 5000
  if (data.error_code === "5000") {
    console.log(`[API] ${apiName} Failed after ${maxRetries} retries with error code 5000`);
    // Return empty result structure instead of throwing
    return {
      error: `Failed to search ${apiName} products after ${maxRetries} retries: Error code 5000`,
      items: { total_results: '0', page: '1', pagecount: '1', item: [] }
    };
  }

  return data;
}

// API endpoint for image-based search
export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const imageFile = formData.get('image') as File | null;

    if (!imageFile) {
      return NextResponse.json(
        { error: 'Image file is required' },
        { status: 400 }
      );
    }

    // For image search, we need to first upload the image somewhere and get a URL
    // Then use that URL with the Taobao API
    // This is a simplified implementation - in a real app, you'd handle image upload properly

    // Taobao API parameters
    const apiKey = 't3596163167';
    const secret = '3167863b';
    const baseUrl = 'https://api-gw.onebound.cn/taobao/item_search/';

    // Convert the image to a base64 string or upload it to get a URL
    // For now, we'll just return a mock response since image upload is complex

    console.log(`[API] Image search with file: ${imageFile.name}`);

    // In a real implementation, you would:
    // 1. Upload the image to a storage service
    // 2. Get the URL of the uploaded image
    // 3. Call the Taobao API with the image URL

    // For now, return a mock response
    return NextResponse.json(
      { error: 'Image search not fully implemented yet' },
      { status: 501 }
    );
  } catch (error: any) {
    console.error('Image search API error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to search by image' },
      { status: 500 }
    );
  }
}
