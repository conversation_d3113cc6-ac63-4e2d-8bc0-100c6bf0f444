import { NextResponse } from 'next/server';
import { analyzeProductRequirement, type ProductAnalysis } from '@/lib/openai';

// Handler for HEAD requests to check rate limits
export async function HEAD() {
  // The middleware will add rate limit headers to the response
  return new NextResponse(null, { status: 200 });
}

export async function POST(request: Request) {
  try {
    const { query, model } = await request.json();

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: 'Query parameter is required and must be a string' },
        { status: 400 }
      );
    }

    try {
      const analysis = await analyzeProductRequirement(query, model);
      return NextResponse.json(analysis);
    } catch (openaiError: any) {
      // Handle OpenAI API errors

      // Check for specific OpenAI errors
      if (openaiError.message && openaiError.message.includes('does not exist or you do not have access')) {
        return NextResponse.json(
          { error: 'The selected AI model is not available. Please try a different model or check your API key permissions.' },
          { status: 400 }
        );
      }

      if (openaiError.message && openaiError.message.includes('Rate limit')) {
        return NextResponse.json(
          { error: 'Rate limit exceeded for OpenAI API. Please try again later.' },
          { status: 429 }
        );
      }

      throw openaiError; // Re-throw to be caught by the outer catch
    }
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'Failed to analyze query' },
      { status: 500 }
    );
  }
}
