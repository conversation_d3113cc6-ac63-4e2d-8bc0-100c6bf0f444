import NextAuth, { Session, User, Account, AuthOptions } from "next-auth"; // Import AuthOptions, Session, Account
import { JWT } from "next-auth/jwt"; // Import JWT
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { connectToDatabase, Collections } from "@/lib/database";
import { findOrCreateUser } from "@/models/user"; // Import findOrCreateUser
import { ObjectId } from "mongodb"; // Import ObjectId for database queries
// Import any necessary adapter if you plan to use a database
// import { PrismaAdapter } from "@auth/prisma-adapter";
// import prisma from "@/lib/prisma"; // Assuming you have a prisma client setup

export const authOptions: AuthOptions = { // Add AuthOptions type annotation
  pages: {
    signOut: '/',
    signIn: '/login',
    error: '/login'
  },
  // adapter: PrismaAdapter(prisma), // Uncomment if using Prisma adapter
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    }),
    CredentialsProvider({
      // The name to display on the sign in form (e.g. "Sign in with...")
      name: "Email",
      // `credentials` is used to generate a form on the sign in page.
      // You can specify which fields should be submitted, by adding keys to the `credentials` object.
      // e.g. domain, username, password, 2FA token, etc.
      // You can pass any HTML attribute to the <input> tag through the object.
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        // We might not need a password field if using magic links/email code
        // password: { label: "Password", type: "password" }
      },
      async authorize(credentials): Promise<User | null> {
        // Add logic here to look up the user from the credentials supplied
        // This is where you'd typically verify the email/password or handle magic links/codes
        if (credentials?.email) {
          const { db } = await connectToDatabase();
          const user = await db.collection(Collections.USERS).findOne({
            email: credentials.email,
            isVerified: true // Only allow verified users to log in
          });

          if (user) {
            console.log("User authorized:", {
              id: user._id,
              email: user.email,
              isVerified: user.isVerified
            });

            // Construct the user object with name from firstName and lastName if available
            const name = user.firstName && user.lastName
              ? `${user.firstName} ${user.lastName}`
              : user.name || credentials.email.split('@')[0];

            return {
              id: user._id.toString(),
              name: name,
              email: user.email,
              firstName: user.firstName,
              lastName: user.lastName
            };
          }
          return null;
        }
        // If you return null then an error will be displayed advising the user to check their details.
        console.log("Authorization failed: No email provided");
        return null;
      }
    })
  ],
  // Add other NextAuth options here if needed:
  // secret: process.env.NEXTAUTH_SECRET,
  session: { strategy: "jwt" }, // Explicitly use JWT strategy
  callbacks: { // Uncommented and updated callbacks
    async jwt({ token, user, account }: { token: JWT; user?: User; account?: Account | null }) {
      // On initial sign-in (account and user are present)
      if (account && user?.email) { // Ensure user and email exist
        // Use findOrCreateUser to ensure a DB record exists
        const dbUser = await findOrCreateUser({
          email: user.email,
          name: user.name,
          image: user.image,
        });

        if (dbUser?._id) {
          // Always store the MongoDB _id in the token
          token.id = dbUser._id.toString();
          console.log(`JWT Callback: Stored DB ID ${token.id} for email ${user.email}`);
        } else {
          // Handle case where findOrCreateUser failed
          console.error(`JWT Callback: Could not find or create user for email ${user.email}.`);
        }
      }
      // Ensure token is always returned
      // For subsequent requests, token.id should ideally be present from the initial login.
      return token;
    },
    async session({ session, token }: { session: Session; token: JWT }) {
      // Add the id (MongoDB _id) from the token to the session object
      if (token.id && session.user) {
        session.user.id = token.id as string;

        // Fetch user from database to get the plan
        try {
          const { db } = await connectToDatabase();
          const user = await db.collection(Collections.USERS).findOne({ _id: new ObjectId(token.id as string) });
          if (user && user.plan) {
            // @ts-ignore - Add plan to session user
            session.user.plan = user.plan;
          } else {
            // @ts-ignore - Default to Free if no plan is found
            session.user.plan = 'Free';
          }
        } catch (error) {
          console.error('Error fetching user plan:', error);
          // @ts-ignore - Default to Free if there's an error
          session.user.plan = 'Free';
        }
      } else {
         console.warn("Session Callback: Token ID missing, cannot add to session user.");
      }
      return session;
    }
  } // End uncommented callbacks
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
