import { NextResponse } from 'next/server';
import { getUserByEmail, createUserWithVerification } from '@/models/user';
import { generateVerificationCode, sendVerificationEmail } from '@/lib/email';

export async function POST(request: Request) {
  try {
    const { email, firstName, lastName } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await getUserByEmail(email);
    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists' },
        { status: 400 }
      );
    }

    // Generate verification code
    const verificationCode = generateVerificationCode();

    // Create new user with verification code
    const newUser = await createUserWithVerification({
      email,
      firstName,
      lastName,
      verificationCode
    });

    if (!newUser) {
      return NextResponse.json(
        { error: 'Failed to create user' },
        { status: 500 }
      );
    }

    // Send verification email
    const emailResult = await sendVerificationEmail(email, verificationCode);
    if (!emailResult.success) {
      console.error('Failed to send verification email:', emailResult.error);
      return NextResponse.json(
        { error: 'Failed to send verification email' },
        { status: 500 }
      );
    }

    console.log('✅ User created:', newUser._id);

    return NextResponse.json(
      {
        id: newUser._id,
        email,
        firstName,
        lastName,
        message: 'User created. Verification code sent to email.'
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Signup error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
