import { NextResponse } from 'next/server';
import { connectToDatabase, Collections } from '@/lib/database';
import { ObjectId } from 'mongodb';
import { getUserByEmail } from '@/models/user';

export async function POST(request: Request) {
  try {
    const { email, firstName, lastName, isVerified } = await request.json();
    const { db } = await connectToDatabase();

    // Check if user already exists
    const existingUser = await getUserByEmail(email);
    if (existingUser) {
      return NextResponse.json(
        { message: 'User already exists', user: existingUser },
        { status: 200 }
      );
    }

    // Create new user
    const newUser = {
      _id: new ObjectId(),
      email,
      firstName,
      lastName,
      name: firstName && lastName ? `${firstName} ${lastName}` : null,
      plan: 'Free', // Set default plan to Free for new users
      isVerified: isVerified || false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await db.collection(Collections.USERS).insertOne(newUser);
    console.log('✅ Test user created:', result.insertedId);

    return NextResponse.json(
      { id: result.insertedId, email, firstName, lastName, isVerified },
      { status: 201 }
    );
  } catch (error) {
    console.error('Create test user error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
