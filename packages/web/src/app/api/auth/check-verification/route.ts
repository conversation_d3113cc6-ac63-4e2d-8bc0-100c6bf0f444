import { NextResponse } from 'next/server';
import { getUserByEmail, setVerificationCode } from '@/models/user';
import { generateVerificationCode, sendVerificationEmail } from '@/lib/email';

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Check if user exists
    const user = await getUserByEmail(email);
    if (!user) {
      // For security reasons, don't reveal if the user exists or not
      return NextResponse.json(
        { exists: false, isVerified: false },
        { status: 200 }
      );
    }

    // We'll always send a verification code, even if the email is verified
    // Just keep track of the verification status for reference

    // If not verified, generate a new verification code
    const code = generateVerificationCode();

    // Save the code to the user record
    const codeSet = await setVerificationCode(email, code);
    if (!codeSet) {
      return NextResponse.json(
        { error: 'Failed to set verification code' },
        { status: 500 }
      );
    }

    // Send the verification email
    const emailResult = await sendVerificationEmail(email, code);
    if (!emailResult.success) {
      console.error('Failed to send verification email:', emailResult.error);
      return NextResponse.json(
        { error: 'Failed to send verification email' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        exists: true,
        isVerified: user.isVerified || false,
        message: 'Verification code sent'
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Check verification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
