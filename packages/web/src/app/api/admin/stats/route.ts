import { NextResponse } from 'next/server';
import { checkAdminAccess } from '@/lib/admin-auth';
import { getDashboardStats } from '@/lib/admin-logging';

export async function GET() {
  // Check admin access
  const { isAdmin, error } = await checkAdminAccess();
  if (!isAdmin && error) {
    return error;
  }

  try {
    const stats = await getDashboardStats();
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch statistics' },
      { status: 500 }
    );
  }
}
