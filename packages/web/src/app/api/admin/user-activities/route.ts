import { NextResponse } from 'next/server';
import { checkAdminAccess } from '@/lib/admin-auth';
import { getUserActivityLogs } from '@/lib/admin-logging';

export async function GET(request: Request) {
  // Check admin access
  const { isAdmin, error } = await checkAdminAccess();
  if (!isAdmin && error) {
    return error;
  }

  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const activityType = searchParams.get('activityType') || undefined;
    const userId = searchParams.get('userId') || undefined;
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;

    const filters = {
      activityType,
      userId,
      startDate,
      endDate
    };

    // Remove undefined values
    Object.keys(filters).forEach(key => {
      if (filters[key as keyof typeof filters] === undefined) {
        delete filters[key as keyof typeof filters];
      }
    });

    const result = await getUserActivityLogs(page, limit, filters);
    
    return NextResponse.json({
      logs: result.logs,
      total: result.total,
      page,
      limit,
      totalPages: Math.ceil(result.total / limit)
    });
  } catch (error) {
    console.error('Error fetching user activity logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user activity logs' },
      { status: 500 }
    );
  }
}
