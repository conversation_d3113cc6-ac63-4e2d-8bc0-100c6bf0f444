import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { jwtVerify } from 'jose';

// JWT secret for admin sessions
const JWT_SECRET = new TextEncoder().encode(
  process.env.NEXTAUTH_SECRET || 'admin-secret-key-change-in-production'
);

export async function GET() {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get('admin-token')?.value;

    if (!token) {
      return NextResponse.json({
        authenticated: false,
        user: null
      });
    }

    // Verify JWT token
    const { payload } = await jwtVerify(token, JWT_SECRET);

    return NextResponse.json({
      authenticated: true,
      user: {
        username: payload.username,
        role: payload.role,
        loginTime: payload.loginTime
      }
    });

  } catch (error) {
    console.error('Admin session verification error:', error);

    // Clear invalid token
    const cookieStore = await cookies();
    cookieStore.delete('admin-token');

    return NextResponse.json({
      authenticated: false,
      user: null
    });
  }
}
