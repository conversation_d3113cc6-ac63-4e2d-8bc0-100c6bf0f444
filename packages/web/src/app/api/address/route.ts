import { NextResponse } from 'next/server';
import { getAddressesByUser, createAddress, updateAddress } from '@/models/address';
import { ObjectId } from 'mongodb';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get('userId');
  
  if (!userId) {
    return NextResponse.json(
      { error: 'userId query parameter is required' },
      { status: 400 }
    );
  }


  try {
    const addresses = await getAddressesByUser(userId);
    return NextResponse.json(addresses);
  } catch (error) {
    console.error('[API GET /api/address] Failed to fetch addresses:', error); // Add context to log
    return NextResponse.json(
      { error: 'Failed to fetch addresses' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const addressData = await request.json();
    const newAddress = await createAddress({
      ...addressData,
      userId: new ObjectId(addressData.userId)
    });
    return NextResponse.json(newAddress, { status: 201 });
  } catch (error) {
    console.error('Failed to create address:', error);
    return NextResponse.json(
      { error: 'Failed to create address' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    // Destructure id (string) and _id (ObjectId, if present) from the body,
    // keeping the rest in addressData. This prevents _id from being passed to $set.
    const { id, _id, ...addressData } = await request.json(); 
    
    if (!id) {
      return NextResponse.json({ error: 'Address ID is required for update' }, { status: 400 });
    }

    // Pass only the actual update data (addressData) to the model function
    const updatedAddress = await updateAddress(id, addressData); 
    
    // The updateAddress function returns the MongoDB result, not the updated document directly.
    // Check if the update was successful.
    if (updatedAddress.matchedCount === 0) {
       return NextResponse.json({ error: 'Address not found' }, { status: 404 });
    }
    if (updatedAddress.modifiedCount === 0) {
       // Optionally return 304 Not Modified or just success if no change needed
       return NextResponse.json({ message: 'Address not modified (no changes detected)' }); 
    }

    // Return success message or potentially the fetched updated document if needed
    return NextResponse.json({ message: 'Address updated successfully' });
  } catch (error) {
    console.error('Failed to update address:', error);
    return NextResponse.json(
      { error: 'Failed to update address' },
      { status: 500 }
    );
  }
}
