import { NextResponse } from 'next/server';
import Strip<PERSON> from 'stripe';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { ObjectId } from 'mongodb'; // Keep for userId/addressId conversion if needed locally, though createOrder handles it now
import { createOrder } from '@/models/order'; // Import only the function
import { type Order, type OrderItem, type ShippingInfo, type PaymentInfo, OrderStatus } from '@/types/order-shared'; // Import types/enum from shared file
// Removed tempCheckout import

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: Request) {
  // Get user session server-side
  const authSession = await getServerSession(authOptions); // Renamed to authSession

  if (!authSession || !authSession.user?.id) {
    return NextResponse.json({ error: 'Unauthorized: User not logged in' }, { status: 401 });
  }
  const userId = authSession.user.id; // Use authSession

  try {
    // Ensure customerEmail matches session email for security, or just use session email
    const { items, shipping /*, customerEmail */ } = await request.json();
    const customerEmail: string | null | undefined = authSession.user.email; // Use authSession and add type

    if (!customerEmail) {
      // Handle case where email might be missing from session
      return NextResponse.json({ error: 'User email not found in session' }, { status: 400 });
    }

    if (!shipping?.addressId) {
       return NextResponse.json({ error: 'Shipping address ID is required' }, { status: 400 });
    }
    const addressId = shipping.addressId; // Assuming this is already an ObjectId string
    const shippingMethod = shipping.method || 'Standard';

    // --- Create Order in DB Before Stripe ---
    const orderItemsToSave: OrderItem[] = items.map((item: any) => {
       if (!item.productId || !item.sku || !item.title || !item.price || !item.quantity) {
        throw new Error(`Missing required fields for item: ${JSON.stringify(item)}`);
      }
      return {
        productId: item.productId, // Store as string (num_iid)
        sku: item.sku,
        title: item.title,
        price: item.price, // Use the price sent from frontend
        quantity: item.quantity,
        imageUrl: item.imageUrl,
      };
    });

    // Calculate totals based on items received (ensure consistency)
    // Note: Price conversion (like * 0.14 USD) should ideally happen *before* this point
    // or be handled consistently. Assuming item.price is the final price per item in desired currency.
    const subtotal = orderItemsToSave.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const shippingCost = 0; // Shipping costs are not included as they are just for simulation
    const tax = 0; // Placeholder for tax calculation if needed
    const total = subtotal + tax; // Shipping cost is excluded

    // Prepare order data using string IDs as defined in the Order type
    const orderDataToCreate: Omit<Order, '_id' | 'createdAt' | 'updatedAt' | 'status'> = {
      userId: userId, // Pass userId as string
      items: orderItemsToSave,
      shipping: {
        addressId: addressId, // Pass addressId as string
        method: shippingMethod,
      },
      payment: { // Initial payment info
        method: 'stripe', // Will be confirmed by webhook
        amount: total,
        currency: 'eur', // Use EUR currency to match frontend and Stripe
        status: 'pending', // Initial status
      },
      subtotal: subtotal,
      shippingCost: shippingCost,
      tax: tax,
      total: total,
    };

    const newOrderId = await createOrder(orderDataToCreate);
    console.log(`Created preliminary order ${newOrderId} with status PENDING.`);
    // --- End Order Creation ---


    // Create line items for Stripe checkout
    const lineItems = items.map((item: any) => {
      if (!item.productId || !item.sku || !item.title || !item.price || !item.quantity) {
        // Add validation for required item fields
        throw new Error(`Missing required fields for item: ${JSON.stringify(item)}`);
      }
      return {
        price_data: {
          currency: 'eur', // Use EUR currency to match frontend display
          product_data: { // Keep basic product info
            name: item.title,
            images: item.imageUrl ? [item.imageUrl] : [],
            // Removed metadata from product_data
          },
          unit_amount: Math.round(item.price * 100), // Convert to cents
          // Removed metadata from price_data
        },
        quantity: item.quantity,
      };
    });

    // Shipping costs are not included in the checkout as they are just for simulation
    // The actual shipping costs will be calculated and charged separately

    const stripeSession = await stripe.checkout.sessions.create({ // Renamed to stripeSession
      payment_method_types: ['card'],
      line_items: lineItems,
      mode: 'payment',
      // Redirect to my-orders page on success
      success_url: `${process.env.NEXTAUTH_URL}/my-orders?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXTAUTH_URL}/checkout`,
      customer_email: customerEmail, // Use email from session
      metadata: { // Pass Order ID to webhook
        orderId: newOrderId.toString(), // Pass the created MongoDB Order ID
        // userId: userId, // Optional: Keep if needed for other webhook logic
        // addressId: addressId, // Optional: Keep if needed
      },
    });

    // Removed temporary data saving logic

    return NextResponse.json({ id: stripeSession.id }); // Return session ID to frontend
  } catch (err) {
    console.error('Stripe checkout error:', err);
    return NextResponse.json(
      { error: 'Error creating checkout session' },
      { status: 500 }
    );
  }
}
