import { NextResponse } from 'next/server';
import { createOrder, getOrdersByUser } from '@/models/order'; // Import getOrdersByUser
import { ObjectId } from 'mongodb';
import { getServerSession } from "next-auth/next"; // Import getServerSession
import { authOptions } from "@/app/api/auth/[...nextauth]/route"; // Import authOptions
import { connectToDatabase, Collections } from '@/lib/database'; // Import connectToDatabase and Collections

// GET handler to fetch orders for the logged-in user
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Get user ID from session
    const userId = session.user.id;

    // Connect to database directly to check raw data
    const { db } = await connectToDatabase();
    const ordersCollection = db.collection(Collections.ORDERS);

    // Get orders for the current user
    const orders = await ordersCollection.find({ userId: new ObjectId(userId) }).toArray();

    // Log raw orders for debugging
    console.log('Raw orders from database for user:', userId, JSON.stringify(orders.map(order => ({
      _id: order._id.toString(),
      status: order.status,
      combined: order.combined || false,
      combinedGroupId: order.combinedGroupId || null
    })), null, 2));

    // Convert ObjectId to string for JSON serialization
    const serializedOrders = orders.map(order => ({
      ...order,
      _id: order._id.toString(),
      userId: order.userId.toString(),
      // Ensure combined fields are included
      combined: order.combined || false,
      combinedGroupId: order.combinedGroupId || null,
      combinedAt: order.combinedAt || null
    }));

    return NextResponse.json(serializedOrders);
  } catch (error) {
    console.error('Failed to fetch orders:', error);
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}


// POST handler (existing - might be redundant now?)
export async function POST(request: Request) {
  try {
    const orderData = await request.json();

    // Convert string IDs to ObjectId
    const orderWithObjectIds = {
      ...orderData,
      userId: new ObjectId(orderData.userId),
      // Note: productId is now a string in the model, no need to convert here
      // items: orderData.items.map((item: any) => ({
      //   ...item,
      //   productId: new ObjectId(item.productId) // This was incorrect based on new model
      // })),
      items: orderData.items, // Pass items directly if productId is already string
      shipping: {
        ...orderData.shipping,
        addressId: new ObjectId(orderData.shipping.addressId)
      }
    };

    const orderId = await createOrder(orderWithObjectIds);
    return NextResponse.json({ orderId }, { status: 201 });
  } catch (error) {
    console.error('Failed to create order:', error);
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    );
  }
}
