import { NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { ObjectId } from 'mongodb';
import { connectToDatabase, Collections } from '@/lib/database';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: Request) {
  // Get user session server-side
  const authSession = await getServerSession(authOptions);

  if (!authSession || !authSession.user?.id) {
    return NextResponse.json({ error: 'Unauthorized: User not logged in' }, { status: 401 });
  }
  const userId = authSession.user.id;
  const customerEmail = authSession.user.email;

  if (!customerEmail) {
    return NextResponse.json({ error: 'User email not found in session' }, { status: 400 });
  }

  try {
    const { orderId } = await request.json();

    if (!orderId) {
      return NextResponse.json({ error: 'Order ID is required' }, { status: 400 });
    }

    // Connect to database
    const { db } = await connectToDatabase();
    const ordersCollection = db.collection(Collections.ORDERS);

    // Find the order
    const order = await ordersCollection.findOne({
      _id: new ObjectId(orderId),
      userId: new ObjectId(userId)
    });

    if (!order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 });
    }

    // Verify order status is PENDING
    if (order.status !== 'pending') {
      return NextResponse.json({ error: 'Order is not in pending status' }, { status: 400 });
    }

    // Create line items for Stripe checkout
    const lineItems = order.items.map((item: any) => {
      return {
        price_data: {
          currency: 'eur', // Use EUR currency to match frontend display
          product_data: {
            name: item.title,
            images: item.imageUrl ? [item.imageUrl] : [],
          },
          unit_amount: Math.round(item.price * 100), // Convert to cents
        },
        quantity: item.quantity,
      };
    });

    // Add shipping as a line item if cost is available
    if (order.shippingCost > 0) {
      lineItems.push({
        price_data: {
          currency: 'eur', // Use EUR currency to match frontend display
          product_data: {
            name: `Shipping (${order.shipping.method || 'Standard'})`,
          },
          unit_amount: Math.round(order.shippingCost * 100),
        },
        quantity: 1,
      });
    }

    const stripeSession = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: lineItems,
      mode: 'payment',
      success_url: `${process.env.NEXTAUTH_URL}/my-orders?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXTAUTH_URL}/my-orders`,
      customer_email: customerEmail,
      metadata: {
        orderId: orderId,
      },
    });

    return NextResponse.json({ id: stripeSession.id });
  } catch (err) {
    console.error('Stripe repayment error:', err);
    return NextResponse.json(
      { error: 'Error creating repayment session' },
      { status: 500 }
    );
  }
}
