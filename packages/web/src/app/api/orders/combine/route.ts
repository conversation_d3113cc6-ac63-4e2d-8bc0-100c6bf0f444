import { NextResponse } from 'next/server';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { connectToDatabase, Collections } from '@/lib/database';
import { ObjectId } from 'mongodb';
import { OrderStatus } from '@/types/order-shared';

export async function POST(request: Request) {
  // Get user session server-side
  const authSession = await getServerSession(authOptions);

  if (!authSession || !authSession.user?.id) {
    return NextResponse.json({ error: 'Unauthorized: User not logged in' }, { status: 401 });
  }
  const userId = authSession.user.id;

  try {
    const { orderIds } = await request.json();

    if (!orderIds || !Array.isArray(orderIds) || orderIds.length < 2) {
      return NextResponse.json({ error: 'At least two order IDs are required' }, { status: 400 });
    }

    // Connect to the database
    const { db } = await connectToDatabase();
    const ordersCollection = db.collection(Collections.ORDERS);

    // Verify all orders belong to the user and are in PENDING_TRANSFER status
    const objectIdOrderIds = orderIds.map(id => new ObjectId(id));

    // First check if all orders exist and belong to the user
    // Convert userId to ObjectId since it's stored as ObjectId in the database
    const userObjectId = new ObjectId(userId);

    // Log for debugging
    console.log(`Checking orders for user: ${userId} (ObjectId: ${userObjectId})`);
    console.log(`Order IDs to combine:`, orderIds);

    const userOrders = await ordersCollection.find({
      _id: { $in: objectIdOrderIds },
      userId: userObjectId
    }).toArray();

    // Log found orders for debugging
    console.log(`Found ${userOrders.length} orders out of ${orderIds.length} requested`);
    if (userOrders.length < orderIds.length) {
      // Find which orders were not found
      const foundOrderIds = userOrders.map(order => order._id.toString());
      const missingOrderIds = orderIds.filter(id => !foundOrderIds.includes(id));
      console.log(`Missing orders:`, missingOrderIds);

      // Check if these orders exist at all
      const existingOrders = await ordersCollection.find({
        _id: { $in: missingOrderIds.map(id => new ObjectId(id)) }
      }).toArray();

      if (existingOrders.length > 0) {
        console.log(`Orders exist but belong to different users:`,
          existingOrders.map(o => ({ id: o._id.toString(), userId: o.userId.toString() })));
      } else {
        console.log(`Orders do not exist in the database:`, missingOrderIds);
      }
    }

    if (userOrders.length !== orderIds.length) {
      // Find which orders were not found for a more detailed error message
      const foundOrderIds = userOrders.map(order => order._id.toString());
      const missingOrderIds = orderIds.filter(id => !foundOrderIds.includes(id));

      return NextResponse.json({
        error: `One or more orders do not exist or do not belong to the current user: ${missingOrderIds.join(', ')}`,
        message: `一个或多个订单不存在或不属于当前用户: ${missingOrderIds.join(', ')}`,
        missingOrderIds: missingOrderIds
      }, { status: 400 });
    }

    // Use OrderStatus enum for status check

    // Then check if all orders are in PENDING_TRANSFER status
    const pendingTransferOrders = userOrders.filter(order => order.status === OrderStatus.PENDING_TRANSFER);
    console.log(`Orders in PENDING_TRANSFER status: ${pendingTransferOrders.length} out of ${orderIds.length}`);
    console.log('Order statuses:', userOrders.map(order => ({ id: order._id.toString(), status: order.status })));

    if (pendingTransferOrders.length !== orderIds.length) {
      const nonPendingOrders = userOrders.filter(order => order.status !== OrderStatus.PENDING_TRANSFER);
      console.log('Orders not in PENDING_TRANSFER status:', nonPendingOrders.map(o => ({ id: o._id.toString(), status: o.status })));

      return NextResponse.json({
        error: 'One or more orders are not in PENDING_TRANSFER status',
        message: '只能合并待转运状态的订单，请重新选择',
        orderStatuses: userOrders.map(order => ({ id: order._id.toString(), status: order.status }))
      }, { status: 400 });
    }

    // Check if any orders are already combined
    const alreadyCombinedOrders = userOrders.filter(order => order.combined);
    console.log(`Already combined orders: ${alreadyCombinedOrders.length} out of ${orderIds.length}`);
    console.log('Combined status:', userOrders.map(order => ({ id: order._id.toString(), combined: order.combined || false })));

    if (alreadyCombinedOrders.length > 0) {
      console.log('Orders already combined:', alreadyCombinedOrders.map(o => ({ id: o._id.toString() })));

      return NextResponse.json({
        error: 'One or more orders are already combined',
        message: '已经合箱的订单不能再次合箱，请重新选择',
        combinedOrders: alreadyCombinedOrders.map(order => order._id.toString())
      }, { status: 400 });
    }

    // Generate a unique group ID for this combination
    const combinedGroupId = new ObjectId().toString();

    // Update all orders to mark them as combined with the same group ID
    const updateResult = await ordersCollection.updateMany(
      { _id: { $in: objectIdOrderIds } },
      { $set: {
        combined: true,
        combinedAt: new Date(),
        combinedGroupId: combinedGroupId
      } }
    );

    if (updateResult.modifiedCount !== orderIds.length) {
      return NextResponse.json({ error: 'Failed to update all orders' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: `Successfully combined ${orderIds.length} orders`,
      combinedOrders: orderIds,
      combinedGroupId: combinedGroupId
    });
  } catch (err) {
    console.error('Error combining orders:', err);
    return NextResponse.json(
      { error: 'Error combining orders' },
      { status: 500 }
    );
  }
}
