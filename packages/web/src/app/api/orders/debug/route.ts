import { NextResponse } from 'next/server';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { connectToDatabase, Collections } from '@/lib/database';

// GET handler to fetch all orders for debugging purposes
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { db } = await connectToDatabase();
    const ordersCollection = db.collection(Collections.ORDERS);

    // Get all orders
    const allOrders = await ordersCollection.find({}).toArray();

    // Log raw orders for debugging
    console.log('Raw orders from database:', JSON.stringify(allOrders.map(order => ({
      _id: order._id.toString(),
      status: order.status,
      combined: order.combined || false,
      combinedGroupId: order.combinedGroupId || null
    })), null, 2));

    // Map orders to a more readable format
    const mappedOrders = allOrders.map(order => ({
      _id: order._id.toString(),
      userId: order.userId.toString(),
      status: order.status,
      combined: order.combined || false,
      combinedGroupId: order.combinedGroupId || null,
      combinedAt: order.combinedAt || null,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      items: order.items.map(item => ({
        title: item.title,
        quantity: item.quantity,
        price: item.price
      }))
    }));

    return NextResponse.json({
      totalOrders: mappedOrders.length,
      orders: mappedOrders
    });
  } catch (error) {
    console.error('Failed to fetch orders for debugging:', error);
    return NextResponse.json(
      { error: 'Failed to fetch orders for debugging' },
      { status: 500 }
    );
  }
}
