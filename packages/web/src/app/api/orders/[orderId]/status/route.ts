import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { updateOrderStatus } from '@/models/order';
import { OrderStatus } from '@/types/order-shared';

// PATCH /api/orders/[orderId]/status - Update order status
export async function PATCH(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    console.log(`Received request to update order status for ID: ${params.orderId}`);
    
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      console.log('Authentication failed: No user session');
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    console.log(`Authenticated user: ${session.user.email}`);

    // Get order ID from URL params
    const orderId = params.orderId;
    if (!orderId) {
      return NextResponse.json(
        { message: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { status } = body;
    console.log(`Requested status update: ${status}`);

    // Validate status
    if (!status || !Object.values(OrderStatus).includes(status)) {
      console.log(`Invalid status value: ${status}`);
      console.log(`Valid status values: ${Object.values(OrderStatus).join(', ')}`);
      return NextResponse.json(
        { message: 'Invalid status value' },
        { status: 400 }
      );
    }
    
    console.log(`Status value is valid: ${status}`);

    // Update order status
    console.log(`Calling updateOrderStatus with orderId: ${orderId}, status: ${status}`);
    const result = await updateOrderStatus(orderId, status);
    console.log(`Update result:`, result);
    
    if (result.modifiedCount === 0) {
      console.log(`No document was modified. Order might not exist or status is already ${status}`);
      return NextResponse.json(
        { message: 'Order not found or status not changed' },
        { status: 404 }
      );
    }

    console.log(`Order status updated successfully to ${status}`);
    return NextResponse.json(
      { message: 'Order status updated successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error updating order status:', error);
    return NextResponse.json(
      { message: 'Failed to update order status' },
      { status: 500 }
    );
  }
}
