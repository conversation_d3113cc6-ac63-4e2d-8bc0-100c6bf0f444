import { NextResponse } from 'next/server';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { ObjectId } from 'mongodb';
import { connectToDatabase, Collections } from '@/lib/database';

// Define context type for params
interface Context {
  params: {
    orderId: string;
  };
}

export async function DELETE(request: Request, context: Context) {
  const { orderId } = context.params;

  // 1. Verify Authentication
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }
  const userId = session.user.id;

  // 2. Validate Order ID
  if (!orderId || !ObjectId.isValid(orderId)) {
    return NextResponse.json({ message: 'Invalid Order ID format' }, { status: 400 });
  }

  try {
    const { db } = await connectToDatabase();

    // 3. Attempt to delete the order, ensuring it belongs to the logged-in user
    const result = await db.collection(Collections.ORDERS).deleteOne({
      _id: new ObjectId(orderId),
      userId: new ObjectId(userId), // Match both order ID and user ID
    });

    // 4. Check if an order was actually deleted
    if (result.deletedCount === 0) {
      // This means either the order doesn't exist or it doesn't belong to this user
      return NextResponse.json({ message: 'Order not found or user mismatch' }, { status: 404 });
    }

    // 5. Return success response
    console.log(`Order ${orderId} deleted successfully by user ${userId}`);
    return NextResponse.json({ message: 'Order deleted successfully' }, { status: 200 });

  } catch (error) {
    console.error(`Failed to delete order ${orderId}:`, error);
    return NextResponse.json({ message: 'Internal Server Error deleting order' }, { status: 500 });
  }
}

// Optional: Add GET handler if needed to fetch a single order by ID
// export async function GET(request: Request, context: Context) { ... }
