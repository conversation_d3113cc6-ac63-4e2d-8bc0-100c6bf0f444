import { NextResponse } from 'next/server';
import Stripe from 'stripe';
import { ObjectId } from 'mongodb';
import { connectToDatabase, Collections } from '@/lib/database';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);
const webhookSecret = process.env.STRIPE_SUBSCRIPTION_WEBHOOK_SECRET || process.env.STRIPE_WEBHOOK_SECRET;

// Disable Next.js body parsing for this route to access the raw body
export const config = {
  api: {
    bodyParser: false,
  },
};

export async function POST(req: Request) {
  if (!webhookSecret) {
    console.error('Stripe webhook secret is not set.');
    return NextResponse.json({ error: 'Webhook secret not configured' }, { status: 500 });
  }

  const sig = req.headers.get('stripe-signature');
  // Read the raw request body as text
  const rawBody = await req.text();

  let event: Stripe.Event;

  try {
    // Use the raw text body for verification
    event = stripe.webhooks.constructEvent(rawBody, sig!, webhookSecret);
  } catch (err: any) {
    console.error(`Webhook signature verification failed: ${err.message}`);
    return NextResponse.json({ error: `Webhook Error: ${err.message}` }, { status: 400 });
  }

  // Handle the event
  switch (event.type) {
    case 'checkout.session.completed':
      const session = event.data.object as Stripe.Checkout.Session;
      console.log('Handling subscription checkout.session.completed for session:', session.id);

      try {
        // Get user ID from metadata
        const userId = session.metadata?.userId;

        if (!userId) {
          console.error(`CRITICAL: User ID not found in metadata for completed session ${session.id}.`);
          return NextResponse.json({ error: `Webhook handler error: Missing userId in metadata` }, { status: 500 });
        }

        if (!ObjectId.isValid(userId)) {
          console.error(`CRITICAL: Invalid User ID format (${userId}) in metadata for completed session ${session.id}.`);
          return NextResponse.json({ error: `Webhook handler error: Invalid userId format` }, { status: 400 });
        }

        // Update user's plan to Pro
        const { db } = await connectToDatabase();
        const result = await db.collection(Collections.USERS).updateOne(
          { _id: new ObjectId(userId) },
          { $set: { plan: 'Pro', updatedAt: new Date() } }
        );

        if (result.modifiedCount === 0) {
          console.error(`Webhook handler: User ${userId} not found or plan already updated for session ${session.id}.`);
          return NextResponse.json({ error: `User ${userId} not found.` }, { status: 404 });
        }

        console.log(`Successfully updated user ${userId} plan to Pro for session ${session.id}`);

      } catch (error: any) {
        console.error(`Error processing subscription checkout session ${session.id}:`, error);
        return NextResponse.json({ error: `Webhook handler error: ${error.message}` }, { status: 500 });
      }
      break;

    case 'customer.subscription.created':
      const subscription = event.data.object as Stripe.Subscription;
      console.log('Subscription created:', subscription.id);
      // Additional handling for subscription creation if needed
      break;

    case 'customer.subscription.updated':
      const updatedSubscription = event.data.object as Stripe.Subscription;
      console.log('Subscription updated:', updatedSubscription.id);
      // Handle subscription updates (e.g., plan changes, payment method updates)
      break;

    case 'customer.subscription.deleted':
      const deletedSubscription = event.data.object as Stripe.Subscription;
      console.log('Subscription cancelled:', deletedSubscription.id);
      
      try {
        // Get customer ID from the subscription
        const customerId = deletedSubscription.customer as string;
        
        // Find the user with this Stripe customer ID
        // Note: You would need to store the Stripe customer ID in your user records
        // This is a simplified example - you might need to adjust based on your data model
        const { db } = await connectToDatabase();
        const user = await db.collection(Collections.USERS).findOne({ 
          stripeCustomerId: customerId 
        });
        
        if (user) {
          // Update user's plan back to Free
          await db.collection(Collections.USERS).updateOne(
            { _id: user._id },
            { $set: { plan: 'Free', updatedAt: new Date() } }
          );
          console.log(`Successfully downgraded user ${user._id} plan to Free after subscription cancellation`);
        } else {
          console.error(`Could not find user with Stripe customer ID: ${customerId}`);
        }
      } catch (error: any) {
        console.error(`Error processing subscription cancellation:`, error);
      }
      break;

    default:
      console.warn(`Unhandled event type ${event.type}`);
  }

  // Return a 200 response to acknowledge receipt of the event
  return NextResponse.json({ received: true });
}
