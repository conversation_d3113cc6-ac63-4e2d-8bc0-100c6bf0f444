import { NextResponse } from 'next/server';
import Stripe from 'stripe';
// Removed micro import
import { updateOrderStatus } from '@/models/order'; // Import only the function
import { OrderStatus, type PaymentInfo } from '@/types/order-shared'; // Import types/enum from shared file
import { ObjectId } from 'mongodb';
import { logStripeEvent } from '@/lib/admin-logging';
// Removed connectToDatabase import (assuming updateOrderStatus handles it)
// Removed tempCheckout imports

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Disable Next.js body parsing for this route to access the raw body
export const config = {
  api: {
    bodyParser: false,
  },
};

export async function POST(req: Request) {
  if (!webhookSecret) {
    console.error('Stripe webhook secret is not set.');
    return NextResponse.json({ error: 'Webhook secret not configured' }, { status: 500 });
  }

  const sig = req.headers.get('stripe-signature');
  // Read the raw request body as text
  const rawBody = await req.text();

  let event: Stripe.Event;

  try {
    // Use the raw text body for verification
    event = stripe.webhooks.constructEvent(rawBody, sig!, webhookSecret);
  } catch (err: any) {
    console.error(`Webhook signature verification failed: ${err.message}`);
    return NextResponse.json({ error: `Webhook Error: ${err.message}` }, { status: 400 });
  }

  // Handle the event
  switch (event.type) {
    case 'checkout.session.completed':
      const session = event.data.object as Stripe.Checkout.Session;
      console.log('Handling checkout.session.completed for session:', session.id);

      try {
        // Retrieve the full session object with line items
        const sessionWithLineItems = await stripe.checkout.sessions.retrieve(
          session.id,
          { expand: ['line_items'] }
        );

        if (!sessionWithLineItems.line_items) {
          throw new Error('Line items not found in session');
        }

        // --- Get Order ID from Metadata ---
        const orderId = session.metadata?.orderId;

        if (!orderId) {
          console.error(`CRITICAL: Order ID not found in metadata for completed session ${session.id}. Cannot update status.`);
          // Return 500 to indicate server error, Stripe might retry.
          return NextResponse.json({ error: `Webhook handler error: Missing orderId in metadata for session ${session.id}` }, { status: 500 });
        }

        if (!ObjectId.isValid(orderId)) {
           console.error(`CRITICAL: Invalid Order ID format (${orderId}) in metadata for completed session ${session.id}.`);
           return NextResponse.json({ error: `Webhook handler error: Invalid orderId format for session ${session.id}` }, { status: 400 }); // Bad request
        }

        // --- Update Order Status ---
        // Determine the new status based on payment status
        const newStatus = session.payment_status === 'paid' ? OrderStatus.PROCESSING : OrderStatus.PENDING; // Or maybe a specific 'PAYMENT_FAILED' status?
        const paymentStatus: PaymentInfo['status'] = session.payment_status === 'paid' ? 'completed' : 'failed'; // Update payment status as well

        // TODO: Update payment info as well if needed (e.g., transaction ID, final status)
        // This might require fetching the order first, then updating specific fields.
        // For simplicity now, just update the main order status.

        const updateResult = await updateOrderStatus(orderId, newStatus);

        // Check if the order was actually modified (implies it was found)
        if (updateResult.modifiedCount === 0) {
             console.error(`Webhook handler: Order ${orderId} not found or status already updated for session ${session.id}.`);

             // Log failed Stripe event
             await logStripeEvent({
               eventType: event.type,
               stripeEventId: event.id,
               status: 'failed',
               amount: session.amount_total || 0,
               currency: session.currency || 'usd',
               orderId,
               sessionId: session.id,
               errorMessage: `Order ${orderId} not found or already updated`,
               metadata: { session }
             }).catch(logError => {
               console.error('Failed to log Stripe event:', logError);
             });

             // Return 404 as the order wasn't found or didn't need updating.
             return NextResponse.json({ error: `Order ${orderId} not found.` }, { status: 404 });
        }

        console.log(`Successfully updated order ${orderId} status to ${newStatus} for session ${session.id}`);

        // Log successful Stripe event
        await logStripeEvent({
          eventType: event.type,
          stripeEventId: event.id,
          status: session.payment_status === 'paid' ? 'success' : 'failed',
          amount: session.amount_total || 0,
          currency: session.currency || 'usd',
          orderId,
          sessionId: session.id,
          metadata: {
            session,
            orderStatus: newStatus,
            paymentStatus
          }
        }).catch(logError => {
          console.error('Failed to log Stripe event:', logError);
        });

        // --- TODO: Trigger Cart Clearing ---
        // Since this is server-side, we can't directly modify client-side cart state.
        // Options:
        // 1. Emit a server-sent event (SSE) or WebSocket message to the specific user.
        // 2. Have the client poll an endpoint after returning to /my-orders to check if cart should be cleared.
        // 3. (Less ideal) Store a flag in the user's session/DB indicating cart needs clearing, checked on next page load.
        if (newStatus === OrderStatus.PROCESSING) {
             console.log(`Placeholder: Trigger cart clearing for user associated with order ${orderId}`);
             // Example (conceptual): await triggerCartClear(userId); // userId would need to be fetched or passed in metadata
        }

      } catch (error: any) {
        console.error(`Error processing checkout session ${session.id}:`, error);
        // Optionally notify admin or retry logic
        return NextResponse.json({ error: `Webhook handler error: ${error.message}` }, { status: 500 });
      }
      break;

    // ... handle other event types if needed
    // case 'payment_intent.succeeded':
    //   const paymentIntent = event.data.object;
    //   // Handle successful payment intent
    //   break;

    default:
      console.warn(`Unhandled event type ${event.type}`);
  }

  // Return a 200 response to acknowledge receipt of the event
  return NextResponse.json({ received: true });
}
