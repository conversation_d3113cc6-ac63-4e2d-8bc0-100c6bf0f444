import { NextRequest, NextResponse } from 'next/server';
import { clearExpiredCache } from '@/models/productCache';

// Track the last time we cleared the cache
let lastCacheClearTime = 0;
const CACHE_CLEAR_INTERVAL = 3600000; // 1 hour in milliseconds

/**
 * POST handler to clear expired cache entries
 * @returns Response with the number of cleared entries
 */
export async function POST(_request: NextRequest) {
  try {
    // Clear expired cache entries
    const clearedCount = await clearExpiredCache();

    // Update the last clear time
    lastCacheClearTime = Date.now();

    return NextResponse.json({
      success: true,
      clearedCount,
      message: `Successfully cleared ${clearedCount} expired cache entries`,
      lastClearTime: new Date(lastCacheClearTime).toISOString()
    });
  } catch (error) {
    console.error('Error clearing expired cache:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to clear expired cache entries'
      },
      { status: 500 }
    );
  }
}

/**
 * GET handler to check cache status and trigger automatic cleanup if needed
 * @returns Response with cache status
 */
export async function GET(_request: NextRequest) {
  try {
    const now = Date.now();
    const timeSinceLastClear = now - lastCacheClearTime;
    let clearedCount = 0;
    let automaticCleanup = false;

    // If it's been more than the interval since the last clear, do it automatically
    if (timeSinceLastClear > CACHE_CLEAR_INTERVAL) {
      clearedCount = await clearExpiredCache();
      lastCacheClearTime = now;
      automaticCleanup = true;
    }

    return NextResponse.json({
      success: true,
      lastClearTime: lastCacheClearTime ? new Date(lastCacheClearTime).toISOString() : null,
      timeSinceLastClear: timeSinceLastClear,
      nextScheduledClear: lastCacheClearTime ? new Date(lastCacheClearTime + CACHE_CLEAR_INTERVAL).toISOString() : null,
      automaticCleanup,
      clearedCount: automaticCleanup ? clearedCount : 0
    });
  } catch (error) {
    console.error('Error checking cache status:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check cache status'
      },
      { status: 500 }
    );
  }
}
