"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
// Navbar is included in the page layout
import { PageLayout } from "@/components/shared/PageLayout";
import { Search, X, ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";

export default function HistoryPage() {
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const tCommon = useTranslations(I18NNamespace.COMMON);

  useEffect(() => {
    // Load searches from localStorage on the client side
    const savedSearches = localStorage.getItem('recentSearches');
    if (savedSearches) {
      setRecentSearches(JSON.parse(savedSearches));
    }
  }, []);

  const clearHistory = () => {
    localStorage.setItem('recentSearches', JSON.stringify([]));
    setRecentSearches([]);
    // Dispatch storage event to update sidebar
    window.dispatchEvent(new Event('storage'));
  };

  const removeSearch = (index: number, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const updatedSearches = [...recentSearches];
    updatedSearches.splice(index, 1);
    setRecentSearches(updatedSearches);
    localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
    // Dispatch storage event to update sidebar
    window.dispatchEvent(new Event('storage'));
  };

  return (
    <div className="min-h-screen bg-[#FAFAFA] history-page-wrapper">
      <PageLayout>
        <div className="pt-20 px-4 sm:px-6 lg:px-8 py-8 history-container">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-semibold">{tCommon("SEARCH_HISTORY")}</h1>
            {recentSearches.length > 0 && (
              <Button variant="outline" onClick={clearHistory} className="text-sm">
                {tCommon("CLEAR_ALL")}
              </Button>
            )}
          </div>

          <p className="text-gray-600 mb-4">{recentSearches.length} {tCommon("SEARCHES")}</p>

          {recentSearches.length > 0 ? (
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 history-list-wrapper"> {/* Added list wrapper class */}
              <ul className="space-y-3">
                {recentSearches.map((search, index) => (
                  <li key={index} className="relative group">
                    <Link
                      href={`/?q=${encodeURIComponent(search)}&showResults=true`} // Link back to home page with params
                      className="flex items-center justify-between text-gray-700 hover:text-accio-primary hover:bg-gray-50 p-3 rounded-md transition-colors text-sm"
                    >
                      <div className="flex items-center">
                        <Search className="w-4 h-4 mr-3 text-gray-500" />
                        <span>{search}</span>
                      </div>
                      <button
                        onClick={(e) => removeSearch(index, e)}
                        className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-200 rounded-full"
                        aria-label="Remove search"
                      >
                        <X className="w-4 h-4 text-gray-500" />
                      </button>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <div className="text-center py-10 bg-white rounded-lg shadow-sm border border-gray-100">
              <p className="text-gray-500 mb-4">{tCommon("NO_SEARCH_HISTORY")}</p>
              <Link href="/">
                <Button>{tCommon("START_SEARCHING")}</Button>
              </Link>
            </div>
          )}
        </div>
      </PageLayout>
    </div>
  );
}
