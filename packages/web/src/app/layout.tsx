import type { <PERSON>ada<PERSON> } from "next";
import { Noto_Sans } from "next/font/google";
import "./globals.css";
import "./fonts.css"; // Import fallback font CSS
import ClientBody from "./ClientBody";
import AuthSessionProvider from "@/components/providers/AuthSessionProvider"; // Import NextAuth provider
import { AuthProvider } from "@/lib/auth-context";
import { ComparisonProvider } from "@/lib/comparison-context";
import { CartProvider } from "@/lib/cart-context"; // Import CartProvider
import { WeChatProvider } from "@/lib/wechat-context"; // Import WeChatProvider
import { ComparisonCard } from "@/components/shared/ComparisonCard";
import { CartPanel } from "@/components/shared/CartPanel"; // Import CartPanel
import { WeChatPanel } from "@/components/shared/WeChatPanel"; // Import WeChatPanel
import { SidebarProvider } from "@/components/ui/sidebar";

import MainContentWrapper from "./MainContentWrapper"; // Import the new wrapper

const notoSans = Noto_Sans({
  subsets: ["latin"],
  weight: ["400", "600", "700"],
  variable: "--font-noto-sans",
  display: "swap",
  fallback: ["system-ui", "Arial", "sans-serif"],
});

// Define viewport export separately as recommended by Next.js
export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

export const metadata: Metadata = {
  title: "Miccobuy - Global Professional Sourcing",
  description: "Find and buy any products from global suppliers with AI-powered sourcing solutions. Source from Taobao, 1688, Amazon, and Aliexpress.",
  keywords: "global sourcing, product sourcing, AI sourcing, Taobao, 1688, Amazon, Aliexpress, international shipping",
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || "https://miccobuy.com"),
  icons: {
    icon: [
      { url: "/favicon.ico" },
      { url: "/images/miccobuy-logo.png" },
      { url: "/favicon.svg", type: "image/svg+xml" },
      { url: "/favicon-transparent.svg", type: "image/svg+xml", media: "(prefers-color-scheme: light)" }
    ],
    apple: { url: "/images/miccobuy-logo.png" },
    shortcut: { url: "/favicon.ico" }
  },
  openGraph: {
    title: "Miccobuy - Global Professional Sourcing",
    description: "Find and buy any products from global suppliers with AI-powered sourcing solutions.",
    url: "https://miccobuy.com",
    siteName: "Miccobuy",
    images: [
      {
        url: "/images/miccobuy-og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Miccobuy - Global Professional Sourcing"
      }
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Miccobuy - Global Professional Sourcing",
    description: "Find and buy any products from global suppliers with AI-powered sourcing solutions.",
    images: ["/images/miccobuy-og-image.jpg"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${notoSans.variable} font-fallback`}>
      <ClientBody>
        {/* Wrap everything with AuthSessionProvider for NextAuth */}
        <AuthSessionProvider>
          <AuthProvider>
            <ComparisonProvider>
              <CartProvider> {/* Wrap with CartProvider */}
                <WeChatProvider> {/* Wrap with WeChatProvider */}
                  {/* SidebarProvider wraps everything that needs access to the sidebar context */}
                  <SidebarProvider defaultOpen={true}>
                    {/* MainContentWrapper applies conditional padding */}
                    <MainContentWrapper>
                      {children}
                    </MainContentWrapper>
                  </SidebarProvider>
                </WeChatProvider> {/* Close WeChatProvider */}
              </CartProvider> {/* Close CartProvider */}
            </ComparisonProvider>
          </AuthProvider>
        </AuthSessionProvider>
      </ClientBody>
    </html>
  );
}
