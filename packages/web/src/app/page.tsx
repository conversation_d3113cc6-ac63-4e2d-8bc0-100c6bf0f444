"use client";

// Navbar removed
import { AppSidebar } from "@/components/shared/Sidebar"; // Restore AppSidebar import
import { FloatingMenu } from "@/components/shared/FloatingMenu"; // Import FloatingMenu
import { SearchBox } from "@/components/shared/SearchBox";
import { CategoryTabs } from "@/components/shared/CategoryTabs";
import { FeatureCards } from "@/components/shared/FeatureCards";
import { PartnerLogos } from "@/components/shared/PartnerLogos";
import { Footer } from "@/components/shared/Footer";
import { HeroBackdrop } from "@/components/shared/HeroBackdrop";
import { ProductGrid } from "@/components/shared/ProductGrid";
import { Testimonial } from "@/components/shared/Testimonial";
import { AboutSection } from "@/components/shared/AboutSection";
import { PartnershipSection } from "@/components/shared/PartnershipSection";
import { ServiceExplanation } from "@/components/shared/ServiceExplanation";
import { ProcessSteps } from "@/components/shared/ProcessSteps";
import { SearchResults } from "@/components/search/SearchResults";
import { SearchResultsSkeleton } from "@/components/search/SearchResultsSkeleton";
import { SearchFilters } from "@/components/search/SearchFilters";
import { SearchTabs } from "@/components/search/SearchTabs";
import { CompareButton } from "@/components/search/CompareButton";
import { AnalysisPanel } from "@/components/search/AIAnalysisPanel";
import { ImageSearchLoadingPanel } from "@/components/search/ImageSearchLoadingPanel";
import { ImageSearchErrorDialog } from "@/components/search/ImageSearchErrorDialog";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ChevronDown } from "lucide-react";
import React, { useState, useEffect, Suspense, useRef } from "react"; // Import React and hooks
import { useRouter, useSearchParams } from 'next/navigation'; // Import useRouter, useSearchParams
import Link from 'next/link'; // Import Link component
import { useSidebar } from "@/components/ui/sidebar"; // Import useSidebar
// Import Image removed as it's not needed
// Removed MockLogin import
import { CompareFloatingButton } from "@/components/comparison/CompareButton";
import ComparePanel from "@/components/comparison/ComparePanel";
import textSearchAPIResult from "./textSearchAPIResult.json";
import picSearchAPIResult from "./picSearchAPIResult.json";
import { I18NNamespace, useTranslations } from "@/hooks/useTranslations";
import { checkIsProductAnalysis, ProductAnalysis } from "@/lib/openai";
import { debugLog } from "@/utils/debug";

// Enhanced cache management functions with memory cache and longer expiry
const searchCache = {
  // In-memory cache for faster access
  memoryCache: new Map<string, { data: any, timestamp: number, expiry: number }>(),

  // Get cached search results (checks both memory and localStorage)
  get: (query: string, page: number, isImageSearch: boolean = false): any | null => {
    try {
      // Create a cache key based on the search parameters
      const cacheKey = `search_${isImageSearch ? 'image' : 'text'}_${query.toLowerCase().trim()}_page${page}`;
      const now = new Date().getTime();

      // First check memory cache (faster)
      const memoryCached = searchCache.memoryCache.get(cacheKey);
      if (memoryCached) {
        // Check if memory cache has expired
        if (memoryCached.expiry > now) {
          return memoryCached.data;
        } else {
          // Remove expired item from memory cache
          searchCache.memoryCache.delete(cacheKey);
        }
      }

      // Then check localStorage
      const cachedData = sessionStorage.getItem(cacheKey);
      if (!cachedData) return null;

      const { data, timestamp, expiry } = JSON.parse(cachedData);

      // Check if the cache has expired
      if (expiry && now > expiry) {
        sessionStorage.removeItem(cacheKey);
        return null;
      }

      // Store in memory cache for faster future access
      searchCache.memoryCache.set(cacheKey, { data, timestamp, expiry });

      return data;
    } catch (error) {
      return null;
    }
  },

  // Save search results to cache (both memory and localStorage)
  set: (query: string, page: number, data: any, isImageSearch: boolean = false, expiryMinutes: number = 60): void => {
    try {
      // Create a cache key based on the search parameters
      const cacheKey = `search_${isImageSearch ? 'image' : 'text'}_${query.toLowerCase().trim()}_page${page}`;

      // Calculate expiry time (increased to 60 minutes default)
      const now = new Date().getTime();
      const expiry = now + (expiryMinutes * 60 * 1000);

      // Create the cache object
      const cacheObject = {
        data,
        timestamp: now,
        expiry,
        query,
        page
      };

      // Save to memory cache
      searchCache.memoryCache.set(cacheKey, { data, timestamp: now, expiry });

      // Save to localStorage
      sessionStorage.setItem(cacheKey, JSON.stringify(cacheObject));

      // Limit memory cache size to 100 items
      if (searchCache.memoryCache.size > 100) {
        // Remove oldest entry
        const oldestKey = searchCache.memoryCache.keys().next().value;
        if (oldestKey) {
          searchCache.memoryCache.delete(oldestKey);
        }
      }
    } catch (error) {
      console.error("Error saving to cache:", error);
    }
  },

  // Clear all search caches
  clear: (): void => {
    try {
      // Clear memory cache
      searchCache.memoryCache.clear();

      // Get all keys from localStorage
      const keys = Object.keys(localStorage);

      // Filter out search cache keys
      const searchKeys = keys.filter(key => key.startsWith('search_'));

      // Remove all search cache items
      searchKeys.forEach(key => sessionStorage.removeItem(key));
    } catch (error) {
      console.error("Error clearing cache:", error);
    }
  }
};

// Wrap the component logic in a separate component to use Suspense
function HomePageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { state, toggleSidebar } = useSidebar();
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [userHasToggled, setUserHasToggled] = useState(false);
  const t = useTranslations(I18NNamespace.PAGES_HOME);
  const tCommon = useTranslations(I18NNamespace.COMMON);
  // Initialize state from URL params or defaults
  const initialQuery = searchParams.get('q') || "";
  const initialShowResults = searchParams.get('showResults') === 'true';
  // Note: Restoring image state from URL is complex, skipping for now.
  // const initialSearchType = searchParams.get('searchType');

  const [showSearchResults, setShowSearchResults] = useState(initialShowResults);
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [activeTab, setActiveTab] = useState<'product' | 'business'>('product');
  const [showAnalysisDetails, setShowAnalysisDetails] = useState(false);
  const [image, setImage] = useState<File | null>(null); // Image state remains local
  const [selectedModel, setSelectedModel] = useState("GPT-4o mini (free)");
  const [searchResults, setSearchResults] = useState<any>(null); // Add state for search results
  const [isLoading, setIsLoading] = useState(false); // Add loading state
  const [currentSearchPage, setCurrentSearchPage] = useState(1); // Add state for current search page
  const [isImageSearching, setIsImageSearching] = useState(false); // Track image search specifically
  const [imageSearchError, setImageSearchError] = useState(false); // Track image search errors
  const [analysisResult, setAnalysisResult] = useState<ProductAnalysis | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  // Ref to track if a search is currently being performed
  // This prevents URL changes from triggering a new search while one is already in progress
  const isPerformingSearch = useRef(false);

  // Add responsive behavior to detect small screens
  useEffect(() => {
    const handleResize = () => {
      const smallScreen = window.innerWidth < 800;
      setIsSmallScreen(smallScreen);
    };

    // Set initial state
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Effect to auto-resize textarea when content changes
  useEffect(() => {
    if (textareaRef.current && searchQuery) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.max(48, textareaRef.current.scrollHeight)}px`;
    }
  }, [searchQuery]);


  console.log("initialQuery: ", initialQuery, "  searchQuery: ", searchQuery)
  // Effect to update state if URL params change (e.g., browser back/forward)
  // Calling fetchSearchResults
  useEffect(() => {
    // Skip if we're currently performing a search
    // This prevents URL changes from triggering a new search while one is already in progress
    if (isPerformingSearch.current) {
      debugLog('Skipping URL-triggered search because a search is already in progress');
      return;
    }

    const query = searchParams.get('q') || "";
    const showResults = searchParams.get('showResults') === 'true';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const searchType = searchParams.get('searchType');
    const fromImageSearch = searchParams.get('fromImageSearch') === 'true';
    const timestamp = searchParams.get('ts');

    if (!query || !showResults) return;

    // Check if we have a Chinese query parameter
    const chineseQuery = searchParams.get('cn');

    // Update state based on URL params
    setSearchQuery(query);
    setShowSearchResults(showResults);
    setCurrentSearchPage(page);

    // Get model from URL params
    const modelParam = searchParams.get('model');
    if (modelParam) {
      setSelectedModel(modelParam);
    }

    // Check if we're returning from an image search detail page
    if (fromImageSearch) {
      try {
        const restoreImageSearch = localStorage.getItem('restoreImageSearch') === 'true';
        const storedTimestamp = localStorage.getItem('restoreImageSearchTimestamp');

        // Verify the timestamp matches to ensure we're handling the correct navigation
        if (restoreImageSearch && storedTimestamp === timestamp) {

          // Try to get the saved image search results
          const savedResults = localStorage.getItem('lastImageSearchResults');
          if (savedResults) {
            const parsedResults = JSON.parse(savedResults);
            if (parsedResults.data) {
              setSearchResults(parsedResults.data);
              setIsLoading(false);

              // Clear the restore flags
              localStorage.removeItem('restoreImageSearch');
              localStorage.removeItem('restoreImageSearchTimestamp');

              // Set image search type
              if (searchType === 'image') {
                setImage({} as File); // Set a dummy file to indicate image search
              }

              return; // Skip the regular search flow
            }
          }
        }
      } catch (error) {
        console.error("Error restoring image search:", error);
      }
    }

    // Reset image state if navigating back/forward without an image search context
    if (searchType !== 'image') {
      setImage(null);
    }

    // trigger a search
    isPerformingSearch.current = true;
    setIsLoading(true);

    // Use a small timeout to ensure state updates have been applied
    setTimeout(async () => {
      try {
        // If we have a Chinese query parameter, use it instead of the original query
        const searchQuery = chineseQuery || query;
        debugLog(`URL-triggered search using query: "${searchQuery}" (Chinese: ${chineseQuery ? 'yes' : 'no'})`);

        const results = await fetchSearchResults(searchQuery, page);
        setSearchResults(results);
      } catch (error) {
        debugLog('Error during URL-triggered search:', error);
      } finally {
        setIsLoading(false);
        debugLog('URL-triggered search using query FINALLY');
        isPerformingSearch.current = false;
      }
    }, 10);
  }, [initialQuery]);

  // Function to handle search with pagination
  const fetchSearchResults = async (query: string, page: number = 1, imageFile?: File, originalQuery?: string, aiModel?: string) => {
    // Avoid setting state directly in this function to prevent re-renders
    // We'll return the data and let the caller decide what to do with it
    const isImageSearch = !!imageFile;

    // Check if the query contains Chinese characters
    const containsChinese = /[\u4e00-\u9fa5]/.test(query);

    debugLog(`Starting fetchSearchResults: query="${query}", containsChinese=${containsChinese}, page=${page}, hasImage=${isImageSearch}, aiModel=${aiModel || 'none'}`);

    // If the query doesn't contain Chinese and is likely an English query that should have been translated,
    // log a warning (this helps with debugging)

    try {
      // Check cache first if we have a query
      if (query && !isImageSearch) {
        const cachedData = searchCache.get(originalQuery || query, page);
        if (cachedData) {
          return cachedData;
        }
        debugLog('No cache found, fetching from API');
      }

      let searchData;

      if (imageFile) {
        debugLog('Performing image search');
        // Set image search loading state
        setIsImageSearching(true);
        setImageSearchError(false);

        // For image search, we need to upload the image first
        const formData = new FormData();
        formData.append('image', imageFile);

        // Call the image search API endpoint
        const response = await fetch('/api/search/image', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          setIsImageSearching(false);
          setImageSearchError(true);
          throw new Error(`Image search failed: ${response.statusText}`);
        }

        searchData = await response.json();

        // Save the API response to localStorage for debugging and for restoring when returning from product detail
        try {
          // Save for restoring when returning from product detail
          localStorage.setItem('lastImageSearchResults', JSON.stringify({
            data: searchData,
            timestamp: new Date().getTime(),
            searchType: 'image'
          }));
          debugLog('Image search results saved to localStorage for restoration');
        } catch (e) {
          console.warn('Could not save search response to localStorage:', e);
        }

        // Check for error code 5000 after max retries
        if (searchData.error && searchData.error.includes('after 5 retries: Error code 5000')) {
          setImageSearchError(true);
          debugLog('Image search failed after max retries with error code 5000');

          // Log the complete Taobao response for debugging if available
          if (searchData.taobaoResponse) {
            console.log('[CLIENT] Taobao API error response:', searchData.taobaoResponse);
          }
        }

        // If the API isn't fully implemented yet, fall back to mock data
        if (searchData.error) {
          debugLog('Using mock data for image search:', searchData.error);
          searchData = picSearchAPIResult;
        }

        // Clear image search loading state
        setIsImageSearching(false);
      } else {
        debugLog(`Performing text search: "${query}", page ${page}`);

        // Check if the query contains Chinese characters
        const containsChinese = /[\u4e00-\u9fa5]/.test(query);
        if (!containsChinese && query.length > 10) {
          debugLog(`WARNING: Query does not contain Chinese characters: "${query}"`);
          debugLog(`This might indicate that the AI translation was not used correctly.`);

          // Try to get the Chinese keyword from localStorage as a fallback
          try {
            const lastChineseKeyword = localStorage.getItem('lastChineseKeyword');
            if (lastChineseKeyword) {
              debugLog(`Found Chinese keyword in localStorage: "${lastChineseKeyword}"`);
              debugLog(`Consider using this instead if appropriate.`);
            }
          } catch (e) {
            // Ignore localStorage errors
          }
        } else if (containsChinese) {
          debugLog(`GOOD: Query contains Chinese characters: "${query}"`);
        }

        // For text search, call the API endpoint with the query and page
        // Make sure we're using the correct query (which might be the Chinese translation)
        let apiUrl = `/api/search?q=${encodeURIComponent(query)}&page=${page}`;

        // Add original query and AI model if available
        if (originalQuery) {
          apiUrl += `&originalQuery=${encodeURIComponent(originalQuery)}`;
        }
        if (aiModel) {
          apiUrl += `&model=${encodeURIComponent(aiModel)}`;
        }

        debugLog('API URL:', apiUrl);

        const response = await fetch(apiUrl);
        debugLog('Text search response status:', response.status);

        if (!response.ok) {
          throw new Error(`Text search failed: ${response.statusText}`);
        }

        searchData = await response.json();
        debugLog('Text search API response:', searchData);

        // Log some information about the results
        if (searchData.items) {
          debugLog(`Received ${searchData.items.item?.length || 0} items, page ${searchData.items.page || 1} of ${searchData.items.pagecount || 1}, total results: ${searchData.items.total_results || 0}`);
        }

        // Cache the search results
        if (query && searchData && !searchData.error) {
          searchCache.set(originalQuery || query, page, searchData);
        }
      }

      debugLog('Search completed successfully');
      return searchData;
    } catch (error) {
      debugLog('Search error:', error);

      // Set error state for image search
      if (imageFile) {
        setIsImageSearching(false);
        setImageSearchError(true);
      }

      // Fall back to mock data in case of error
      const fallbackData = imageFile ? picSearchAPIResult : textSearchAPIResult;
      debugLog('Using fallback data');

      return fallbackData;
    }
  };

  // Function to handle sidebar opening with user toggle tracking
  const handleSidebarOpen = () => {
    setUserHasToggled(true);
    toggleSidebar(true);
  };

  // Handle page change in search results
  const handleSearchPageChange = async (page: number) => {
    debugLog(`Page change requested: ${page}`);

    if (searchQuery && page !== currentSearchPage) {
      // Show loading state immediately
      setIsLoading(true);

      // Store the current query to avoid closure issues
      // Check if we have a Chinese query parameter in the URL (which we added for debugging)
      const params = new URLSearchParams(searchParams.toString());
      const chineseQuery = params.get('cn');

      // Try to get the Chinese keyword from different sources in order of preference:
      // 1. URL parameter
      // 2. localStorage
      // 3. Fall back to the current search query
      let query = searchQuery;

      if (chineseQuery) {
        // 1. Use the Chinese query from URL parameter if available
        query = chineseQuery;
        debugLog(`Page change: Using Chinese query from URL: "${query}"`);
      } else {
        // 2. Try to get the Chinese keyword from localStorage
        try {
          const lastChineseKeyword = localStorage.getItem('lastChineseKeyword');
          const lastOriginalQuery = localStorage.getItem('lastOriginalQuery');

          // Only use the stored Chinese keyword if it corresponds to the current search
          if (lastChineseKeyword && lastOriginalQuery && lastOriginalQuery === searchQuery) {
            query = lastChineseKeyword;
            debugLog(`Page change: Using Chinese query from localStorage: "${query}"`);
          } else {
            // 3. Fall back to the current search query
            debugLog(`Page change: No matching Chinese query found, using original: "${query}"`);
          }
        } catch (e) {
          debugLog(`Page change: Error accessing localStorage, using original query: "${query}"`);
        }
      }

      const currentImage = image;

      try {
        // Get the model from URL params if available
        const modelParam = new URLSearchParams(searchParams.toString()).get('model');

        // Fetch results for the new page immediately
        debugLog(`Fetching results for page ${page}`);
        const data = await fetchSearchResults(query, page, currentImage || undefined, searchQuery, modelParam || undefined);
        debugLog('Search results received for page change');

        // Update state with the results we already have
        setCurrentSearchPage(page);
        setSearchResults(data);

        // Update URL to reflect the new page after we have the results
        const params = new URLSearchParams(searchParams.toString());
        params.set('page', page.toString());
        router.push(`/?${params.toString()}`, { scroll: false });
      } catch (error) {
        debugLog('Error during page change search:', error);
        // Don't clear existing results on error
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleSearch = async (query: string, imageFile?: File, model?: string, modelId?: string) => {
    debugLog(`Search requested: query=${query}, hasImage=${!!imageFile}, model=${model || 'none'}`);

    // Skip if the query is empty
    if (!query && !imageFile) {
      debugLog('Empty search, skipping');
      isPerformingSearch.current = false;
      return { success: false, reason: 'empty_query' };
    }

    const newQuery = query || "";

    // Store current values to avoid closure issues
    const currentQuery = newQuery;
    const currentImageFile = imageFile;

    // PHASE 1: Immediately show search results page with skeleton loading state
    // -------------------------------------------------------------------------
    debugLog('PHASE 1: Showing search results page with skeleton loading state');

    // Set loading state immediately
    setIsLoading(true);

    // Update state to show search results page with skeleton
    setSearchQuery(currentQuery);
    setShowSearchResults(true);
    setImage(currentImageFile || null);
    setCurrentSearchPage(1);
    setSearchResults(null); // Clear previous results to show skeleton

    // Reset image search states
    setImageSearchError(false);
    if (currentImageFile) {
      setIsImageSearching(true);
    } else {
      setIsImageSearching(false);
    }

    // IMPORTANT: Set a flag to prevent URL changes from triggering a new search
    // We'll use a ref to track this
    isPerformingSearch.current = true;

    // Update URL to reflect search state
    const initialParams = new URLSearchParams();
    initialParams.set('q', currentQuery);
    initialParams.set('showResults', 'true');
    initialParams.set('page', '1');

    if (currentImageFile) {
      initialParams.set('searchType', 'image');
    } else {
      initialParams.set('searchType', 'text');
    }

    if (model) {
      initialParams.set('model', model);
    }

    // Update URL immediately to show search results page
    router.push(`/?${initialParams.toString()}`, { scroll: false });

    // Check if we need to use AI for keyword extraction and translation
    // Since we've temporarily commented out the "No AI" option, this check will rarely be true
    const isNoAIModel = modelId === "auto" || model === "No AI (best)";
    let searchQuery = currentQuery;
    let aiAnalysisResult = null;
    let chineseKeyword = null;

    // PHASE 2: If using AI model, extract keywords and translate to Chinese
    // --------------------------------------------------------------------
    if (!isNoAIModel && !currentImageFile) {
      debugLog('PHASE 2: Using AI to extract keywords and translate to Chinese');

      // Show analysis panel if using AI
      setShowAnalysisDetails(true);

      try {
        // Call AI analysis API to get keywords
        const response = await fetch('/api/ai-analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ query: currentQuery, model }),
        });

        if (response.ok) {
          aiAnalysisResult = await response.json();
          if (checkIsProductAnalysis(aiAnalysisResult))
            setAnalysisResult(aiAnalysisResult);
          // Use Chinese keywords for search if available
          if (aiAnalysisResult.chineseKeywords) {
            // Ensure we only use a single search term - take the first part if there are commas
            chineseKeyword = aiAnalysisResult.chineseKeywords.split(',')[0].trim();
            searchQuery = chineseKeyword;

            // Wait a moment to ensure the AI analysis panel is visible and the user can see the Chinese keyword
            await new Promise(resolve => setTimeout(resolve, 1500));
          }
        }
      } catch (error) {
        console.error("Error during AI analysis:", error);
      }
    } else {
      // Always show analysis panel even for No AI or image search
      setShowAnalysisDetails(true);
    }

    // PHASE 3: Fetch search results using the appropriate query
    // --------------------------------------------------------
    let searchResults;
    try {
      // IMPORTANT: Only proceed to PHASE 3 if we have a Chinese keyword (when using AI)
      // or if we're not using AI at all
      if ((isNoAIModel || currentImageFile) || (chineseKeyword && !isNoAIModel && !currentImageFile)) {

        // If we have a Chinese keyword, use it
        if (chineseKeyword) {

          // Update URL with Chinese keyword for debugging and pagination
          const params = new URLSearchParams(initialParams.toString());
          params.set('cn', chineseKeyword);
          router.push(`/?${params.toString()}`, { scroll: false });

          // Store the Chinese keyword in localStorage for persistence
          try {
            localStorage.setItem('lastChineseKeyword', chineseKeyword);
            localStorage.setItem('lastOriginalQuery', currentQuery);
          } catch (e) {
            console.warn('Could not save Chinese keyword to localStorage:', e);
          }

          // Now fetch search results with the Chinese keyword
          searchResults = await fetchSearchResults(chineseKeyword, 1, currentImageFile, currentQuery, model);
        } else {
          // Use the original query if no Chinese keyword is available
          searchResults = await fetchSearchResults(searchQuery, 1, currentImageFile, currentQuery, model);
        }

      } else {
        // This means we're using AI but didn't get a Chinese keyword yet

        // Instead of waiting and potentially making another API call,
        // just use the original query directly
        searchResults = await fetchSearchResults(searchQuery, 1, currentImageFile, currentQuery, model);

        // If we get the Chinese keyword later from the first API call, we can update the UI
        // but we don't need to make another search request
      }
    } catch (error) {
      searchResults = null;
    }

    // PHASE 4: Update state with the final results
    // --------------------------------------------------------

    // 预处理搜索结果，确保它们可以立即显示
    if (searchResults && searchResults.items && searchResults.items.item) {
      // 直接在这里预处理搜索结果，添加必要的字段
      const processedItems = searchResults.items.item.map((item: any) => {
        // 确保每个项目都有必要的字段
        return {
          ...item,
          // 确保 source 字段是小写的 "taobao" 或 "1688"
          source: item.source?.toLowerCase?.() === "1688" ? "1688" : "taobao"
        };
      });

      // 更新处理后的搜索结果
      searchResults.items.item = processedItems;

    }

    // Update the search results state
    setSearchResults(searchResults);

    // Update selected model if provided
    if (model) {
      setSelectedModel(model);
    }

    // Clear loading state
    setIsLoading(false);

    // Clear image search loading state if this was an image search
    if (currentImageFile) {
      setIsImageSearching(false);
    }

    // Reset the isPerformingSearch flag
    // This allows URL changes to trigger searches again
    isPerformingSearch.current = false;


    // 强制刷新 - 使用更直接的方法
    if (searchResults && searchResults.items && searchResults.items.item) {

      // 使用setTimeout确保状态更新已经完成
      setTimeout(() => {
        // 这将触发一个新的渲染周期
        setSearchQuery(prev => {
          return prev.trim();
        });
      }, 500);
    }
    isPerformingSearch.current = false;
    // Return the search results
    return { success: true, results: searchResults };
  }

  const handleBackToFeatured = () => {
    setShowSearchResults(false);
    setSearchQuery(""); // Reset query to empty string
    setImage(null); // Reset image
    // Clear search params from URL
    router.push('/', { scroll: false });
    // Scroll back to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleProductListTranslated = (translatedProducts: any[]) => {
    // Update the search results with the translated products
    searchCache.set(searchQuery, currentSearchPage, { ...searchResults, items: { item: translatedProducts }, translated: true }, isImageSearching);
  };

  const handleTabChange = (tab: 'product' | 'business') => {
    setActiveTab(tab);
  };

  const toggleAnalysisDetails = () => {
    setShowAnalysisDetails(!showAnalysisDetails);
  };

  // The actual JSX rendering remains largely the same, using the state variables
  return (
    <div className="home-page-wrapper overflow-x-hidden w-full max-w-full">
      <AppSidebar />
      {/* MockLogin removed, Compare buttons etc. might need context if moved outside */}
      {/* <MockLogin /> */}
      <CompareFloatingButton />
      <ComparePanel />

      {/* Main content uses state */}
      <main className="home-main-content relative w-full max-w-full">
        {/* Conditionally render the hero section */}
        {!showSearchResults && (
          <section className="pt-0 pb-0 text-center relative hero-section w-full min-h-screen">
            <HeroBackdrop />
            {/* Mobile header bar with menu, logo, and floating menu in one line */}
            {isSmallScreen ? (
              <div className="absolute top-4 left-0 right-0 z-50 flex items-center justify-between px-4">
                <div className="flex items-center">
                  <div
                    className="h-8 w-8 flex items-center justify-center text-white hover:text-gray-200 transition-all duration-200 cursor-pointer"
                    onClick={handleSidebarOpen}
                  >
                    <img
                      src="/images/menu.svg"
                      alt="Menu"
                      className="h-6 w-6 filter invert"
                    />
                  </div>
                  <Link href="/">
                    <img
                      src="/images/logoMiccobuy-white.png"
                      alt="Miccobuy"
                      className="h-10 ml-2 cursor-pointer"
                    />
                  </Link>
                </div>
                <FloatingMenu />
              </div>
            ) : (
              <>
                {/* Desktop: Menu button and logo when sidebar is collapsed */}
                {state === "collapsed" && (
                  <div className="absolute top-4 left-4 z-50 flex items-center">
                    <div
                      className="h-8 w-8 flex items-center justify-center text-white hover:text-gray-200 transition-all duration-200 cursor-pointer"
                      onClick={handleSidebarOpen}
                    >
                      <img
                        src="/images/menu.svg"
                        alt="Menu"
                        className="h-6 w-6 filter invert"
                      />
                    </div>
                    <Link href="/">
                      <img
                        src="/images/logoMiccobuy-white.png"
                        alt="Miccobuy"
                        className="h-12 cursor-pointer"
                      />
                    </Link>
                  </div>
                )}
                {/* Desktop: Floating menu positioned on top of hero section */}
                <div className="absolute top-4 right-4 z-30 w-auto flex justify-end px-4 sm:px-6 lg:px-8">
                  <FloatingMenu />
                </div>
              </>
            )}
            {/* accio-container now only applies padding */}
            <div className="relative z-10 hero-content-container w-full max-w-7xl mx-auto pt-28 md:pt-36 lg:pt-44 pb-10 md:pb-12 px-4 sm:px-6 lg:px-8">

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 md:mb-10 text-white">
                {t("PROFESSIONAL_SOURCING")}
              </h1>
              <h2 className="text-base md:text-lg lg:text-xl mb-10 md:mb-12 text-white">
                {t("PROMPT_TXT")}
              </h2>
              <div className="my-12 md:my-16 max-w-4xl mx-auto">
                {/* Pass current searchQuery state to SearchBox */}
                <SearchBox
                  onSearch={handleSearch}
                  defaultQuery={searchQuery}
                />

                {/* Suggested prompts */}
                <SuggestedPrompts setSearchQuery={setSearchQuery} />
              </div>
              {/* Process Steps */}
              <ProcessSteps />

            </div>
          </section>
        )}

        {showSearchResults ? (
          <section id="searchResults" className="pt-4 pb-16 bg-white px-4 sm:px-6 lg:px-8 search-results-section relative"> {/* Added section class and relative */}
            {/* Mobile header bar with menu, logo, and floating menu in one line */}
            {isSmallScreen ? (
              <div className="absolute top-4 left-0 right-0 z-50 flex items-center justify-between px-4">
                <div className="flex items-center">
                  <div
                    className="h-8 w-8 flex items-center justify-center text-gray-700 hover:text-gray-900 transition-all duration-200 cursor-pointer"
                    onClick={handleSidebarOpen}
                  >
                    <img
                      src="/images/menu.svg"
                      alt="Menu"
                      className="h-6 w-6"
                    />
                  </div>
                  <img
                    src="/images/logoMiccobuy.png"
                    alt="Miccobuy"
                    className="h-10 ml-2"
                  />
                </div>
                <FloatingMenu />
              </div>
            ) : (
              <>
                {/* Desktop: Menu button and logo when sidebar is collapsed */}
                {state === "collapsed" && (
                  <div className="absolute top-4 left-4 z-50 flex items-center">
                    <div
                      className="h-8 w-8 flex items-center justify-center text-gray-700 hover:text-gray-900 transition-all duration-200 cursor-pointer"
                      onClick={handleSidebarOpen}
                    >
                      <img
                        src="/images/menu.svg"
                        alt="Menu"
                        className="h-6 w-6"
                      />
                    </div>
                    <img
                      src="/images/logoMiccobuy.png"
                      alt="Miccobuy"
                      className="h-12"
                    />
                  </div>
                )}
                {/* Desktop: Floating menu positioned on top of search results */}
                <div className="absolute top-4 right-4 z-30 w-auto flex justify-end px-4 sm:px-6 lg:px-8">
                  <FloatingMenu />
                </div>
              </>
            )}
             {/* Consider if accio-container needs adjustment or removal */}
            <div className="miccobuy-container search-results-container pt-12"> {/* Added container class and top padding */}
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 search-results-header"> {/* Added header class */}
                <div className="flex-1 w-full sm:w-auto mb-4 sm:mb-0">
                  <SearchTabs
                    activeTab={activeTab}
                    onTabChange={handleTabChange}
                  />
                </div>
                <div>
                  <CompareButton />
                </div>
              </div>

              <div className="flex flex-col w-full my-4">
                {/* Use the same SearchBox component as the homepage */}
                <SearchBox
                  onSearch={handleSearch}
                  defaultQuery={searchQuery}
                />
              </div>

              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2 sm:gap-0">
                <div className="flex items-center">
                  <div
                    onClick={toggleAnalysisDetails}
                    className="flex items-center cursor-pointer hover:text-gray-900 transition-colors"
                  >
                    <p className="text-gray-700 text-xs sm:text-sm">{t("FROM")} <span className="font-semibold">45000+</span> {tCommon("PRODUCTS")} & <span className="font-semibold">10000+</span> {tCommon("SUPPLIERS")}</p>
                    <button
                      className="ml-1 sm:ml-2 text-gray-500 hover:text-gray-700"
                    >
                      <ChevronDown className={`w-3 h-3 sm:w-4 sm:h-4 transition-transform ${showAnalysisDetails ? 'transform rotate-180' : ''}`} />
                    </button>
                  </div>
                </div>
                <Button
                  variant="link"
                  className="text-xs sm:text-sm text-accio-primary px-0 sm:px-2"
                  onClick={handleBackToFeatured}
                >
                  <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                  {t("TO_FEATURED")}
                </Button>
              </div>

              {showSearchResults && (
                <AnalysisPanel
                  key={`analysis-${searchQuery}-${!!image}`}
                  query={searchQuery || ""}
                  isVisible={true}
                  model={selectedModel}
                  isImageSearch={!!image}
                  imageFile={image}
                  analysisResult={analysisResult}
                />
              )}

              {/* Show image search loading panel */}
              {isImageSearching && (
                <ImageSearchLoadingPanel isVisible={true} />
              )}

              {/* Show image search error dialog */}
              <ImageSearchErrorDialog
                isOpen={imageSearchError}
                onClose={() => setImageSearchError(false)}
              />

              <SearchFilters />

              {/* Removed duplicate "All products" label container div */}
              <div className="mt-6 search-results-grid-wrapper"> {/* Added grid wrapper class */}
                {isLoading ? (
                  // Show skeleton loading state instead of spinner
                  <SearchResultsSkeleton />
                ) : searchResults ? (
                  <>
                    {searchResults.items && searchResults.items.item ? (
                      <div key={`search-results-${searchQuery}-${currentSearchPage}`}>
                        <SearchResults
                          jsonData={searchResults}
                          onPageChange={handleSearchPageChange}
                          onProductListTranslated={handleProductListTranslated}
                        />
                      </div>
                    ) : (
                      <div className="text-center py-12 text-gray-500">
                        {t("NO_FOUND")}
                      </div>
                    )}

                    {/* Debug buttons - only visible in development */}
                    {process.env.NODE_ENV === 'development' && (
                      <div className="mt-4 text-right flex justify-end gap-4">
                        <button
                          onClick={() => {
                            console.log('Current search state:', {
                              query: searchQuery,
                              page: currentSearchPage,
                              results: searchResults,
                              totalResults: searchResults?.items?.total_results || 0,
                              totalPages: searchResults?.items?.pagecount || 1,
                              hasItems: !!searchResults?.items?.item,
                              itemCount: searchResults?.items?.item?.length || 0
                            });
                          }}
                          className="text-xs text-gray-500 hover:text-gray-700 underline"
                        >
                          Debug Info
                        </button>
                        <button
                          onClick={() => {
                            searchCache.clear();
                            alert('Search cache cleared');
                          }}
                          className="text-xs text-red-500 hover:text-red-700 underline"
                        >
                          Clear Cache
                        </button>
                        <button
                          onClick={() => {
                            // Force a new search with current query
                            handleSearch(searchQuery, image || undefined);
                          }}
                          className="text-xs text-blue-500 hover:text-blue-700 underline"
                        >
                          Refresh Results
                        </button>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-center py-12 text-gray-500">
                    {t("RESULTS_DISPLAY")}
                  </div>
                )}
              </div>
            </div>
          </section>
        ) : (
          <>
            {/* Adding classes to other sections for consistency */}
            {/* Service Explanation */}
            <ServiceExplanation />

            {/* Category tabs moved below Service Explanation
            <section className="py-8 bg-white">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 className="text-2xl font-semibold text-center mb-6">Browse Categories</h2>
                <div className="overflow-x-auto">
                  <CategoryTabs />
                </div>
              </div>
            </section>*/}

            <section className="py-16 px-4 sm:px-6 lg:px-8 trending-section">
              <div className="miccobuy-container trending-container">
                <h2 className="text-2xl font-semibold text-center mb-8">
                  {t("TRENDING")}
                </h2>
                <ProductGrid />
              </div>
            </section>

            <section className="py-16 bg-white px-4 sm:px-6 lg:px-8 features-section"> {/* Added section class */}
               {/* Consider if accio-container needs adjustment or removal */}
              <div className="miccobuy-container features-container"> {/* Added container class */}
                <h2 className="text-3xl font-bold text-center mb-4">
                  {t("HELP_SOURCE")}
                </h2>
                <p className="text-xl text-gray-600 text-center mb-12 max-w-3xl mx-auto">
                  {t("ACCESS_OVER")}
                </p>
                <FeatureCards />

              </div>
            </section>

            <section className="py-16 px-4 sm:px-6 lg:px-8 about-section"> {/* Added section class */}
               {/* Consider if accio-container needs adjustment or removal */}
              <div className="miccobuy-container about-container"> {/* Added container class */}
                <AboutSection />
              </div>
            </section>

            <section className="py-12 bg-white px-4 sm:px-6 lg:px-8 partnership-section"> {/* Added section class */}
               {/* Consider if accio-container needs adjustment or removal */}
              <div className="miccobuy-container partnership-container"> {/* Added container class */}
                <PartnershipSection />
              </div>
            </section>

            <section className="py-16 px-4 sm:px-6 lg:px-8 testimonial-section"> {/* Added section class */}
               {/* Consider if accio-container needs adjustment or removal */}
              <div className="miccobuy-container testimonial-container"> {/* Added container class */}
                <h2 className="text-2xl font-semibold text-center mb-8">
                  {t("JOIN")}
                </h2>
                <div className="max-w-4xl mx-auto">
                  <Testimonial />
                </div>
                <div className="mt-10">
                  <PartnerLogos />
                </div>
              </div>
            </section>
          </>
        )}
      </main>

      {/* Footer remains outside the main content flow potentially */}
      <Footer />
    </div> // Close home-page-wrapper div
  );
}

const SuggestedPromptConfig = {
  PROMPT_TSHIRT: "I want to order 5,000 t-shirts with my custom logo",
  WATCHES: "Find Smart Watches with good reviews",
  TELEPHONES: "Show me Wireless headphones with noise cancellation",
  OEM: "Looking for customizable OEM Hoodies",
  TOOTH_BRUSH: "Recommend Electric toothbrushes with long battery life"
};
const SUggestedPromptList = Object.keys(SuggestedPromptConfig);
function SuggestedPrompts ({ setSearchQuery }: {
  setSearchQuery: React.Dispatch<React.SetStateAction<string>>
}) {
  const t = useTranslations(I18NNamespace.PAGES_HOME);
  return (
    <div className="mt-8 md:mt-10 flex flex-col sm:flex-row items-center justify-center text-white">
      <div className="text-sm mb-2 sm:mb-0 sm:mr-2">{t("TRY")}</div>
      <div className="flex flex-wrap gap-2 justify-center">
        {SUggestedPromptList.map(prompt => (
          <button
            key={prompt}
            type="button"
            className="px-2 sm:px-3 py-1 bg-white/20 hover:bg-white/30 rounded-full text-xs sm:text-sm text-white transition-colors"
            onClick={() => {
              // Set the complete prompt in the search bar
              setSearchQuery(SuggestedPromptConfig[prompt as keyof typeof SuggestedPromptConfig]);
            }}
          >
            {t(prompt)}
          </button>
        ))}
      </div>
    </div>
  );
}

// Export default function that wraps the content in Suspense
export default function Home() {
  return (
    // Suspense is needed because useSearchParams reads from the URL, which might not be available during SSR pre-rendering
    <Suspense fallback={<div>Loading...</div>}>
      <HomePageContent />
    </Suspense>
  );
}
