import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';
import adminConfig from '../adminConfig.mjs';
import { locales, defaultLocale } from './i18n';

// Interface for rate limiter results
interface RateLimitResult {
  success: boolean;
  limit: number;
  remaining: number;
  reset: number;
}

// Simple in-memory rate limiter for development
class InMemoryRateLimit {
  private storage: Map<string, { count: number, resetTime: number }>;
  private limit: number;
  private windowMs: number;

  constructor(limit: number, windowMs: number) {
    this.storage = new Map();
    this.limit = limit;
    this.windowMs = windowMs;
  }

  async limit(key: string): Promise<RateLimitResult> {
    const now = Date.now();
    const record = this.storage.get(key);

    // If no record exists or the reset time has passed, create a new record
    if (!record || record.resetTime <= now) {
      this.storage.set(key, {
        count: 1,
        resetTime: now + this.windowMs
      });

      return {
        success: true,
        limit: this.limit,
        remaining: this.limit - 1,
        reset: Math.ceil((now + this.windowMs) / 1000)
      };
    }

    // If the record exists and the limit is not exceeded, increment the count
    if (record.count < this.limit) {
      record.count += 1;
      this.storage.set(key, record);

      return {
        success: true,
        limit: this.limit,
        remaining: this.limit - record.count,
        reset: Math.ceil(record.resetTime / 1000)
      };
    }

    // If the limit is exceeded, return failure
    return {
      success: false,
      limit: this.limit,
      remaining: 0,
      reset: Math.ceil(record.resetTime / 1000)
    };
  }
}

// Interface for rate limiter
interface RateLimiter {
  limit(key: string): Promise<RateLimitResult>;
}

// Initialize rate limiters
let hourlyRateLimit: RateLimiter;
let dailyRateLimit: RateLimiter;

// Get rate limit configuration from adminConfig
const { limits, timeWindows, redisPrefix, endpointLimits } = adminConfig.rateLimiting;

// Create a map to store endpoint-specific rate limiters
const endpointRateLimiters: Map<string, { hourly: RateLimiter, daily: RateLimiter }> = new Map();

// Try to use Redis if environment variables are available
if (process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN) {
  const redis = new Redis({
    url: process.env.UPSTASH_REDIS_REST_URL,
    token: process.env.UPSTASH_REDIS_REST_TOKEN,
  });

  // Create Redis-based rate limiters for default limits
  hourlyRateLimit = new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(limits.hourly, timeWindows.hourly),
    analytics: true,
    prefix: redisPrefix.hourly,
  });

  dailyRateLimit = new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(limits.daily, timeWindows.daily),
    analytics: true,
    prefix: redisPrefix.daily,
  });

  // Create endpoint-specific rate limiters
  if (endpointLimits) {
    for (const [endpoint, endpointLimit] of Object.entries(endpointLimits)) {
      endpointRateLimiters.set(endpoint, {
        hourly: new Ratelimit({
          redis,
          limiter: Ratelimit.slidingWindow(endpointLimit.hourly, timeWindows.hourly),
          analytics: true,
          prefix: `${redisPrefix.hourly}:${endpoint.replace(/\//g, '_')}`,
        }),
        daily: new Ratelimit({
          redis,
          limiter: Ratelimit.slidingWindow(endpointLimit.daily, timeWindows.daily),
          analytics: true,
          prefix: `${redisPrefix.daily}:${endpoint.replace(/\//g, '_')}`,
        }),
      });
    }
  }
} else {
  // Fallback to in-memory rate limiters for development
  console.warn('Using in-memory rate limiting. For production, set UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_REST_TOKEN env variables.');

  // Convert time window strings to milliseconds for in-memory rate limiter
  const hourlyMs = 60 * 60 * 1000; // 1 hour in milliseconds
  const dailyMs = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

  hourlyRateLimit = new InMemoryRateLimit(limits.hourly, hourlyMs);
  dailyRateLimit = new InMemoryRateLimit(limits.daily, dailyMs);

  // Create endpoint-specific rate limiters
  if (endpointLimits) {
    for (const [endpoint, endpointLimit] of Object.entries(endpointLimits)) {
      endpointRateLimiters.set(endpoint, {
        hourly: new InMemoryRateLimit(endpointLimit.hourly, hourlyMs),
        daily: new InMemoryRateLimit(endpointLimit.daily, dailyMs),
      });
    }
  }
}

// Define which paths should be rate limited
const RATE_LIMITED_PATHS = adminConfig.rateLimiting.endpoints;

// Get the preferred locale from request headers
function getLocale(request: NextRequest) {
  // Check if there is a locale in the pathname
  const pathname = request.nextUrl.pathname;
  const pathnameLocale = locales.find(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  if (pathnameLocale) return pathnameLocale;

  // Check for locale in cookie
  const localeCookie = request.cookies.get('NEXT_LOCALE')?.value;
  if (localeCookie && locales.includes(localeCookie)) {
    return localeCookie;
  }

  // Check for Accept-Language header
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    const preferredLocale = acceptLanguage
      .split(',')
      .map(lang => lang.split(';')[0].trim())
      .find(lang => locales.includes(lang.substring(0, 2)));

    if (preferredLocale) {
      return preferredLocale.substring(0, 2);
    }
  }

  // Default locale as fallback
  return defaultLocale;
}

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Skip for assets, api routes, etc.
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/images') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  // Check if the pathname already has a locale
  const pathnameHasLocale = locales.some(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  // If no locale in path, redirect to add locale
  if (!pathnameHasLocale) {
    const locale = getLocale(request);
    const newUrl = new URL(`/${locale}${pathname}`, request.url);
    newUrl.search = request.nextUrl.search;

    return NextResponse.redirect(newUrl);
  }

  // Continue with rate limiting for API routes
  // Check if the path should be rate limited
  const shouldRateLimit = RATE_LIMITED_PATHS.some(limitedPath =>
    pathname.startsWith(limitedPath)
  );

  if (!shouldRateLimit) {
    return NextResponse.next();
  }

  // Get IP address from Cloudflare headers or fallback to connection remote address
  const ip = request.headers.get('cf-connecting-ip') ||
             request.headers.get('x-real-ip') ||
             request.ip ||
             'unknown';

  // Check if the request is from a bot
  const userAgent = request.headers.get('user-agent') || '';
  const isBotRequest = userAgent.toLowerCase().includes('bot') ||
                      userAgent.toLowerCase().includes('crawler') ||
                      userAgent.toLowerCase().includes('spider');

  // Apply stricter limits for bots
  if (isBotRequest) {
    const response = NextResponse.json(
      { error: adminConfig.rateLimiting.errorMessages.bot },
      { status: 429 }
    );
    response.headers.set('Retry-After', '3600');
    return response;
  }

  try {
    // Find the matching endpoint for endpoint-specific rate limits
    let matchingEndpoint: string | null = null;
    let currentLimits = limits; // Default to global limits

    // Find the most specific matching endpoint
    for (const endpoint of Object.keys(endpointLimits || {})) {
      if (pathname.startsWith(endpoint) && (!matchingEndpoint || endpoint.length > matchingEndpoint.length)) {
        matchingEndpoint = endpoint;
      }
    }

    // Use endpoint-specific limiters if available
    let currentHourlyLimiter = hourlyRateLimit;
    let currentDailyLimiter = dailyRateLimit;

    if (matchingEndpoint && endpointRateLimiters.has(matchingEndpoint)) {
      const endpointLimiters = endpointRateLimiters.get(matchingEndpoint)!;
      currentHourlyLimiter = endpointLimiters.hourly;
      currentDailyLimiter = endpointLimiters.daily;
      currentLimits = endpointLimits[matchingEndpoint];
      console.log(`Using endpoint-specific rate limits for ${matchingEndpoint}: hourly=${currentLimits.hourly}, daily=${currentLimits.daily}`);
    }

    // Check hourly rate limit
    const hourlyResult = await currentHourlyLimiter.limit(`${ip}:hourly:${matchingEndpoint || 'global'}`);

    // If hourly limit is exceeded
    if (!hourlyResult.success) {
      const errorMessage = adminConfig.rateLimiting.errorMessages.hourly.replace('{limit}', currentLimits.hourly.toString());
      const response = NextResponse.json(
        { error: errorMessage },
        { status: 429 }
      );
      response.headers.set('Retry-After', hourlyResult.reset.toString());
      return response;
    }

    // Check daily rate limit
    const dailyResult = await currentDailyLimiter.limit(`${ip}:daily:${matchingEndpoint || 'global'}`);

    // If daily limit is exceeded
    if (!dailyResult.success) {
      const errorMessage = adminConfig.rateLimiting.errorMessages.daily.replace('{limit}', currentLimits.daily.toString());
      const response = NextResponse.json(
        { error: errorMessage },
        { status: 429 }
      );
      response.headers.set('Retry-After', dailyResult.reset.toString());
      return response;
    }

    // Add rate limit headers to the response
    const response = NextResponse.next();
    response.headers.set('X-RateLimit-Limit-Hour', currentLimits.hourly.toString());
    response.headers.set('X-RateLimit-Remaining-Hour', hourlyResult.remaining.toString());
    response.headers.set('X-RateLimit-Limit-Day', currentLimits.daily.toString());
    response.headers.set('X-RateLimit-Remaining-Day', dailyResult.remaining.toString());

    return response;
  } catch (error) {
    console.error('Rate limiting error:', error);
    // If there's an error with rate limiting, allow the request to proceed
    return NextResponse.next();
  }
}

// Configure which paths this middleware will run on
export const config = {
  matcher: [
    // Match all paths except static assets and API routes that don't need i18n
    '/((?!_next|images|favicon.ico|api/auth).*)',
    // Include specific routes for locale handling
    '/(login|signup|verify-email|checkout|history|my-orders|product|product-detail-translate-browser|search|subscription|admin)',
    // Match API routes that need rate limiting,
    '/api/ai-analysis/:path*',
    '/search/:path*',
    '/api/search/:path*',
  ],
};
