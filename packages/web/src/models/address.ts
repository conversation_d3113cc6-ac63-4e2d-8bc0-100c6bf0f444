import { ObjectId } from 'mongodb';
import { connectToDatabase, Collections } from '../lib/database'; // Changed path alias to relative path

export interface Address {
  _id?: ObjectId;
  userId: ObjectId;
  name: string;
  street: string;
  city: string;
  zip: string;
  country: string;
  phone: string;
  email: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
  type: 'address';
}

export interface AddressWithId {
  _id: ObjectId;
  userId: ObjectId;
  name: string;
  street: string;
  city: string;
  zip: string;
  country: string;
  phone: string;
  email: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
  type: 'address';
}

export const createAddress = async (address: Omit<Address, '_id' | 'createdAt' | 'updatedAt'>) => {
  try {
    const { db, client } = await connectToDatabase();

    const newAddress = {
      ...address,
      createdAt: new Date(),
      updatedAt: new Date(),
      isDefault: false
    };

    

    // Removed explicit check for collection existence

    // console.log('ℹ️ Current address count:', await db.collection(Collections.ADDRESSES).countDocuments()); // Optional: Keep if needed for debugging before insert
    
    // Insert document
    console.log('ℹ️ Inserting address document...');
    const result = await db.collection(Collections.ADDRESSES).insertOne({
      ...newAddress,
      type: 'address' // Add type field to distinguish documents
    });
    
    // Verify the document was actually saved
    console.log('ℹ️ Verifying document was saved...');
    const savedDoc = await db.collection(Collections.ADDRESSES).findOne({_id: result.insertedId});
    if (savedDoc) {
      console.log('✅ Verified document exists in database:', {
        id: savedDoc._id,
        name: savedDoc.name,
        street: savedDoc.street,
        city: savedDoc.city,
        createdAt: savedDoc.createdAt
      });
      console.log('ℹ️ New address count:', await db.collection(Collections.ADDRESSES).countDocuments());
    } else {
      console.error('❌ Document not found after creation');
      console.error('Current address count:', await db.collection(Collections.ADDRESSES).countDocuments());
      throw new Error('Failed to verify document creation');
    }

    // Verify write concern
    const writeConcern = client.options.writeConcern;

    return result.insertedId;
  } catch (error) {
    console.error('Failed to create address:', error);
    throw error;
  }
};

export const getAddressesByUser = async (userId: string): Promise<AddressWithId[]> => {
  try {
    const { db } = await connectToDatabase();
    // Correctly await the result of the query chain and assign to addresses
    const addresses = await db.collection(Collections.ADDRESSES) 
      .find({ 
        userId: new ObjectId(userId) // Revert query to use ObjectId
      })
      .sort({ isDefault: -1, createdAt: -1 })
      .toArray(); 
      
    // Log the raw result *after* getting the array

    // Now map the raw array
    const mappedAddresses = addresses.map(doc => ({ // Map the addresses array
        _id: doc._id,
        userId: doc.userId,
        name: doc.name,
        street: doc.street,
        city: doc.city,
        zip: doc.zip,
        country: doc.country,
        phone: doc.phone,
        email: doc.email,
        isDefault: doc.isDefault || false,
        createdAt: doc.createdAt,
        updatedAt: doc.updatedAt,
        type: doc.type || 'address' // Ensure type is present in mapped result, default to 'address'
      }));
      
   
    return mappedAddresses; // Return the mapped array
  } catch (error) {
    console.error('[Model getAddressesByUser] Failed to get addresses:', error); // Add context to log
    throw error;
  }
};

export const updateAddress = async (id: string, updates: Partial<Address>) => {
  const { db } = await connectToDatabase();
  try {
    // Ensure the query matches the document type
    const filter = { _id: new ObjectId(id), type: 'address' }; 
    const updateDoc = { 
      $set: { 
        ...updates, 
        updatedAt: new Date() 
        // Optionally ensure type field exists if it might be missing:
        // type: 'address' 
      } 
    };
    

    const result = await db.collection(Collections.ADDRESSES).updateOne(filter, updateDoc);
    return result;
  } catch (error) {
    console.error('Failed to update address:', error);
    throw error;
  }
};

export const deleteAddress = async (id: string) => {
  const { db } = await connectToDatabase();
  try {
    console.log(`ℹ️ Deleting address ${id}`);
    const result = await db.collection(Collections.ADDRESSES).deleteOne({ 
      _id: new ObjectId(id),
      type: 'address' 
    });
    return result;
  } catch (error) {
    console.error('Failed to delete address:', error);
    throw error;
  }
};
