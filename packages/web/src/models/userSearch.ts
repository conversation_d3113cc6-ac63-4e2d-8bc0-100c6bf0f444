import { ObjectId } from 'mongodb';
import { connectToDatabase, Collections } from '@/lib/database';

// Define the UserSearch interface
export interface UserSearch {
  _id?: ObjectId;
  userId?: string | null; // Optional - can be null for anonymous users
  userEmail?: string | null; // Optional - can be null for anonymous users
  originalQuery: string; // The original search query in user's language
  chineseKeywords?: string | null; // The translated Chinese keywords used for Taobao API
  searchType: 'text' | 'image'; // Type of search performed
  aiModel?: string | null; // AI model used for translation, if any
  imageUrl?: string | null; // URL of the uploaded image for image searches
  resultCount: number; // Number of results returned
  displayData: any[]; // Simplified data for display in trending section
  createdAt: Date;
  updatedAt: Date;
}

// Function to save a user search
export const saveUserSearch = async (searchData: Omit<UserSearch, '_id' | 'createdAt' | 'updatedAt'>): Promise<string | null> => {
  try {
    const { db } = await connectToDatabase();

    // Prepare the search data with timestamps
    const newSearch: Omit<UserSearch, '_id'> = {
      ...searchData,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Insert the search data
    const result = await db.collection(Collections.USER_SEARCHES).insertOne(newSearch);

    console.log(`Saved user search: ${searchData.originalQuery} (${result.insertedId})`);
    return result.insertedId.toString();
  } catch (error) {
    console.error('Error saving user search:', error);
    return null;
  }
};

// Function to get trending searches
export const getTrendingSearches = async (limit: number = 10): Promise<UserSearch[]> => {
  try {
    const { db } = await connectToDatabase();

    // No need to check if the collection exists as it's already defined in Collections

    // Get the most recent searches
    // We could implement more complex trending logic based on popularity later
    const searches = await db.collection<UserSearch>(Collections.USER_SEARCHES)
      .find({})
      .sort({ createdAt: -1 })
      .limit(limit)
      .toArray();

    return searches;
  } catch (error) {
    console.error('Error getting trending searches:', error);
    return [];
  }
};

// Function to get popular search terms (aggregated by query)
export const getPopularSearchTerms = async (limit: number = 10): Promise<{ query: string; count: number }[]> => {
  try {
    const { db } = await connectToDatabase();

    // Aggregate searches by originalQuery and count occurrences
    const popularTerms = await db.collection(Collections.USER_SEARCHES)
      .aggregate([
        {
          $group: {
            _id: "$originalQuery",
            count: { $sum: 1 },
            latestDate: { $max: "$createdAt" }
          }
        },
        { $sort: { count: -1, latestDate: -1 } },
        { $limit: limit },
        { $project: { query: "$_id", count: 1, _id: 0 } }
      ])
      .toArray();

    // Cast the result to the expected type
    return popularTerms as { query: string; count: number }[];
  } catch (error) {
    console.error('Error getting popular search terms:', error);
    return [];
  }
};
