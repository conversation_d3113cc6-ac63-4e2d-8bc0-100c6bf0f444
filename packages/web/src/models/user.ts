import { ObjectId } from 'mongodb';
import { connectToDatabase, Collections } from '@/lib/database';

// Basic User interface - align with NextAuth User and your needs
export interface User {
  _id?: ObjectId;
  name?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  email?: string | null;
  isVerified?: boolean;
  emailVerified?: Date | null;
  image?: string | null;
  plan?: 'Free' | 'Pro' | 'Enterprise';
  verificationCode?: string;
  verificationCodeExpires?: Date;
  createdAt: Date;
  updatedAt: Date;
  // Add any other fields you store for users
}

// Function to find or create a user (upsert logic)
export const findOrCreateUser = async (userData: {
  email: string;
  name?: string | null;
  image?: string | null;
}): Promise<User | null> => {
  if (!userData.email) {
    console.error("findOrCreateUser: Email is required.");
    return null;
  }

  try {
    const { db } = await connectToDatabase();
    const usersCollection = db.collection<User>(Collections.USERS);

    const existingUser = await usersCollection.findOne({ email: userData.email });

    if (existingUser) {
      // Optionally update name/image if they changed from provider
      // await usersCollection.updateOne(
      //   { _id: existingUser._id },
      //   { $set: { name: userData.name, image: userData.image, updatedAt: new Date() } }
      // );
      return existingUser;
    } else {
      // Create new user
      const newUser: Omit<User, '_id'> = {
        email: userData.email,
        name: userData.name,
        image: userData.image,
        emailVerified: null, // Or set based on provider info if available
        plan: 'Free', // Set default plan to Free for new users
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      const result = await usersCollection.insertOne(newUser);
      console.log(`Created new user with ID: ${result.insertedId} for email: ${userData.email}`);
      // Fetch the newly created user to return the full document with _id
      return await usersCollection.findOne({ _id: result.insertedId });
    }
  } catch (error) {
    console.error("Error in findOrCreateUser:", error);
    return null; // Or throw error depending on desired handling
  }
};

// Optional: Function to get user by ID if needed elsewhere
export const getUserById = async (userId: string): Promise<User | null> => {
   try {
     const { db } = await connectToDatabase();
     if (!ObjectId.isValid(userId)) return null;
     return await db.collection<User>(Collections.USERS).findOne({ _id: new ObjectId(userId) });
   } catch (error) {
     console.error("Error in getUserById:", error);
     return null;
   }
}

// Get user by email
export const getUserByEmail = async (email: string): Promise<User | null> => {
  try {
    const { db } = await connectToDatabase();
    return await db.collection<User>(Collections.USERS).findOne({ email });
  } catch (error) {
    console.error("Error in getUserByEmail:", error);
    return null;
  }
}

// Set verification code for a user
export const setVerificationCode = async (email: string, code: string): Promise<boolean> => {
  try {
    const { db } = await connectToDatabase();

    // Code expires in 10 minutes
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 10);

    const result = await db.collection<User>(Collections.USERS).updateOne(
      { email },
      {
        $set: {
          verificationCode: code,
          verificationCodeExpires: expiresAt,
          updatedAt: new Date()
        }
      }
    );

    return result.modifiedCount > 0;
  } catch (error) {
    console.error("Error in setVerificationCode:", error);
    return false;
  }
}

// Verify a code and mark the user as verified if correct
export const verifyCode = async (email: string, code: string): Promise<boolean> => {
  try {
    const { db } = await connectToDatabase();

    // Find the user with the matching email and code
    const user = await db.collection<User>(Collections.USERS).findOne({
      email,
      verificationCode: code,
      verificationCodeExpires: { $gt: new Date() } // Code must not be expired
    });

    if (!user) {
      return false;
    }

    // Mark the user as verified and clear the verification code
    const result = await db.collection<User>(Collections.USERS).updateOne(
      { _id: user._id },
      {
        $set: {
          isVerified: true,
          emailVerified: new Date(),
          verificationCode: null,
          verificationCodeExpires: null,
          updatedAt: new Date()
        }
      }
    );

    return result.modifiedCount > 0;
  } catch (error) {
    console.error("Error in verifyCode:", error);
    return false;
  }
}

// Create a new user with verification code
export const createUserWithVerification = async (userData: {
  email: string;
  firstName?: string | null;
  lastName?: string | null;
  verificationCode: string;
}): Promise<User | null> => {
  try {
    const { db } = await connectToDatabase();

    // Code expires in 10 minutes
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 10);

    // Create the user object
    const newUser: Omit<User, '_id'> = {
      email: userData.email,
      firstName: userData.firstName || null,
      lastName: userData.lastName || null,
      name: userData.firstName && userData.lastName
        ? `${userData.firstName} ${userData.lastName}`
        : userData.email?.split('@')[0] || null,
      isVerified: false,
      emailVerified: null,
      verificationCode: userData.verificationCode,
      verificationCodeExpires: expiresAt,
      plan: 'Free',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Insert the user
    const result = await db.collection<User>(Collections.USERS).insertOne(newUser);

    // Return the created user
    return result.acknowledged
      ? await db.collection<User>(Collections.USERS).findOne({ _id: result.insertedId })
      : null;
  } catch (error) {
    console.error("Error in createUserWithVerification:", error);
    return null;
  }
}
