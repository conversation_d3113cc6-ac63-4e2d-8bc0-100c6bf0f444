import { ObjectId } from 'mongodb';
import { connectToDatabase, Collections } from '@/lib/database';
// Import shared types and enum from the new dedicated file
import {
  type Order,
  type OrderItem,
  type ShippingInfo,
  type PaymentInfo,
  OrderStatus // Import enum value directly
} from '@/types/order-shared';

// No longer need to define OrderStatus enum or re-export types here.

// Define a type for the database representation which uses ObjectId
// Note: This DbOrder type now correctly references the imported Order/ShippingInfo types
type DbOrder = Omit<Order, '_id' | 'userId' | 'shipping'> & { // Keep status here
  _id?: ObjectId;
  userId: ObjectId;
  shipping: Omit<ShippingInfo, 'addressId'> & { addressId: ObjectId };
  // status is already part of Order, so it's included via Omit
};

export const createOrder = async (orderData: Omit<Order, '_id' | 'createdAt' | 'updatedAt' | 'status'>): Promise<string> => {
  const { db } = await connectToDatabase();
  // Convert string IDs to ObjectId for database storage
  const newDbOrder: Omit<DbOrder, '_id'> = {
    ...orderData,
    userId: new ObjectId(orderData.userId),
    shipping: {
      ...orderData.shipping,
      addressId: new ObjectId(orderData.shipping.addressId),
    },
    status: OrderStatus.PENDING,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  // Use Collections enum and DbOrder type argument
  const result = await db.collection<DbOrder>(Collections.ORDERS).insertOne(newDbOrder);
  // Return the inserted ID as a string
  return result.insertedId.toHexString();
};

// Helper function to convert DbOrder to Order
const mapDbOrderToOrder = (dbOrder: DbOrder): Order => {
  // Explicitly map fields to ensure correct types and include status
  return {
    // Map all fields from Order interface that are present in DbOrder
    items: dbOrder.items,
    payment: dbOrder.payment,
    status: dbOrder.status, // Include status
    subtotal: dbOrder.subtotal,
    shippingCost: dbOrder.shippingCost,
    tax: dbOrder.tax,
    total: dbOrder.total,
    createdAt: dbOrder.createdAt,
    updatedAt: dbOrder.updatedAt,
    // Additional fields for PENDING_TRANSFER status
    freeStoragePeriod: dbOrder.freeStoragePeriod,
    warehouseEntryDate: dbOrder.warehouseEntryDate,
    combined: dbOrder.combined,
    combinedAt: dbOrder.combinedAt,
    // Convert IDs
    _id: dbOrder._id?.toHexString(),
    userId: dbOrder.userId.toHexString(),
    shipping: {
      ...dbOrder.shipping,
      addressId: dbOrder.shipping.addressId.toHexString(), // Convert ObjectId to string
    },
  };
};


export const getOrderById = async (orderId: string): Promise<Order | null> => {
  if (!ObjectId.isValid(orderId)) {
    return null; // Invalid ID format
  }
  const { db } = await connectToDatabase();
  // Use DbOrder type for the query
  const dbOrder = await db.collection<DbOrder>(Collections.ORDERS).findOne({ _id: new ObjectId(orderId) });

  if (!dbOrder) {
    return null;
  }
  // Convert the result back to the Order type
  return mapDbOrderToOrder(dbOrder);
};

export const getOrdersByUser = async (userId: string): Promise<Order[]> => {
  if (!ObjectId.isValid(userId)) {
    return []; // Invalid ID format
  }
  const { db } = await connectToDatabase();
  // Use DbOrder type for the query
  const dbOrders = await db.collection<DbOrder>(Collections.ORDERS)
    .find({ userId: new ObjectId(userId) })
    .sort({ createdAt: -1 })
    .toArray();

  // Convert the results back to the Order type
  return dbOrders.map(mapDbOrderToOrder);
};

export const updateOrderStatus = async (orderId: string, status: OrderStatus) => {
  console.log(`[Model] updateOrderStatus called with orderId: ${orderId}, status: ${status}`);

  if (!ObjectId.isValid(orderId)) {
    console.log(`[Model] Invalid ObjectId format: ${orderId}`);
    return { modifiedCount: 0 }; // Or throw an error, depending on desired behavior
  }

  try {
    const { db } = await connectToDatabase();
    console.log(`[Model] Database connected, updating order ${orderId} to status ${status}`);

    // Prepare update document
    const updateDoc: any = {
      status,
      updatedAt: new Date()
    };

    // Add package information when status is PENDING_TRANSFER
    if (status === OrderStatus.PENDING_TRANSFER) {
      console.log(`[Model] Adding package information for PENDING_TRANSFER status`);

      // Set warehouse entry date to now
      updateDoc.warehouseEntryDate = new Date();

      // Set free storage period to 90 days
      updateDoc.freeStoragePeriod = 90;

      // Update each item with package information
      const order = await db.collection<DbOrder>(Collections.ORDERS).findOne({ _id: new ObjectId(orderId) });

      if (order && order.items) {
        // Create a new items array with package information
        const updatedItems = order.items.map(item => ({
          ...item,
          dimensions: '26*25*10cm',
          weight: 1.25, // kg
          volumeWeight: 1.35, // kg
          billableWeight: 1.49 // kg (max of weight and volumeWeight, with some handling fee)
        }));

        updateDoc.items = updatedItems;
      }
    }

    // Use DbOrder type for the update operation
    const result = await db.collection<DbOrder>(Collections.ORDERS).updateOne(
      { _id: new ObjectId(orderId) },
      { $set: updateDoc }
    );

    console.log(`[Model] Update result:`, {
      matchedCount: result.matchedCount,
      modifiedCount: result.modifiedCount,
      upsertedCount: result.upsertedCount,
      updateDoc
    });

    return { modifiedCount: result.modifiedCount }; // Return relevant info
  } catch (error) {
    console.error(`[Model] Error updating order status:`, error);
    throw error; // Re-throw to be caught by the API route handler
  }
};
