import { ObjectId } from 'mongodb';
import { connectToDatabase, Collections } from '@/lib/database';

// Interface for the product cache entry
export interface ProductCacheEntry {
  _id?: ObjectId;
  cacheKey: string;       // Unique key for the product (source:productId)
  data: any;              // The product data
  timestamp: number;      // When the cache entry was created/updated
  expiresAt: number;      // When the cache entry expires
  source: string;         // The source of the product (taobao or 1688)
  productId: string;      // The product ID
  createdAt: Date;        // When the cache entry was first created
  updatedAt: Date;        // When the cache entry was last updated
}

/**
 * Get a product from the cache
 * @param cacheKey The cache key (source:productId)
 * @returns The cached product data or null if not found or expired
 */
export const getProductFromCache = async (cacheKey: string): Promise<{ data: any, timestamp: number } | null> => {
  try {
    const { db } = await connectToDatabase();
    const now = Date.now();

    // Find a non-expired cache entry
    const cacheEntry = await db.collection<ProductCacheEntry>(Collections.PRODUCTS).findOne({
      cacheKey,
      expiresAt: { $gt: now }
    });

    if (!cacheEntry) {
      return null;
    }

    return {
      data: cacheEntry.data,
      timestamp: cacheEntry.timestamp
    };
  } catch (error) {
    console.error('Error getting product from cache:', error);
    return null;
  }
};

/**
 * Save a product to the cache
 * @param cacheKey The cache key (source:productId)
 * @param data The product data to cache
 * @param expirySeconds How long the cache should be valid in seconds
 * @returns True if the operation was successful
 */
export const saveProductToCache = async (
  cacheKey: string,
  data: any,
  expirySeconds: number
): Promise<boolean> => {
  try {
    const { db } = await connectToDatabase();
    const now = Date.now();
    const [source, productId] = cacheKey.split(':');

    // Prepare the cache entry
    const cacheEntry: Omit<ProductCacheEntry, '_id'> = {
      cacheKey,
      data,
      timestamp: now,
      expiresAt: now + (expirySeconds * 1000),
      source,
      productId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Upsert the cache entry (update if exists, insert if not)
    const result = await db.collection(Collections.PRODUCTS).updateOne(
      { cacheKey },
      { $set: cacheEntry },
      { upsert: true }
    );

    return result.acknowledged;
  } catch (error) {
    console.error('Error saving product to cache:', error);
    return false;
  }
};

/**
 * Clear expired cache entries
 * @returns Number of cleared entries
 */
export const clearExpiredCache = async (): Promise<number> => {
  try {
    const { db } = await connectToDatabase();
    const now = Date.now();

    const result = await db.collection(Collections.PRODUCTS).deleteMany({
      expiresAt: { $lt: now }
    });

    return result.deletedCount || 0;
  } catch (error) {
    console.error('Error clearing expired cache:', error);
    return 0;
  }
};
