"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface AdminUser {
  username: string;
  role: string;
  loginTime: number;
}

interface AdminAuthState {
  user: AdminUser | null;
  loading: boolean;
  authenticated: boolean;
}

export function useAdminAuth() {
  const router = useRouter();
  const [authState, setAuthState] = useState<AdminAuthState>({
    user: null,
    loading: true,
    authenticated: false
  });

  // Check admin session
  const checkSession = async () => {
    try {
      const response = await fetch('/api/admin/auth/session');
      const data = await response.json();
      
      setAuthState({
        user: data.user,
        loading: false,
        authenticated: data.authenticated
      });

      return data.authenticated;
    } catch (error) {
      console.error('Error checking admin session:', error);
      setAuthState({
        user: null,
        loading: false,
        authenticated: false
      });
      return false;
    }
  };

  // Login function
  const login = async (username: string, password: string) => {
    try {
      const response = await fetch('/api/admin/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (response.ok) {
        // Refresh session after successful login
        await checkSession();
        return { success: true };
      } else {
        return { success: false, error: data.error };
      }
    } catch (error) {
      return { success: false, error: 'Network error' };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await fetch('/api/admin/auth/logout', {
        method: 'POST',
      });
      
      setAuthState({
        user: null,
        loading: false,
        authenticated: false
      });

      router.push('/admin/login');
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  // Redirect to login if not authenticated
  const requireAuth = () => {
    if (!authState.loading && !authState.authenticated) {
      router.push('/admin/login');
    }
  };

  // Check session on mount
  useEffect(() => {
    checkSession();
  }, []);

  return {
    ...authState,
    login,
    logout,
    checkSession,
    requireAuth
  };
}
