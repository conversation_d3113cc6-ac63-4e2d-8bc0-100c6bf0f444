import { useTranslations as useNextIntlTranslations } from 'next-intl';

// This is a wrapper around next-intl's useTranslations hook with error handling
export function useTranslations(namespace: string) {
  try {
    return useNextIntlTranslations(namespace);
  } catch (error) {
    // If there's an error (like missing translations), return a function that returns the key
    console.warn(`Translation error for namespace "${namespace}":`, error);
    return (key: string, values?: Record<string, any>) => {
      // Return the key itself as a fallback
      return key;
    };
  }
}

export const I18NNamespace = {
  PAGES_PRODUCT: "pages_product",
  PAGES_CHECKOUT: "pages_checkout",
  PAGES_VERIFY_EMAIL: "pages_verify_email",
  PAGES_SIGNUP: "pages_signup",
  PAGES_LOGIN: "pages_login",
  PAGES_HOME: "pages_home",
  PAGES_MY_ORDERS: "pages_my_orders",
  PAGES_SUBSCRIPTION: "pages_subscription",
  PAGES_BLOG: "pages_blog",
  COMMON: "common",
  COMPONENTS_SIDEBAR: "components_sidebar",
  COMPONENTS_SHIPPING_COST_CALCULATOR: "components_shipping_cost_calculator",
  COMPONENTS_SERVICE_EXPLAN: "components_service_explan",
  COMPONENTS_SEARCHBOX: "components_searchbox",
  COMPONENTS_RATE_LIMIT_TOAST: "components_rate_limit_toast",
  COMPONENTS_PROCESS_STEPS: "components_process_steps",
  COMPONENTS_PARTNSHIP_SEC: "components_partnership_sec",
  COMPONENTS_FOOTER: "components_footer",
  COMPONENTS_ABOUT_SEC: "components_about_sec",
  COMPONENTS_CART_PANEL: "components_cart_panel",
  COMPONENTS_CATEGORY_TABS: "components_category_tabs",
  COMPONENTS_COMPARASION_CARD: "components_comparasion_card",
  COMPONENTS_FEATURE_CARDS: "components_feature_cards",
  COMPONENTS_SEARCH: "components_search",
  COMPONENTS_PRODUCTS: "components_products",
  COMPONENTS_COMPAREPANEL: "components_comparepanel",
  COMPONENTS_PRODUCT_IMAGE_GALLERY: "components_product_image_gallery",
  COMPONENTS_ORDER_DETAILS_POPUP: "components_order_details_popup",
  COMPONENTS_PRODUCT_DETAIL_MODAL: "components_product_detail_modal",
  COMPONENTS_PRODUCT_SUBMISSION_FORM: "components_product_submission_form",
  COMPONENTS_SHIPPING_CALCULATOR_MODAL: "components_shipping_calculator_modal",
  COMPONENTS_PRODUCT_DETAIL_VIEW: "components_product_detail_view",
  COMPONENTS_PRODUCT_GRID: "components_product_grid"
};
