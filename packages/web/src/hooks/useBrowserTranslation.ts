'use client';
import { useEffect } from 'react';
import { isChinese, useLanguage } from '@/lib/language-context';
import { TranslationsEngine } from '@/lib/translationEngine';
import { getTextsToTransate, setNestedValue } from '@/lib/translator';
import { getLocaleFromStorage } from '@/utils/storage';


let translationEngine: TranslationsEngine | null | Error = null;

export function translateByLocalEngine (targetlanguage: string, inputs: string[]): Promise<string[]> | null {
  if (!translationEngine)
    translationEngine = new TranslationsEngine("zh", targetlanguage);
  if (!translationEngine || translationEngine instanceof Error)
    return null;
  const engine = translationEngine;
  const terminate = () => {
    engine.terminate();
    translationEngine = null;
  }
  const execution = (): Promise<string[]> => new Promise((resolve, reject) => {
    const txts: string[] = [];
    let idx = 0;
    const execute = async () => {
      let translated = inputs[idx];
      try {
        translated = await engine.translate(inputs[idx], false);
      } catch (_) {}
      txts.push(translated as string)
      if (idx >= inputs.length - 1) {
        terminate();
        resolve(txts);
      } else {
        requestAnimationFrame(execute)
      }
      idx++
      if (idx > inputs.length) {
        terminate();
        resolve(txts);
      }
    }
    requestAnimationFrame(execute)
  });
  return translationEngine.isReadyPromise.then(() => execution())
}

export function useLoadBrowsTranslationEngine () {
  const { language } = useLanguage();
  useEffect(() => {
    if (typeof window === 'undefined') return; // Just run in client side
    setTimeout(() => {
      const lang = getLocaleFromStorage() || language;
      if (isChinese(lang)) return;
      if (translationEngine) return;
      try {
        translationEngine = new TranslationsEngine("zh", language);
        translationEngine.isReadyPromise
          .then(() => {
            if (translationEngine instanceof TranslationsEngine)
              translationEngine.terminate();
          })
          .finally(() => translationEngine = null);
      } catch (err: any) {
        console.error(err);
        translationEngine = new Error(err.toString());
      }
    }, 1500);
  }, [language]);
}

// Fields to translate in product detail
const _DETAIL_FIELDS_TO_TRANSLATE = [
  'item.title',
  'item.desc_short',
  'item.location',
  'item.props', // Product properties array
  'item.nick',
  'item.props_list', // All specification properties
  'item.skus.sku.[any].properties_name', // SKU properties names (including color and size)
];

// Hook for translating product detail using browser-based translation middleware
export async function productDetailTranslation(targetLanguage: string, productDetail: Record<string, any>): Promise<null | Record<string, any>> {
  // Extract fields that need translation
  const textsToTranslate = getTextsToTransate(productDetail, _DETAIL_FIELDS_TO_TRANSLATE);

  try {
    console.log(`[BrowserTranslation] Starting translation for ${textsToTranslate.length} texts`);

    // Create a deep copy of the product
    const translatedProductCopy = JSON.parse(JSON.stringify(productDetail));

    const inputTxts = textsToTranslate.map(it => it.value);

    const promise = translateByLocalEngine(targetLanguage, inputTxts);

    if (!promise) return null;
    const translated = await promise;

    // Apply translations to the product copy
    textsToTranslate.forEach((item, index) => {
      if (translated[index]) {
        setNestedValue(translatedProductCopy, item.key, translated[index]);
      }
    });

    return translatedProductCopy;
  } catch (error) {
    console.error(`[BrowserTranslation] Translation producr detail error:`, error);
    // Return the original product on error
    return null;
  }
}

