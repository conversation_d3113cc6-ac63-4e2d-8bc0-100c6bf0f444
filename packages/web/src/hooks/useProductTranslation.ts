'use client';

import { useState, useEffect, useRef } from 'react';
import { isChinese, useLanguage } from '@/lib/language-context';
import { translateProductList, translateProductDetail } from '@/lib/productTranslator';
import { productDetailTranslation, translateByLocalEngine } from './useBrowserTranslation';

// Global cache for product list translations
const productListCache = new Map<string, any[]>();

// 全局变量，用于跟踪正在进行的翻译
const ongoingTranslations = new Map<string, Promise<any>>();

// 全局变量，用于跟踪已经完成的翻译（作为额外的缓存层）
const completedTranslations = new Map<string, any>();

// Hook for translating product list
export function useProductListTranslation(products: any[], translated: boolean) {
  const { language } = useLanguage();
  const [translatedProducts, setTranslatedProducts] = useState<any[]>(products);
  const [isTranslating, setIsTranslating] = useState(false);
  const [lastTranslationHash, setLastTranslationHash] = useState<string | null>(null);

  // Use a ref to track if the component is mounted
  const isMountedRef = useRef(true);

  useEffect(() => {
    if (isChinese(language) || translated || !products || products.length === 0) {
      setTranslatedProducts(products);
      return;
    }

    // Create a hash of the product IDs to detect changes
    const productIds = products.map(p => p.num_iid || p.id || Math.random()).join(',');
    const translationKey = `${productIds}:${language}:${products.length}`;

    const translatedTexts = products.map((item: { title?: string }) => item.title?.toString() || '');
    const p = translateByLocalEngine(language, translatedTexts);
    if (p) {
      setIsTranslating(true);
      p.then(txts => {
        if (!txts) throw new Error();
        console.log("useProductListTranslation: local translation finished: ", txts);
        const translated = products.map((it, idx) => ({ ...it, title: txts[idx] }));
        setTranslatedProducts(translated);
        setIsTranslating(false)
      })
      .catch((err) => {
        console.error("useProductListTranslation: local translation failed: ", err);
        translateProducts();
      });
      return;
    }

    async function translateProducts() {
      // 检查是否有缓存的翻译结果
      if (productListCache.has(translationKey)) {
        const cachedProducts = productListCache.get(translationKey);
        if (cachedProducts && cachedProducts.length > 0) {
          setTranslatedProducts(cachedProducts);
          setLastTranslationHash(translationKey);
          return;
        }
      }

      // 检查是否有已完成的翻译
      if (completedTranslations.has(translationKey)) {
        const translated = completedTranslations.get(translationKey);
        if (translated && translated.length > 0) {
          productListCache.set(translationKey, translated);
          setTranslatedProducts(translated);
          setLastTranslationHash(translationKey);
          return;
        }
      }

      // 检查是否有正在进行的翻译
      if (ongoingTranslations.has(translationKey)) {
        // 等待翻译完成
        const translationPromise = ongoingTranslations.get(translationKey);
        if (translationPromise) {
          translationPromise
            .then(translated => {
              if (isMountedRef.current) {
                setTranslatedProducts(translated);
                setLastTranslationHash(translationKey);
              }
            })
            .catch(error => {
              console.error(`[ProductTranslation] Error in ongoing translation:`, error);
            });
        }
        return;
      }

      // Skip translation if we've already translated these products with this language
      if (translationKey === lastTranslationHash) {
        return;
      }


      // Create a flag to track if this effect instance is the most recent one
      const effectId = Date.now();
      (window as any).__latestProductListTranslationEffect = effectId;

      // 总是设置翻译状态，即使组件可能被卸载
      // 这确保了下一次挂载时能够知道翻译正在进行
      setIsTranslating(true);

      // 创建翻译Promise
      const translationPromise = (async () => {
        try {

          const translated = await translateProductList(products, language);

          // 存入全局缓存 - 使用更简单的键，便于后续查找
          const simpleKey = `${language}:${products.length}`;
          productListCache.set(translationKey, translated);
          productListCache.set(simpleKey, translated); // 额外使用简化的键

          // 存入已完成的翻译缓存
          completedTranslations.set(translationKey, translated);
          completedTranslations.set(simpleKey, translated); // 额外使用简化的键

          return translated;
        } catch (error) {
          console.error(`[ProductTranslation] Translation error:`, error);
          throw error;
        } finally {
          // 从正在进行的翻译中移除
          setTimeout(() => {
            ongoingTranslations.delete(translationKey);
          }, 1000);
        }
      })();

      // 存储翻译Promise
      ongoingTranslations.set(translationKey, translationPromise);

      try {
        // 等待翻译完成
        const translated = await translationPromise;

        // 再次检查组件是否已卸载和effect是否仍然有效
        const shouldUpdateState = isMountedRef.current;

        if (!shouldUpdateState) {
          return;
        }


        // 更新状态
        setTranslatedProducts(translated);
        setLastTranslationHash(translationKey);
      } catch (error) {
        console.error(`[ProductTranslation] Failed to translate products for request ${effectId}:`, error);
        // Fallback to original products
        if (isMountedRef.current) {
          setTranslatedProducts(products);
        }
      } finally {
        // 总是设置翻译状态为false，即使组件可能被卸载
        // 这确保了下一次挂载时能够知道翻译已经完成
        setIsTranslating(false);
      }

    }

    // 启动翻译过程
    translateProducts();
  }, [products, language, lastTranslationHash, translated]);

  return { translatedProducts, isTranslating };
}

// Global cache for product detail translations
const productDetailCache = new Map<string, Record<string, any>>();

// 全局变量，用于跟踪正在进行的产品详情翻译
const ongoingDetailTranslations = new Map<string, Promise<any>>();

// 全局变量，用于跟踪已经完成的产品详情翻译（作为额外的缓存层）
const completedDetailTranslations = new Map<string, Record<string, any>>();

// Hook for translating product detail
export function useProductDetailTranslation(productDetail: Record<string, any>) {
  const { language } = useLanguage();
  const [translatedProduct, setTranslatedProduct] = useState<Record<string, any> | null>(null);
  const [isTranslating, setIsTranslating] = useState(false);

  const startAPITransProduct = () => {
    // 创建翻译键
    const productId = productDetail.item?.num_iid || productDetail.item?.id || 'unknown';
    const translationKey = `${productId}:${language}`;

    // 检查是否有缓存的翻译结果
    if (productDetailCache.has(translationKey)) {
      const cachedProduct = productDetailCache.get(translationKey);
      if (cachedProduct && cachedProduct.item) {
        setTranslatedProduct(cachedProduct);
        setIsTranslating(false);
        return;
      }
    }

    // 检查是否有已完成的翻译
    if (completedDetailTranslations.has(translationKey)) {
      const translated = completedDetailTranslations.get(translationKey);
      if (translated && translated.item) {
        productDetailCache.set(translationKey, translated);
        setTranslatedProduct(translated);
        setIsTranslating(false);
        return;
      }
    }

    // 检查是否有正在进行的翻译
    if (ongoingDetailTranslations.has(translationKey)) {
      // 等待翻译完成
      const translationPromise = ongoingDetailTranslations.get(translationKey);
      if (translationPromise) {
        translationPromise
          .then(translated => {
            setTranslatedProduct(translated);
            setIsTranslating(false);
          })
          .catch(error => {
            console.error(`[ProductTranslation] Error in ongoing product detail translation:`, error);
          });
      }
      return;
    }

    // Create a flag to track if this effect instance is the most recent one
    const effectId = Date.now();
    (window as any).__latestProductDetailTranslationEffect = effectId;

    async function translateProduct() {

      // 创建翻译键，用于缓存和去重
      const translationKey = `${productId}:${language}`;  
      setIsTranslating(true);

      // 创建翻译Promise
      const translationPromise = (async () => {
        try {

          const translated = await translateProductDetail(productDetail, language);
          // 存入全局缓存
          productDetailCache.set(translationKey, translated);

          // 存入已完成的翻译缓存
          completedDetailTranslations.set(translationKey, translated);

          return translated;
        } catch (error) {
          console.error(`[ProductTranslation] Product detail translation error:`, error);
          throw error;
        } finally {
          // 从正在进行的翻译中移除
          setTimeout(() => {
            ongoingDetailTranslations.delete(translationKey);
          }, 1000);
        }
      })();

      // 存储翻译Promise
      ongoingDetailTranslations.set(translationKey, translationPromise);

      try {
        // 等待翻译完成
        const translated = await translationPromise;

        // 存入全局缓存（无论组件是否已卸载）
        productDetailCache.set(translationKey, translated);

        // 更新状态
        setTranslatedProduct(translated);
      } catch (error) {
        console.error(`[ProductTranslation] Failed to translate product details for request ${effectId}:`, error);
        setTranslatedProduct(productDetail);
      } finally {
        setIsTranslating(false);
      }
    }

    // 启动翻译过程
    translateProduct();
  }

  // 在组件挂载时立即检查缓存
  useEffect(() => {
    if (!productDetail) {
      return;
    }

    // 如果是中文，直接使用原始数据，但要检查是否已经设置过
    if (isChinese(language)) {
      setTranslatedProduct(productDetail);
      setIsTranslating(false);
      return;
    }

    setIsTranslating(true);

    productDetailTranslation(language, productDetail)
      .then((translated) => {
        console.log("Translation from local translator: ", translated);
        setTranslatedProduct(translated);
        setIsTranslating(false);
      })
      .catch(startAPITransProduct);
  }, [language]);

  return { translatedProduct, isTranslating };
}
