import prohibitedProducts from '@/data/prohibited-products.json';

export interface ValidationResult {
  isProhibited: boolean;
  matchedKeyword?: string;
  matchedCategory?: string;
  message?: string;
  confidence: number;
  requiresReview: boolean;
}

/**
 * Checks if a product name contains prohibited keywords
 * @param productName The product name to check
 * @returns ValidationResult object with validation details
 */
export function validateProduct(productName: string): ValidationResult {
  if (!productName) {
    return { 
      isProhibited: false, 
      confidence: 0,
      requiresReview: false
    };
  }

  const normalizedName = prohibitedProducts.rules.caseSensitive 
    ? productName 
    : productName.toLowerCase();
  
  // Check for prohibited keywords
  for (const category of prohibitedProducts.categories) {
    for (const keyword of category.keywords) {
      const normalizedKeyword = prohibitedProducts.rules.caseSensitive 
        ? keyword 
        : keyword.toLowerCase();
      
      if (normalizedName.includes(normalizedKeyword)) {
        // Check if this matches any exception patterns
        const isException = checkExceptions(normalizedName);
        
        if (isException) {
          return {
            isProhibited: false,
            matchedKeyword: keyword,
            matchedCategory: category.name,
            message: prohibitedProducts.messages.review,
            confidence: 0.5,
            requiresReview: true
          };
        }
        
        return {
          isProhibited: true,
          matchedKeyword: keyword,
          matchedCategory: category.name,
          message: prohibitedProducts.messages.rejected.replace('{keyword}', keyword),
          confidence: 1.0,
          requiresReview: false
        };
      }
    }
  }

  return { 
    isProhibited: false, 
    confidence: 0,
    requiresReview: false
  };
}

/**
 * Checks if the product name matches any exception patterns
 * @param normalizedName The normalized product name
 * @returns boolean indicating if an exception was found
 */
function checkExceptions(normalizedName: string): boolean {
  for (const exception of prohibitedProducts.exceptions) {
    const exceptionRegex = new RegExp(exception.pattern, 'i');
    if (exceptionRegex.test(normalizedName)) {
      return true;
    }
  }
  return false;
}

/**
 * Batch validates multiple product names
 * @param productNames Array of product names to validate
 * @returns Array of ValidationResult objects
 */
export function batchValidateProducts(productNames: string[]): ValidationResult[] {
  return productNames.map(name => validateProduct(name));
}
