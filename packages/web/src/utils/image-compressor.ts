/**
 * Utility functions for compressing images
 */

import imageCompression from 'browser-image-compression';

export interface CompressedImage {
  file: File;
  originalSize: number;
  compressedSize: number;
  originalWidth: number;
  originalHeight: number;
  compressedWidth: number;
  compressedHeight: number;
  wasCompressed: boolean;
}

/**
 * Compresses an image to meet size and dimension requirements
 * @param file The original image file
 * @param maxSizeMB Maximum file size in MB (default: 2)
 * @param maxWidthOrHeight Maximum width or height in pixels (default: 600)
 * @returns Promise with compressed image result
 */
export async function compressImage(
  file: File,
  maxSizeMB: number = 2,
  maxWidthOrHeight: number = 600
): Promise<CompressedImage> {
  // Get original image dimensions
  const originalDimensions = await getImageDimensions(file);
  
  // Check if compression is needed
  const needsCompression = 
    file.size > maxSizeMB * 1024 * 1024 || 
    originalDimensions.width > maxWidthOrHeight || 
    originalDimensions.height > maxWidthOrHeight;
  
  // If no compression needed, return original with metadata
  if (!needsCompression) {
    return {
      file,
      originalSize: file.size,
      compressedSize: file.size,
      originalWidth: originalDimensions.width,
      originalHeight: originalDimensions.height,
      compressedWidth: originalDimensions.width,
      compressedHeight: originalDimensions.height,
      wasCompressed: false
    };
  }
  
  // Compression options
  const options = {
    maxSizeMB,
    maxWidthOrHeight,
    useWebWorker: true,
    fileType: file.type as any, // Preserve original file type
  };
  
  try {
    // Compress the image
    const compressedFile = await imageCompression(file, options);
    
    // Get dimensions of compressed image
    const compressedDimensions = await getImageDimensions(compressedFile);
    
    return {
      file: compressedFile,
      originalSize: file.size,
      compressedSize: compressedFile.size,
      originalWidth: originalDimensions.width,
      originalHeight: originalDimensions.height,
      compressedWidth: compressedDimensions.width,
      compressedHeight: compressedDimensions.height,
      wasCompressed: true
    };
  } catch (error) {
    console.error('Error compressing image:', error);
    // If compression fails, return original file
    return {
      file,
      originalSize: file.size,
      compressedSize: file.size,
      originalWidth: originalDimensions.width,
      originalHeight: originalDimensions.height,
      compressedWidth: originalDimensions.width,
      compressedHeight: originalDimensions.height,
      wasCompressed: false
    };
  }
}

/**
 * Gets the dimensions of an image file
 * @param file The image file
 * @returns Promise with width and height
 */
function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height
      });
    };
    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };
    img.src = URL.createObjectURL(file);
  });
}
