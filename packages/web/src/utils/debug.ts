/**
 * Debug logging utility
 * Only logs in development environment
 */

/**
 * Log debug messages to the console in development environment
 * @param message The message to log
 * @param optionalParams Additional parameters to log
 */


/**
 * Log warning messages to the console in development environment
 * @param message The warning message to log
 * @param optionalParams Additional parameters to log
 */
export function debugWarn(message: string, ...optionalParams: any[]): void {
  // Only log in development environment
  if (process.env.NODE_ENV === 'development') {
    console.warn(`[WARNING] ${message}`, ...optionalParams);
  }
}

/**
 * Log error messages to the console in development environment
 * @param message The error message to log
 * @param optionalParams Additional parameters to log
 */
export function debugError(message: string, ...optionalParams: any[]): void {
  // Only log in development environment
  if (process.env.NODE_ENV === 'development') {
    console.error(`[ERROR] ${message}`, ...optionalParams);
  }
}

// Helper function to replace debugLog calls
export function debugLog (...args: any[]) {
  // Production code should not log debug messages
  if (process.env.NODE_ENV === 'development') {
    console.log(...args);
  }
};

