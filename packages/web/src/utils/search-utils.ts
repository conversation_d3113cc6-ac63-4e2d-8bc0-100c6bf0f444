/**
 * Utility functions for search operations
 */

/**
 * Extracts display data from Taobao API response for storing in the database
 * This reduces the size of stored data by keeping only what's needed for display
 */
export function extractDisplayData(apiResponse: any): any[] {
  if (!apiResponse || !apiResponse.items || !apiResponse.items.item || !Array.isArray(apiResponse.items.item)) {
    return [];
  }

  // Extract only the fields needed for display (up to 20 items)
  return apiResponse.items.item.slice(0, 20).map((item: any) => ({
    id: item.num_iid || String(Math.random()),
    title: item.title || 'Unknown product',
    price: item.price || '0.00',
    image: item.pic_url || '',
    detailUrl: item.detail_url || '#'
  }));
}

/**
 * Extracts the original query from the search parameters
 */
export function extractOriginalQuery(searchParams: URLSearchParams): string {
  return searchParams.get('originalQuery') || searchParams.get('q') || '';
}

/**
 * Extracts the AI model used from the search parameters
 */
export function extractAiModel(searchParams: URLSearchParams): string | null {
  return searchParams.get('model') || null;
}
