import { BlogPost } from '@/types/blog';

// Default language if none is specified
const DEFAULT_LANGUAGE = 'en';

/**
 * Loads a blog post from the API based on the slug and language
 * @param slug The slug of the blog post
 * @param lang The language code (en, fr, zh)
 * @returns The blog post or null if not found
 */
export async function getBlogPostBySlug(slug: string, lang: string = DEFAULT_LANGUAGE): Promise<BlogPost | null> {
  try {
    // Fetch the blog post from the API
    const response = await fetch(`/api/blog/post/${slug}?lang=${lang}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch blog post: ${response.statusText}`);
    }

    // Parse the response as JSON
    const post = await response.json();

    // Convert date strings to Date objects
    return {
      ...post,
      createdAt: new Date(post.createdAt),
      updatedAt: new Date(post.updatedAt),
      publishedAt: new Date(post.publishedAt),
    };
  } catch (error) {
    console.error(`Error loading blog post ${slug} in language ${lang}:`, error);
    return null;
  }
}

/**
 * Gets all blog posts for a specific language from the API
 * @param lang The language code (en, fr, zh)
 * @returns Array of blog posts
 */
export async function getAllBlogPosts(lang: string = DEFAULT_LANGUAGE): Promise<BlogPost[]> {
  try {
    // Fetch all blog posts from the API
    const response = await fetch(`/api/blog/posts?lang=${lang}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch blog posts: ${response.statusText}`);
    }

    // Parse the response as JSON
    const posts = await response.json();

    // Convert date strings to Date objects
    return posts.map((post: any) => ({
      ...post,
      createdAt: new Date(post.createdAt),
      updatedAt: new Date(post.updatedAt),
      publishedAt: new Date(post.publishedAt),
    }));
  } catch (error) {
    console.error(`Error loading blog posts for language ${lang}:`, error);
    return [];
  }
}
