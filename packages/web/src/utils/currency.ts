import adminConfig from "../../adminConfig.mjs";

/**
 * Convert CNY (Yuan) to EUR (Euro)
 * @param amount Amount in CNY
 * @returns Amount in EUR
 */
export function convertCNYtoEUR(amount: number): number {
  const rate = adminConfig.currency.exchangeRates.CNY_EUR;
  return amount * rate;
}

/**
 * Format a currency amount according to the specified currency
 * @param amount The amount to format
 * @param currency The currency code ('CNY' or 'EUR')
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, currency: 'CNY' | 'EUR' = 'EUR'): string {
  const config = adminConfig.currency.display.format[currency];
  
  // Format the number with the specified decimal places
  const formattedNumber = amount.toFixed(config.decimalPlaces);
  
  // Add the currency symbol in the correct position
  if (config.position === 'before') {
    return `${config.symbol}${formattedNumber}`;
  } else {
    return `${formattedNumber}${config.symbol}`;
  }
}

/**
 * Format a price from CNY to the display currency based on admin config
 * @param cnyAmount Amount in CNY
 * @returns Formatted price string according to display settings
 */
export function formatPrice(cnyAmount: number): string {
  const { showOnlyCNY, showOnlyEUR, showBoth } = adminConfig.currency.display;
  
  if (showOnlyEUR) {
    const eurAmount = convertCNYtoEUR(cnyAmount);
    return formatCurrency(eurAmount, 'EUR');
  } else if (showOnlyCNY) {
    return formatCurrency(cnyAmount, 'CNY');
  } else if (showBoth) {
    const eurAmount = convertCNYtoEUR(cnyAmount);
    return `${formatCurrency(eurAmount, 'EUR')} (${formatCurrency(cnyAmount, 'CNY')})`;
  }
  
  // Default to EUR if no display preference is set
  const eurAmount = convertCNYtoEUR(cnyAmount);
  return formatCurrency(eurAmount, 'EUR');
}
