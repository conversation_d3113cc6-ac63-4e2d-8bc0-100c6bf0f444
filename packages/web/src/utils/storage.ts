'use client';
import { defaultLocale, localeNames } from "@/i18n";
import { debugError } from "./debug";
import { Locale } from "next-intl";

const LocaleLocalStorageKey = "__miccobuy_loale_key";


export function setLocaleInStorage (locale: string) {
  if (!(locale in localeNames)) {
    debugError("the locale is not supported");
    return;
  }
  localStorage.setItem(LocaleLocalStorageKey, locale);
}

export function getLocaleFromStorage (returnDefault = true) {
  const locale = localStorage.getItem(LocaleLocalStorageKey);
  if (locale) return locale as Locale;
  if (returnDefault)
    return defaultLocale;
  return null;
}