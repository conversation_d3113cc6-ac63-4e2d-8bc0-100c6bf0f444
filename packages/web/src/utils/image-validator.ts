/**
 * Utility functions for validating image files
 */

export interface ImageValidationResult {
  isValid: boolean;
  message?: string;
  error?: 'format' | 'dimensions' | 'size';
}

/**
 * Validates an image file based on format only
 * Note: Size and dimension restrictions have been removed since we now compress images automatically
 * @param file The image file to validate
 * @returns Promise with validation result
 */
export async function validateImage(file: File): Promise<ImageValidationResult> {
  // Check file format - this is the only validation we still need
  const validFormats = ['image/png', 'image/jpeg', 'image/jpg'];
  if (!validFormats.includes(file.type)) {
    return {
      isValid: false,
      message: 'Invalid image format. Please use PNG or JPEG/JPG.',
      error: 'format'
    };
  }

  // All checks passed
  return { isValid: true };
}

/**
 * Gets the dimensions of an image file
 * @param file The image file
 * @returns Promise with width and height
 */
function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height
      });
    };
    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };
    img.src = URL.createObjectURL(file);
  });
}
