import {getRequestConfig} from 'next-intl/server';
import { debugError } from './utils/debug';

export const locales = ['en', 'zh', 'fr'];

export const localeNames = {
  en: 'English',
  zh: '中文',
  fr: 'Français',
};

export const localeCurrencies = {
  en: 'USD',
  zh: 'CNY',
  fr: 'EUR',
};

export const defaultLocale = 'en';

type Locale = keyof typeof localeNames;

export function getLocalCurrencyTxt (locale: Locale) {
  return `${localeNames[locale]} - ${localeCurrencies[locale]}`;
}

export function setLocaleInCookie (cookie: string) {
  document.cookie = `NEXT_LOCALE=${cookie}; path=/; max-age=${60 * 60 * 24 * 7}`;
}

export default getRequestConfig(async ({requestLocale}) => {
  const locale = requestLocale?.toString();
  const messages = (await import(`./messages/${locale}.json`)).default;
  return {
    locale: locale,
    messages,
  };
});
