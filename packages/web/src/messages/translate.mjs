import { readFile, writeFile } from 'node:fs/promises';

const supportedLanguages = {
  zh: "Chinese simplified",
  zhTW: "traditional Chinese",
  fr: "French",
}

const supportedKeys = Object.keys(supportedLanguages);

const lang = process.argv[2];

if (!lang || !supportedKeys.includes(lang))
  throw Error("Language not selected, please select: " + supportedKeys.join(','));
const language = supportedLanguages[lang];

async function translate(keys) {
  const input = keys.map((t, index) => `${index}|${t}`).join('\n');
  const url = "https://api.deepbricks.ai/v1/chat/completions";
  const content = `
You need to translate such english phrases into ${language}. DO NOT RETURN ANY OTHER INFORMATION.
Input odrmat:
id|text
0|home
Output format:
id|text
0|<translated_result>

Input:
id|text
${input}

Ok, let's start.
Output:
id|text
  `;
  const body = {
    "model": "gpt-4o-mini",
    "messages": [
      { "role": "system", "content": "You are an experienced professional translator. Be careful and concise." },
      {
        "role": "user",
        "content": content
      }
    ],
    "temperature": .01
  };
  const response = await fetch(url, {
    method: "POST",
    headers: {
      "Authorization": "Bearer sk-U1xd2xtsSSm1yMCJVRMtTFh2C0wjcSgdH52BV0UdahA8HfO0",
      "accept": "application/json",
      "content-type": "application/json",
    },
    body: JSON.stringify(body),
  }).then(res => res.json());
  const ret = response.choices[0].message.content.toString();
  const m = {};
  ret.split('\n').filter(t => t.length).filter(t => !isNaN(t[0])).map(t => {
    const [idx, txt] = t.split('|');
    return m[idx] = txt.trim();
  });
  return m;
}

async function execute () {
  const raw = await readFile('packages/web/src/messages/en.json').then(t => t.toString());
  const en = JSON.parse(raw);
  const config = Object.entries(en).map(([k, v]) => ([k, Object.entries(v)]));
  const resultMap = {};
  for (const [key, texts] of config) {
    console.log("processing " + key);
    const translated = await translate(texts.map(arr => arr[1]));
    const map = {};
    texts.forEach((ss, idx) =>{
      const key = ss[0];
      map[key] = translated[idx] || '';
    });
    resultMap[key] = map;
  }
  const writePath = `packages/web/src/messages/${lang}.json`;
  console.log("Translation completed. Start writing to: " + writePath);
  await writeFile(writePath, JSON.stringify(resultMap, null, 2));
}

execute();
