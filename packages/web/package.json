{"name": "web", "version": "0.2.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.798.0", "@aws-sdk/s3-request-presigner": "^3.798.0", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.0", "@stripe/stripe-js": "^7.0.0", "@types/micro": "^7.3.7", "@types/mongodb": "^4.0.6", "@types/nodemailer": "^6.4.17", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.8", "@wisp-cms/client": "^0.0.23", "@workos-inc/authkit-nextjs": "^2.3.1", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "framer-motion": "^12.6.3", "jose": "^6.0.11", "lucide-react": "^0.487.0", "micro": "^10.0.1", "mongodb": "^6.15.0", "next": "^15.2.0", "next-intl": "^4.1.0", "nodemailer": "^6.10.1", "openai": "^4.95.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "stripe": "^18.0.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20.17.30", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}