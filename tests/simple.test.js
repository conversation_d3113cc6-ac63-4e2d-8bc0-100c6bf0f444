/**
 * Simple tests for Chrome extension
 */

describe('Chrome Extension Tests', () => {
  test('Basic test to verify Jest is working', () => {
    expect(1 + 1).toBe(2);
  });

  describe('Background script functionality', () => {
    test('Should handle image capture requests', () => {
      // Mock implementation
      const handleCaptureRequest = (rect, callback) => {
        // In the real background.js, this would capture the tab and process the image
        const imageData = 'data:image/png;base64,mockImageData';
        callback({ imageData });
      };

      // Test the mock function
      const mockCallback = jest.fn();
      handleCaptureRequest(
        { left: 10, top: 20, width: 100, height: 200 },
        mockCallback
      );

      // Verify callback was called with expected data
      expect(mockCallback).toHaveBeenCalledWith({
        imageData: 'data:image/png;base64,mockImageData'
      });
    });
  });

  describe('Popup script functionality', () => {
    test('Should process server response', () => {
      // Mock implementation of the function that processes server response
      const processServerResponse = (data) => {
        if (data.productUrl) {
          return {
            success: true,
            url: data.productUrl,
            message: '找到相似商品!'
          };
        } else {
          return {
            success: false,
            message: '未找到相似商品。'
          };
        }
      };

      // Test with successful response
      const successResult = processServerResponse({
        productUrl: 'https://example.com/product'
      });
      expect(successResult.success).toBe(true);
      expect(successResult.url).toBe('https://example.com/product');
      expect(successResult.message).toBe('找到相似商品!');

      // Test with unsuccessful response
      const failResult = processServerResponse({});
      expect(failResult.success).toBe(false);
      expect(failResult.message).toBe('未找到相似商品。');
    });
  });

  describe('Content script functionality', () => {
    test('Should calculate selection rectangle', () => {
      // Mock implementation of rectangle calculation
      const calculateRect = (startX, startY, endX, endY) => {
        const left = Math.min(startX, endX);
        const top = Math.min(startY, endY);
        const width = Math.abs(endX - startX);
        const height = Math.abs(endY - startY);
        return { left, top, width, height };
      };

      // Test with different coordinates
      const rect1 = calculateRect(10, 20, 110, 220);
      expect(rect1).toEqual({
        left: 10,
        top: 20,
        width: 100,
        height: 200
      });

      // Test with reversed coordinates
      const rect2 = calculateRect(110, 220, 10, 20);
      expect(rect2).toEqual({
        left: 10,
        top: 20,
        width: 100,
        height: 200
      });
    });
  });
});
