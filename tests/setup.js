// Mock Chrome API
global.chrome = {
  runtime: {
    onMessage: {
      addListener: jest.fn(),
      callListeners: function(message, sender, callback) {
        this.addListener.mock.calls.forEach(call => {
          const listener = call[0];
          listener(message, sender, callback);
        });
      }
    },
    sendMessage: jest.fn(),
    lastError: undefined
  },
  storage: {
    local: {
      set: jest.fn((data, callback) => callback && callback())
    }
  },
  action: {
    openPopup: jest.fn()
  },
  tabs: {
    query: jest.fn(),
    sendMessage: jest.fn(),
    captureVisibleTab: jest.fn()
  }
};

// Mock DOM for content.test.js
document.body = document.createElement('body');
document.head = document.createElement('head');
