/**
 * Tests for background.js
 */

// Mock Image and Canvas
global.Image = class {
  constructor() {
    setTimeout(() => {
      if (this.onload) this.onload();
    }, 0);
  }
};

// Mock document.createElement for canvas
const originalCreateElement = document.createElement;
document.createElement = function(tagName) {
  if (tagName === 'canvas') {
    return {
      getContext: jest.fn(() => ({
        drawImage: jest.fn(),
      })),
      toDataURL: jest.fn(() => 'data:image/png;base64,mockImageData'),
      width: 0,
      height: 0
    };
  }
  return originalCreateElement.call(document, tagName);
};

// Setup response mock
const mockSendResponse = jest.fn();

// Import the background script
require('../background.js');

describe('Background Script', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('capturedImage message handler', () => {
    test('should store image data and open popup', () => {
      // Trigger the message listener
      chrome.runtime.onMessage.callListeners(
        { action: 'capturedImage', imageData: 'mockImageData' },
        {},
        mockSendResponse
      );

      // Check if storage was called with correct data
      expect(mockChromeStorageLocalSet).toHaveBeenCalledWith(
        { capturedImage: 'mockImageData' },
        expect.any(Function)
      );

      // Check if popup was opened
      expect(mockChromeActionOpenPopup).toHaveBeenCalled();

      // Check if response was sent
      expect(mockSendResponse).toHaveBeenCalledWith({ status: 'success' });

      // Wait for the timeout to complete
      jest.advanceTimersByTime(500);

      // Check if message was sent to popup
      expect(mockChromeRuntimeSendMessage).toHaveBeenCalledWith({
        action: 'captureComplete',
        imageData: 'mockImageData'
      });
    });
  });

  describe('requestCapture message handler', () => {
    test('should capture tab and process image', () => {
      // Setup mock for captureVisibleTab
      mockChromeTabsCaptureVisibleTab.mockImplementation((_, __, callback) => {
        callback('mockDataUrl');
      });

      // Trigger the message listener
      chrome.runtime.onMessage.callListeners(
        {
          action: 'requestCapture',
          rect: { left: 10, top: 20, width: 100, height: 200 }
        },
        {},
        mockSendResponse
      );

      // Check if captureVisibleTab was called
      expect(mockChromeTabsCaptureVisibleTab).toHaveBeenCalled();

      // Check if response was sent with processed image data
      expect(mockSendResponse).toHaveBeenCalledWith({
        imageData: 'data:image/png;base64,mockImageData'
      });
    });

    test('should handle error in captureVisibleTab', () => {
      // Setup mock for captureVisibleTab with error
      chrome.runtime.lastError = { message: 'Mock error' };
      mockChromeTabsCaptureVisibleTab.mockImplementation((_, __, callback) => {
        callback(null);
      });

      // Trigger the message listener
      chrome.runtime.onMessage.callListeners(
        {
          action: 'requestCapture',
          rect: { left: 10, top: 20, width: 100, height: 200 }
        },
        {},
        mockSendResponse
      );

      // Check if response was sent with error
      expect(mockSendResponse).toHaveBeenCalledWith({
        error: 'Mock error'
      });

      // Clean up
      chrome.runtime.lastError = undefined;
    });
  });
});
