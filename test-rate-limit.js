/**
 * Test script for rate limiting functionality
 *
 * This script makes repeated requests to the rate-limited endpoints
 * to test if the rate limiting is working correctly.
 *
 * Usage:
 * 1. Start the development server: npm run dev
 * 2. Run this script: npm run test:rate-limit
 *
 * Note: This script tests the search endpoint with different queries.
 */

import fetch from 'node-fetch';
import adminConfig from './packages/web/adminConfig.mjs';

// Get rate limit configuration
const { limits } = adminConfig.rateLimiting;

// Configuration
const BASE_URL = 'http://localhost:3000';
const SEARCH_ENDPOINT = '/api/search'; // Regular search endpoint
const TOTAL_REQUESTS = 20; // Number of requests to make
const DELAY_MS = 200; // Delay between requests in milliseconds

// Test search endpoint with different queries
async function testSearch() {
  console.log(`\n=== Testing Search Rate Limiting (${SEARCH_ENDPOINT}) ===`);
  console.log(`Making ${TOTAL_REQUESTS} requests (hourly limit: ${limits.hourly}, daily limit: ${limits.daily})`);
  console.log('---------------------------------------------------');

  for (let i = 0; i < TOTAL_REQUESTS; i++) {
    try {
      // For HEAD requests (to check limits without consuming them)
      const headResponse = await fetch(`${BASE_URL}${SEARCH_ENDPOINT}`, {
        method: 'HEAD',
      });

      const remaining = {
        hourly: headResponse.headers.get('X-RateLimit-Remaining-Hour'),
        daily: headResponse.headers.get('X-RateLimit-Remaining-Day'),
      };

      // For GET requests (to actually consume the rate limit)
      const response = await fetch(`${BASE_URL}${SEARCH_ENDPOINT}?q=test+query+${i}`, {
        method: 'GET',
      });

      const status = response.status;
      let message = '';

      if (status === 429) {
        try {
          const data = await response.json();
          message = data.error;
        } catch (e) {
          message = 'Rate limit exceeded';
        }
      }

      console.log(`Request ${i + 1}/${TOTAL_REQUESTS} - Status: ${status} - Remaining: Hourly=${remaining.hourly}, Daily=${remaining.daily}${message ? ' - ' + message : ''}`);

      if (status === 429) {
        console.log('Rate limit exceeded! Test successful.');
        break;
      }
    } catch (error) {
      console.error(`Error on request ${i + 1}:`, error.message);
    }

    // Add a small delay between requests
    await new Promise(resolve => setTimeout(resolve, DELAY_MS));
  }
}

// Run the test
testSearch();
