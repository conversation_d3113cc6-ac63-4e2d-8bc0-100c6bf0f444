[images]
  remote_images = ["https://same-assets.com"]

[build]
  command = "bun run build"
  publish = "out"

[build.environment]
  NODE_VERSION = "18"
  NEXT_USE_NETLIFY_EDGE = "true"

[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = false
  minify = false

[build.processing.js]
  bundle = false
  minify = false

[build.processing.html]
  pretty_urls = true

[build.processing.images]
  compress = true

[functions]
  node_bundler = "esbuild"

[[plugins]]
  package = "@netlify/plugin-nextjs"

[dev]
  command = "bun run dev -H 0.0.0.0"
  port = 3000
  targetPort = 3000
  framework = "next"
